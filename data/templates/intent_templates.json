{"ADD_COLUMN": {"template": {"intentType": "ADD_COLUMN", "targetConceptName": "{entity}", "props": {"name": "{field_name}", "stdSqlType": "{data_type}", "nullable": "{nullable}", "defaultValue": "{default_value}", "comment": "{comment}"}}, "entities": ["客户", "订单", "产品", "用户", "供应商", "员工", "部门", "项目"], "field_names": ["等级", "状态", "类型", "编号", "名称", "描述", "备注", "创建时间", "更新时间", "价格", "数量", "总额"], "data_types": ["VARCHAR(50)", "INT", "DECIMAL(10,2)", "DATETIME", "TEXT", "BOOLEAN", "BIGINT"], "expressions": ["给{entity}表添加一个{field_name}字段", "为{entity}增加{field_name}属性", "{entity}需要一个{field_name}字段", "在{entity}表中加入{field_name}", "添加{field_name}到{entity}表", "{entity}表要有{field_name}字段", "给{entity}加个{field_name}", "新增{entity}的{field_name}属性"], "fuzzy_expressions": ["给{entity}加字段", "{entity}要加个字段", "添加字段到{entity}", "新增字段"]}, "DELETE_COLUMN": {"template": {"intentType": "DELETE_COLUMN", "targetConceptName": "{entity}", "props": {"name": "{field_name}"}}, "entities": ["客户", "订单", "产品", "用户", "供应商", "员工", "部门", "项目"], "field_names": ["等级", "状态", "类型", "编号", "名称", "描述", "备注", "创建时间", "更新时间", "价格", "数量", "总额"], "expressions": ["删除{entity}表的{field_name}字段", "移除{entity}的{field_name}属性", "{entity}不需要{field_name}字段了", "去掉{entity}表中的{field_name}", "把{entity}的{field_name}删了", "取消{entity}的{field_name}字段"], "fuzzy_expressions": ["删除{entity}的字段", "移除{entity}字段", "{entity}删字段"]}, "MODIFY_COLUMN": {"template": {"intentType": "MODIFY_COLUMN", "targetConceptName": "{entity}", "props": {"name": "{field_name}", "newName": "{new_field_name}", "stdSqlType": "{data_type}", "nullable": "{nullable}"}}, "entities": ["客户", "订单", "产品", "用户", "供应商", "员工", "部门", "项目"], "field_names": ["等级", "状态", "类型", "编号", "名称", "描述", "备注"], "new_field_names": ["客户等级", "订单状态", "产品类型", "用户编号", "客户名称"], "data_types": ["VARCHAR(100)", "INT", "DECIMAL(15,2)", "TEXT"], "expressions": ["修改{entity}表的{field_name}字段类型为{data_type}", "将{entity}的{field_name}改为{new_field_name}", "更新{entity}表{field_name}字段", "{entity}的{field_name}字段要改成{data_type}类型"]}, "ENABLE_SOFT_DELETE": {"template": {"intentType": "ENABLE_SOFT_DELETE", "targetConceptName": "{entity}", "props": {"deleteField": "is_deleted", "deleteFieldType": "BOOLEAN"}}, "entities": ["客户", "订单", "产品", "用户", "供应商", "员工", "部门", "项目"], "expressions": ["启用{entity}表的逻辑删除", "{entity}表需要软删除功能", "给{entity}添加逻辑删除", "{entity}要支持软删除", "开启{entity}的逻辑删除"]}}