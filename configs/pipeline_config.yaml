# 数据生成Pipeline配置

# Pipeline模式选择
pipeline:
  mode: "hybrid"  # basic, enhanced, hybrid
  enable_logging: true
  save_intermediate: true

# 基础生成模式配置
basic_mode:
  samples_per_intent: 500
  enable_variants: true
  variant_ratio: 0.5

# 增强生成模式配置（专家建议）
enhanced_mode:
  seed_samples_per_intent: 3
  evolution_generations: 3
  variants_per_generation: 3
  diversity_threshold: 0.8
  enable_llm_enhancement: false

# 数据增强配置
augmentation:
  adversarial_ratio: 0.1
  boundary_cases: 100
  negative_samples: 100
  enable_noise_injection: true

# 质量控制配置
quality_control:
  enable_deduplication: true
  similarity_threshold: 0.85
  min_instruction_length: 5
  max_instruction_length: 200
  enable_quality_scoring: true

# 输出配置
output:
  save_format: ["json", "csv"]
  split_by_intent: true
  train_val_split: 0.8
  output_dir: "./data/processed"
  
# 监控配置
monitoring:
  enable_metrics: true
  log_generation_stats: true
  save_quality_report: true
