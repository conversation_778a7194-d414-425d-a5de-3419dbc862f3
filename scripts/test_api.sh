#!/bin/bash

echo "🧪 开始测试API接口..."

# 等待服务启动
sleep 2

echo "1. 测试健康检查接口..."
curl -s http://127.0.0.1:8000/health | jq .

echo -e "\n2. 测试根路径..."
curl -s http://127.0.0.1:8000/ | jq .

echo -e "\n3. 测试解析功能（添加字段）..."
curl -s -X POST "http://127.0.0.1:8000/parse" \
  -H "Content-Type: application/json" \
  -d '{"text": "给客户表添加一个等级字段"}' | jq .

echo -e "\n4. 测试解析功能（删除字段）..."
curl -s -X POST "http://127.0.0.1:8000/parse" \
  -H "Content-Type: application/json" \
  -d '{"text": "删除订单表的备注字段"}' | jq .

echo -e "\n5. 测试澄清功能..."
curl -s -X POST "http://127.0.0.1:8000/parse" \
  -H "Content-Type: application/json" \
  -d '{"text": "加字段"}' | jq .

echo -e "\n6. 获取示例..."
curl -s http://127.0.0.1:8000/parse/examples | jq .

echo -e "\n7. 获取配置信息..."
curl -s http://127.0.0.1:8000/config | jq .

echo -e "\nAPI测试完成！"