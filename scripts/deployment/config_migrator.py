#!/usr/bin/env python3
"""
配置迁移和验证工具
将现有配置转换为distilabel兼容格式，并验证配置完整性
"""

import yaml
import json
import argparse
from pathlib import Path
from typing import Dict, Any, List, Optional
from datetime import datetime

class ConfigMigrator:
    """配置迁移器 - 遵循KISS原则的简单转换工具"""
    
    def __init__(self):
        self.supported_models = [
            "Qwen/Qwen2.5-7B-Instruct",
            "Qwen/Qwen2.5-4B-Instruct", 
            "Qwen/Qwen3-4B-Instruct"
        ]
    
    def migrate_pipeline_config(self, source_config_path: str, 
                               target_config_path: str) -> bool:
        """迁移现有pipeline配置到distilabel格式"""
        try:
            # 读取源配置
            with open(source_config_path, 'r', encoding='utf-8') as f:
                source_config = yaml.safe_load(f)
            
            # 创建distilabel配置结构
            distilabel_config = self._create_distilabel_config(source_config)
            
            # 保存目标配置
            with open(target_config_path, 'w', encoding='utf-8') as f:
                yaml.dump(distilabel_config, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
            
            print(f"✅ 配置迁移完成: {source_config_path} -> {target_config_path}")
            return True
            
        except Exception as e:
            print(f"❌ 配置迁移失败: {e}")
            return False
    
    def _create_distilabel_config(self, source_config: Dict[str, Any]) -> Dict[str, Any]:
        """创建distilabel兼容的配置结构"""
        
        # 基础配置迁移
        distilabel_config = {
            "pipeline": {
                "mode": "distilabel",
                "enable_logging": source_config.get("monitoring", {}).get("enable_metrics", True),
                "save_intermediate": source_config.get("output", {}).get("save_quality_report", True),
                "use_cache": False
            }
        }
        
        # distilabel特定配置
        distilabel_config["distilabel"] = {
            "pipeline_name": "ConfigDrivenIntentGeneration",
            "batch_size": 32,
            "num_workers": 4,
            "model": self._migrate_model_config(source_config)
        }
        
        # 增强模式配置
        enhanced_mode = source_config.get("enhanced_mode", {})
        distilabel_config["enhanced_mode"] = {
            "seed_samples_per_intent": enhanced_mode.get("seed_samples_per_intent", 100),
            "evolution_generations": enhanced_mode.get("evolution_generations", 2),
            "variants_per_generation": enhanced_mode.get("variants_per_generation", 3),
            "diversity_threshold": enhanced_mode.get("diversity_threshold", 0.8),
            "enable_llm_enhancement": enhanced_mode.get("enable_llm_enhancement", True),
            "quality_filter": {
                "min_consistency_score": 0.7,
                "min_quality_score": 0.5,
                "enable_duplicate_detection": True
            }
        }
        
        # 数据增强配置
        augmentation = source_config.get("augmentation", {})
        distilabel_config["generation_strategy"] = {
            "semantic_matrix": {
                "atomic": 35, "composite": 30, "sequence": 20, "implicit": 15
            },
            "expression_matrix": {
                "formal": 40, "casual": 35, "technical": 25
            },
            "robustness_matrix": {
                "adversarial_ratio": augmentation.get("adversarial_ratio", 0.1),
                "boundary_cases": 0.05,
                "negative_samples": 0.05
            }
        }
        
        # 质量控制配置
        quality_control = source_config.get("quality_control", {})
        distilabel_config["quality_control"] = {
            "enable_consistency_check": True,
            "consistency_threshold": 0.7,
            "enable_semantic_validation": True,
            "enable_structure_validation": True,
            "deduplication": {
                "similarity_threshold": quality_control.get("similarity_threshold", 0.85),
                "use_semantic_similarity": True,
                "use_exact_match": True
            }
        }
        
        # 输出配置
        output = source_config.get("output", {})
        distilabel_config["output"] = {
            "save_format": output.get("save_format", ["json"]) + ["parquet"],
            "output_dir": output.get("output_dir", "./data/processed/distilabel"),
            "filename_prefix": "intent_dataset",
            "train_ratio": 0.8,
            "val_ratio": 0.1,
            "test_ratio": 0.1,
            "save_metadata": True,
            "save_quality_report": True,
            "save_pipeline_config": True
        }
        
        # 监控配置
        monitoring = source_config.get("monitoring", {})
        distilabel_config["monitoring"] = {
            "enable_step_timing": True,
            "enable_memory_monitoring": True,
            "log_sample_examples": monitoring.get("log_generation_stats", True),
            "sample_log_count": 10,
            "quality_thresholds": {
                "min_samples_per_intent": 50,
                "max_consistency_score_std": 0.3,
                "min_avg_quality_score": 0.6
            }
        }
        
        # 错误处理配置
        distilabel_config["error_handling"] = {
            "max_retries": 3,
            "retry_delay": 2.0,
            "skip_failed_samples": True,
            "log_failed_samples": True,
            "fallback_strategy": {
                "enable_rule_based_fallback": True,
                "enable_template_fallback": True
            }
        }
        
        return distilabel_config
    
    def _migrate_model_config(self, source_config: Dict[str, Any]) -> Dict[str, Any]:
        """迁移模型配置"""
        model_config = source_config.get("model", {})
        
        # 确定模型名称
        base_model = model_config.get("base_model", "Qwen/Qwen2.5-7B-Instruct")
        if base_model not in self.supported_models:
            print(f"⚠️  模型 {base_model} 可能不被支持，使用默认模型")
            base_model = "Qwen/Qwen2.5-7B-Instruct"
        
        return {
            "name": base_model,
            "type": "transformers",
            "generation_kwargs": {
                "max_new_tokens": model_config.get("max_length", 512),
                "temperature": model_config.get("temperature", 0.7),
                "do_sample": model_config.get("do_sample", True),
                "top_p": model_config.get("top_p", 0.9)
            },
            "fallback_models": [
                "Qwen/Qwen2.5-4B-Instruct",
                "microsoft/DialoGPT-medium"
            ]
        }


class ConfigValidator:
    """配置验证器 - 确保配置文件完整性和正确性"""
    
    def __init__(self):
        self.required_sections = [
            "pipeline", "distilabel", "enhanced_mode", 
            "quality_control", "output"
        ]
        self.required_distilabel_keys = [
            "model", "pipeline_name"
        ]
    
    def validate_config(self, config_path: str) -> tuple[bool, List[str]]:
        """验证配置文件，返回验证结果和错误列表"""
        errors = []
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 检查必需节
            errors.extend(self._check_required_sections(config))
            
            # 检查distilabel特定配置
            errors.extend(self._check_distilabel_config(config))
            
            # 检查模型配置
            errors.extend(self._check_model_config(config))
            
            # 检查数据路径
            errors.extend(self._check_paths(config))
            
            # 检查数值范围
            errors.extend(self._check_value_ranges(config))
            
            return len(errors) == 0, errors
            
        except Exception as e:
            errors.append(f"配置文件读取失败: {e}")
            return False, errors
    
    def _check_required_sections(self, config: Dict[str, Any]) -> List[str]:
        """检查必需的配置节"""
        errors = []
        for section in self.required_sections:
            if section not in config:
                errors.append(f"缺少必需配置节: {section}")
        return errors
    
    def _check_distilabel_config(self, config: Dict[str, Any]) -> List[str]:
        """检查distilabel特定配置"""
        errors = []
        distilabel_config = config.get("distilabel", {})
        
        for key in self.required_distilabel_keys:
            if key not in distilabel_config:
                errors.append(f"distilabel配置缺少必需键: {key}")
        
        # 检查batch_size
        batch_size = distilabel_config.get("batch_size", 32)
        if not isinstance(batch_size, int) or batch_size <= 0:
            errors.append("batch_size必须是正整数")
        
        return errors
    
    def _check_model_config(self, config: Dict[str, Any]) -> List[str]:
        """检查模型配置"""
        errors = []
        model_config = config.get("distilabel", {}).get("model", {})
        
        if "name" not in model_config:
            errors.append("模型配置缺少name字段")
        
        if "generation_kwargs" not in model_config:
            errors.append("模型配置缺少generation_kwargs")
        else:
            gen_kwargs = model_config["generation_kwargs"]
            if "max_new_tokens" not in gen_kwargs:
                errors.append("generation_kwargs缺少max_new_tokens")
        
        return errors
    
    def _check_paths(self, config: Dict[str, Any]) -> List[str]:
        """检查路径配置"""
        errors = []
        output_config = config.get("output", {})
        
        output_dir = output_config.get("output_dir")
        if output_dir:
            try:
                Path(output_dir).parent.mkdir(parents=True, exist_ok=True)
            except Exception as e:
                errors.append(f"输出目录创建失败: {e}")
        
        return errors
    
    def _check_value_ranges(self, config: Dict[str, Any]) -> List[str]:
        """检查数值范围"""
        errors = []
        
        # 检查enhanced_mode参数
        enhanced_mode = config.get("enhanced_mode", {})
        
        seed_samples = enhanced_mode.get("seed_samples_per_intent", 100)
        if not isinstance(seed_samples, int) or seed_samples <= 0:
            errors.append("seed_samples_per_intent必须是正整数")
        
        diversity_threshold = enhanced_mode.get("diversity_threshold", 0.8)
        if not isinstance(diversity_threshold, (int, float)) or not 0 < diversity_threshold <= 1:
            errors.append("diversity_threshold必须在(0,1]范围内")
        
        # 检查质量控制参数
        quality_control = config.get("quality_control", {})
        consistency_threshold = quality_control.get("consistency_threshold", 0.7)
        if not isinstance(consistency_threshold, (int, float)) or not 0 < consistency_threshold <= 1:
            errors.append("consistency_threshold必须在(0,1]范围内")
        
        return errors


def create_sample_config(output_path: str):
    """创建示例配置文件"""
    sample_config = {
        "pipeline": {
            "mode": "distilabel",
            "enable_logging": True,
            "save_intermediate": True,
            "use_cache": False
        },
        "distilabel": {
            "pipeline_name": "ConfigDrivenIntentGeneration",
            "batch_size": 32,
            "num_workers": 4,
            "model": {
                "name": "Qwen/Qwen2.5-7B-Instruct",
                "type": "transformers",
                "generation_kwargs": {
                    "max_new_tokens": 512,
                    "temperature": 0.7,
                    "do_sample": True,
                    "top_p": 0.9
                }
            }
        },
        "enhanced_mode": {
            "seed_samples_per_intent": 100,
            "evolution_generations": 2,
            "variants_per_generation": 3,
            "diversity_threshold": 0.8,
            "enable_llm_enhancement": True
        },
        "quality_control": {
            "enable_consistency_check": True,
            "consistency_threshold": 0.7,
            "enable_semantic_validation": True
        },
        "output": {
            "save_format": ["json", "parquet"],
            "output_dir": "./data/processed/distilabel",
            "save_metadata": True
        }
    }
    
    with open(output_path, 'w', encoding='utf-8') as f:
        yaml.dump(sample_config, f, default_flow_style=False, allow_unicode=True, indent=2)
    
    print(f"✅ 示例配置文件已创建: {output_path}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="配置迁移和验证工具")
    parser.add_argument("--migrate", nargs=2, metavar=("SOURCE", "TARGET"),
                       help="迁移配置文件: --migrate source.yaml target.yaml")
    parser.add_argument("--validate", metavar="CONFIG",
                       help="验证配置文件: --validate config.yaml")
    parser.add_argument("--create-sample", metavar="OUTPUT",
                       help="创建示例配置: --create-sample sample_config.yaml")
    
    args = parser.parse_args()
    
    if args.migrate:
        source_path, target_path = args.migrate
        migrator = ConfigMigrator()
        success = migrator.migrate_pipeline_config(source_path, target_path)
        
        if success:
            print("🔄 开始验证迁移后的配置...")
            validator = ConfigValidator()
            is_valid, errors = validator.validate_config(target_path)
            
            if is_valid:
                print("✅ 迁移的配置文件验证通过")
            else:
                print("⚠️  迁移的配置文件有以下问题:")
                for error in errors:
                    print(f"  - {error}")
    
    elif args.validate:
        validator = ConfigValidator()
        is_valid, errors = validator.validate_config(args.validate)
        
        if is_valid:
            print("✅ 配置文件验证通过")
        else:
            print("❌ 配置文件验证失败:")
            for error in errors:
                print(f"  - {error}")
    
    elif args.create_sample:
        create_sample_config(args.create_sample)
    
    else:
        parser.print_help()


if __name__ == "__main__":
    main()