# scripts/data_generation/convert_to_sft.py
#!/usr/bin/env python3
"""
SFT格式转换脚本 - 将数据转换为Huggingface训练格式
"""

import json
from pathlib import Path
from typing import List, Dict, Any

class SFTConverter:
    """SFT格式转换器"""
    
    def __init__(self):
        self.system_prompt = """你是一个专业的数据建模助手。请将用户的自然语言指令解析为标准的JSON意图格式。"""
    
    def convert_to_sft(self, input_path: str, output_path: str) -> int:
        """转换为SFT训练格式"""
        # 读取原始数据
        with open(input_path, 'r', encoding='utf-8') as f:
            data = [json.loads(line) for line in f]
        
        # 转换格式
        sft_data = []
        for item in data:
            sft_item = self._convert_item(item)
            if sft_item:
                sft_data.append(sft_item)
        
        # 保存SFT格式数据
        with open(output_path, 'w', encoding='utf-8') as f:
            for item in sft_data:
                f.write(json.dumps(item, ensure_ascii=False) + '\n')
        
        return len(sft_data)
    
    def _convert_item(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """转换单个样本为SFT格式"""
        try:
            # 构建输入文本
            instruction = item["instruction"]
            
            # 构建输出JSON
            intent_json = json.dumps(item["intent"], ensure_ascii=False, indent=2)
            
            # SFT格式：instruction + output
            return {
                "instruction": instruction,
                "output": intent_json
            }
        except Exception:
            return None

if __name__ == "__main__":
    converter = SFTConverter()
    
    # 转换所有分割的数据
    for split in ["train", "val", "test"]:
        input_path = f"data/training/splits/{split}.jsonl"
        output_path = f"data/training/sft/{split}.jsonl"
        
        # 确保输出目录存在
        Path(output_path).parent.mkdir(parents=True, exist_ok=True)
        
        count = converter.convert_to_sft(input_path, output_path)
        print(f"✅ {split}.jsonl 转换完成: {count} 样本")