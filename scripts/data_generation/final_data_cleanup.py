# scripts/data_generation/final_data_cleanup.py
#!/usr/bin/env python3
"""
最终数据清洗脚本 - 处理distilabel输出的训练数据
"""

import json
import pandas as pd
from pathlib import Path
from typing import List, Dict, Any
import re

class DataCleaner:
    """简单高效的数据清洗器"""
    
    def __init__(self):
        self.valid_intent_types = {
            "ADD_COLUMN", "DELETE_COLUMN", "MODIFY_COLUMN", 
            "ENABLE_SOFT_DELETE", "DELETE_RELATION", "UPDATE_ENTITY"
        }
        self.valid_sql_types = {
            "INT", "VARCHAR", "TEXT", "DECIMAL", "BOOLEAN", 
            "DATETIME", "DATE", "FLOAT"
        }
    
    def clean_dataset(self, input_path: str, output_path: str) -> Dict[str, int]:
        """清洗数据集并返回统计信息"""
        # 读取数据
        with open(input_path, 'r', encoding='utf-8') as f:
            raw_data = [json.loads(line) for line in f]
        
        # 清洗过程
        cleaned_data = []
        stats = {"total": len(raw_data), "valid": 0, "dropped": 0}
        
        for item in raw_data:
            if self._is_valid_sample(item):
                cleaned_item = self._clean_sample(item)
                if cleaned_item:
                    cleaned_data.append(cleaned_item)
                    stats["valid"] += 1
                else:
                    stats["dropped"] += 1
            else:
                stats["dropped"] += 1
        
        # 保存清洗后的数据
        with open(output_path, 'w', encoding='utf-8') as f:
            for item in cleaned_data:
                f.write(json.dumps(item, ensure_ascii=False) + '\n')
        
        return stats
    
    def _is_valid_sample(self, sample: Dict[str, Any]) -> bool:
        """验证样本是否有效"""
        # 必须包含instruction和intent字段
        if not all(key in sample for key in ["instruction", "intent_json"]):
            return False
        
        # instruction不能为空
        if not sample["instruction"] or len(sample["instruction"].strip()) < 5:
            return False
        
        # intent_json必须是有效的JSON
        try:
            intent_data = json.loads(sample["intent_json"]) if isinstance(sample["intent_json"], str) else sample["intent_json"]
            if not isinstance(intent_data, list) or len(intent_data) == 0:
                return False
        except:
            return False
        
        return True
    
    def _clean_sample(self, sample: Dict[str, Any]) -> Dict[str, Any]:
        """清洗单个样本"""
        try:
            # 标准化instruction
            instruction = sample["instruction"].strip()
            instruction = re.sub(r'\s+', ' ', instruction)  # 规范化空格
            
            # 解析和验证intent
            intent_data = json.loads(sample["intent_json"]) if isinstance(sample["intent_json"], str) else sample["intent_json"]
            
            # 验证intent结构
            valid_intents = []
            for intent in intent_data:
                if self._validate_intent(intent):
                    valid_intents.append(self._normalize_intent(intent))
            
            if not valid_intents:
                return None
            
            return {
                "instruction": instruction,
                "intent": valid_intents
            }
        except Exception:
            return None
    
    def _validate_intent(self, intent: Dict[str, Any]) -> bool:
        """验证单个intent的有效性"""
        # 检查必要字段
        if not all(key in intent for key in ["intentType", "targetConceptName"]):
            return False
        
        # 检查intentType是否有效
        if intent["intentType"] not in self.valid_intent_types:
            return False
        
        # 根据intentType验证props
        if "props" in intent and intent["props"]:
            return self._validate_props(intent["intentType"], intent["props"])
        
        return True
    
    def _validate_props(self, intent_type: str, props: Dict[str, Any]) -> bool:
        """验证props字段"""
        if intent_type == "ADD_COLUMN":
            if "name" not in props or "stdSqlType" not in props:
                return False
            # 标准化SQL类型
            sql_type = props["stdSqlType"].upper()
            if sql_type not in self.valid_sql_types:
                return False
        
        return True
    
    def _normalize_intent(self, intent: Dict[str, Any]) -> Dict[str, Any]:
        """标准化intent格式"""
        normalized = {
            "intentType": intent["intentType"],
            "targetConceptName": intent["targetConceptName"]
        }
        
        if "props" in intent and intent["props"]:
            normalized["props"] = intent["props"]
            # 标准化SQL类型
            if "stdSqlType" in normalized["props"]:
                normalized["props"]["stdSqlType"] = normalized["props"]["stdSqlType"].upper()
        
        return normalized

if __name__ == "__main__":
    cleaner = DataCleaner()
    
    # 处理distilabel输出
    input_path = "data/processed/distilabel_output/default.jsonl"
    output_path = "data/training/cleaned_dataset.jsonl"
    
    stats = cleaner.clean_dataset(input_path, output_path)
    
    print(f"🧹 数据清洗完成:")
    print(f"   原始样本: {stats['total']}")
    print(f"   有效样本: {stats['valid']}")
    print(f"   丢弃样本: {stats['dropped']}")
    print(f"   清洗后数据: {output_path}")