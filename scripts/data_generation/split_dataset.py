# scripts/data_generation/split_dataset.py
#!/usr/bin/env python3
"""
数据分割脚本 - 将清洗后的数据按比例分割为训练/验证/测试集
"""

import json
import random
from pathlib import Path
from typing import List, Dict, Any, Tuple

class DatasetSplitter:
    """简单的数据集分割器"""
    
    def __init__(self, train_ratio: float = 0.8, val_ratio: float = 0.1, test_ratio: float = 0.1):
        """初始化分割比例"""
        assert abs(train_ratio + val_ratio + test_ratio - 1.0) < 1e-6, "比例之和必须为1"
        self.train_ratio = train_ratio
        self.val_ratio = val_ratio
        self.test_ratio = test_ratio
    
    def split_dataset(self, input_path: str, output_dir: str, seed: int = 42) -> Dict[str, int]:
        """分割数据集"""
        # 读取数据
        with open(input_path, 'r', encoding='utf-8') as f:
            data = [json.loads(line) for line in f]
        
        # 随机打乱
        random.seed(seed)
        random.shuffle(data)
        
        # 计算分割点
        total_size = len(data)
        train_size = int(total_size * self.train_ratio)
        val_size = int(total_size * self.val_ratio)
        
        # 分割数据
        train_data = data[:train_size]
        val_data = data[train_size:train_size + val_size]
        test_data = data[train_size + val_size:]
        
        # 保存分割后的数据
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        self._save_split(train_data, output_dir / "train.jsonl")
        self._save_split(val_data, output_dir / "val.jsonl")
        self._save_split(test_data, output_dir / "test.jsonl")
        
        return {
            "train": len(train_data),
            "val": len(val_data),
            "test": len(test_data),
            "total": total_size
        }
    
    def _save_split(self, data: List[Dict], output_path: Path):
        """保存分割数据"""
        with open(output_path, 'w', encoding='utf-8') as f:
            for item in data:
                f.write(json.dumps(item, ensure_ascii=False) + '\n')

if __name__ == "__main__":
    splitter = DatasetSplitter(train_ratio=0.8, val_ratio=0.1, test_ratio=0.1)
    
    input_path = "data/training/cleaned_dataset.jsonl"
    output_dir = "data/training/splits"
    
    stats = splitter.split_dataset(input_path, output_dir)
    
    print(f"📊 数据集分割完成:")
    print(f"   训练集: {stats['train']} 样本")
    print(f"   验证集: {stats['val']} 样本")
    print(f"   测试集: {stats['test']} 样本")
    print(f"   总计: {stats['total']} 样本")