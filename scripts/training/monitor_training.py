# scripts/training/monitor_training.py
#!/usr/bin/env python3
"""
训练监控脚本 - 实时监控训练过程
"""

import json
import time
import matplotlib.pyplot as plt
from pathlib import Path
from typing import Dict, List

class TrainingMonitor:
    """训练过程监控器"""
    
    def __init__(self, log_dir: str):
        self.log_dir = Path(log_dir)
        self.metrics_history = {
            "train_loss": [],
            "eval_loss": [],
            "learning_rate": [],
            "steps": []
        }
    
    def parse_training_logs(self) -> Dict[str, List]:
        """解析训练日志"""
        trainer_state_file = self.log_dir / "trainer_state.json"
        
        if not trainer_state_file.exists():
            return self.metrics_history
        
        with open(trainer_state_file, 'r') as f:
            trainer_state = json.load(f)
        
        # 提取关键指标
        for entry in trainer_state.get("log_history", []):
            if "train_loss" in entry:
                self.metrics_history["train_loss"].append(entry["train_loss"])
                self.metrics_history["steps"].append(entry["step"])
            
            if "eval_loss" in entry:
                self.metrics_history["eval_loss"].append(entry["eval_loss"])
            
            if "learning_rate" in entry:
                self.metrics_history["learning_rate"].append(entry["learning_rate"])
        
        return self.metrics_history
    
    def plot_metrics(self, save_path: str = None):
        """绘制训练指标"""
        metrics = self.parse_training_logs()
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('Training Metrics', fontsize=16)
        
        # Loss curves
        if metrics["train_loss"]:
            axes[0, 0].plot(metrics["train_loss"], label="Train Loss")
            axes[0, 0].set_title("Training Loss")
            axes[0, 0].set_xlabel("Steps")
            axes[0, 0].set_ylabel("Loss")
            axes[0, 0].legend()
        
        # Validation Loss
        if metrics["eval_loss"]:
            axes[0, 1].plot(metrics["eval_loss"], label="Validation Loss", color="orange")
            axes[0, 1].set_title("Validation Loss")
            axes[0, 1].set_xlabel("Steps")
            axes[0, 1].set_ylabel("Loss")
            axes[0, 1].legend()
        
        # Learning Rate
        if metrics["learning_rate"]:
            axes[1, 0].plot(metrics["learning_rate"], label="Learning Rate", color="green")
            axes[1, 0].set_title("Learning Rate")
            axes[1, 0].set_xlabel("Steps")
            axes[1, 0].set_ylabel("LR")
            axes[1, 0].legend()
        
        # Combined Loss
        if metrics["train_loss"] and metrics["eval_loss"]:
            steps = range(len(metrics["train_loss"]))
            axes[1, 1].plot(steps, metrics["train_loss"], label="Train Loss")
            
            # 对齐验证损失的步数
            eval_steps = [i for i in range(0, len(metrics["train_loss"]), 10)][:len(metrics["eval_loss"])]
            axes[1, 1].plot(eval_steps, metrics["eval_loss"], label="Validation Loss")
            
            axes[1, 1].set_title("Loss Comparison")
            axes[1, 1].set_xlabel("Steps")
            axes[1, 1].set_ylabel("Loss")
            axes[1, 1].legend()
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"📊 训练图表已保存: {save_path}")
        
        plt.show()
    
    def check_training_health(self) -> Dict[str, str]:
        """检查训练健康状态"""
        metrics = self.parse_training_logs()
        health_report = {}
        
        # 检查训练损失趋势
        if len(metrics["train_loss"]) > 10:
            recent_loss = metrics["train_loss"][-5:]
            early_loss = metrics["train_loss"][:5]
            
            if sum(recent_loss) / len(recent_loss) < sum(early_loss) / len(early_loss):
                health_report["train_loss"] = "✅ 训练损失正常下降"
            else:
                health_report["train_loss"] = "⚠️ 训练损失可能停滞"
        
        # 检查过拟合
        if len(metrics["eval_loss"]) > 3:
            recent_val_loss = metrics["eval_loss"][-3:]
            if len(set(recent_val_loss)) == 1 or recent_val_loss[-1] > recent_val_loss[0]:
                health_report["overfitting"] = "⚠️ 可能存在过拟合"
            else:
                health_report["overfitting"] = "✅ 验证损失正常"
        
        return health_report

if __name__ == "__main__":
    monitor = TrainingMonitor("models/adapters/intent_parser_v1")
    
    while True:
        print("📊 生成训练监控报告...")
        
        # 绘制指标图
        monitor.plot_metrics("logs/training_metrics.png")