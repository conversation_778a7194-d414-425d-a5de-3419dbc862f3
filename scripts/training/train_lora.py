# scripts/training/train_lora.py
#!/usr/bin/env python3
"""
LoRA微调脚本 - 基于PEFT库实现Qwen模型微调
"""

import os
import json
import yaml
from pathlib import Path
from dataclasses import dataclass, field
from typing import Optional

import torch
from transformers import (
    AutoTokenizer, AutoModelForCausalLM, 
    TrainingArguments, Trainer, DataCollatorForSeq2Seq
)
from peft import LoraConfig, get_peft_model, TaskType
from datasets import Dataset

@dataclass
class ModelArguments:
    """模型相关参数"""
    model_name_or_path: str = field(default="Qwen/Qwen2.5-7B-Instruct")
    cache_dir: Optional[str] = field(default=None)
    use_auth_token: bool = field(default=False)

@dataclass 
class DataArguments:
    """数据相关参数"""
    train_file: str = field(default="data/training/sft/train.jsonl")
    validation_file: str = field(default="data/training/sft/val.jsonl")
    max_seq_length: int = field(default=2048)

@dataclass
class LoraArguments:
    """LoRA相关参数"""
    lora_r: int = field(default=16)
    lora_alpha: int = field(default=32)
    lora_dropout: float = field(default=0.1)
    target_modules: str = field(default="q_proj,k_proj,v_proj,o_proj")

class IntentTrainer:
    """意图解析模型训练器"""
    
    def __init__(self, model_args: ModelArguments, data_args: DataArguments, lora_args: LoraArguments):
        self.model_args = model_args
        self.data_args = data_args
        self.lora_args = lora_args
        
        # 初始化tokenizer
        self.tokenizer = AutoTokenizer.from_pretrained(
            model_args.model_name_or_path,
            trust_remote_code=True,
            cache_dir=model_args.cache_dir
        )
        
        # 添加pad_token
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
    
    def load_model(self):
        """加载并配置模型"""
        # 加载基础模型
        model = AutoModelForCausalLM.from_pretrained(
            self.model_args.model_name_or_path,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True,
            cache_dir=self.model_args.cache_dir
        )
        
        # 配置LoRA
        lora_config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            inference_mode=False,
            r=self.lora_args.lora_r,
            lora_alpha=self.lora_args.lora_alpha,
            lora_dropout=self.lora_args.lora_dropout,
            target_modules=self.lora_args.target_modules.split(",")
        )
        
        # 应用LoRA
        model = get_peft_model(model, lora_config)
        model.print_trainable_parameters()
        
        return model
    
    def load_dataset(self):
        """加载并预处理数据集"""
        # 读取训练数据
        train_data = self._read_jsonl(self.data_args.train_file)
        val_data = self._read_jsonl(self.data_args.validation_file)
        
        # 转换为Dataset
        train_dataset = Dataset.from_list(train_data)
        val_dataset = Dataset.from_list(val_data)
        
        # 预处理
        train_dataset = train_dataset.map(self._preprocess_function, batched=True)
        val_dataset = val_dataset.map(self._preprocess_function, batched=True)
        
        return train_dataset, val_dataset
    
    def _read_jsonl(self, file_path: str):
        """读取JSONL文件"""
        data = []
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                data.append(json.loads(line))
        return data
    
    def _preprocess_function(self, examples):
        """数据预处理函数"""
        inputs = []
        targets = []
        
        for instruction, output in zip(examples["instruction"], examples["output"]):
            # 构建输入文本
            input_text = f"Instruction: {instruction}\nOutput: "
            
            # 构建完整文本 (input + target)
            full_text = input_text + output
            
            inputs.append(input_text)
            targets.append(full_text)
        
        # Tokenize
        model_inputs = self.tokenizer(
            targets,
            max_length=self.data_args.max_seq_length,
            truncation=True,
            padding=False
        )
        
        # 计算labels (只对output部分计算loss)
        input_ids = model_inputs["input_ids"]
        labels = []
        
        for i, input_text in enumerate(inputs):
            input_tokens = self.tokenizer(input_text, add_special_tokens=False)["input_ids"]
            input_len = len(input_tokens)
            
            # 创建labels，input部分用-100忽略
            label = [-100] * input_len + input_ids[i][input_len:]
            labels.append(label)
        
        model_inputs["labels"] = labels
        return model_inputs
    
    def train(self, output_dir: str):
        """执行训练"""
        # 加载模型和数据
        model = self.load_model()
        train_dataset, val_dataset = self.load_dataset()
        
        # 训练参数
        training_args = TrainingArguments(
            output_dir=output_dir,
            per_device_train_batch_size=4,
            per_device_eval_batch_size=4,
            gradient_accumulation_steps=4,
            num_train_epochs=3,
            learning_rate=5e-5,
            warmup_steps=100,
            logging_steps=10,
            eval_steps=100,
            save_steps=500,
            evaluation_strategy="steps",
            save_strategy="steps",
            load_best_model_at_end=True,
            metric_for_best_model="eval_loss",
            greater_is_better=False,
            dataloader_pin_memory=False,
            fp16=True,
            report_to=None  # 禁用wandb等
        )
        
        # 数据整理器
        data_collator = DataCollatorForSeq2Seq(
            tokenizer=self.tokenizer,
            model=model,
            label_pad_token_id=-100
        )
        
        # 创建训练器
        trainer = Trainer(
            model=model,
            args=training_args,
            train_dataset=train_dataset,
            eval_dataset=val_dataset,
            tokenizer=self.tokenizer,
            data_collator=data_collator
        )
        
        # 开始训练
        print("🚀 开始LoRA微调...")
        trainer.train()
        
        # 保存最终模型
        trainer.save_model()
        print(f"✅ 模型训练完成，保存至: {output_dir}")
        
        return trainer

if __name__ == "__main__":
    # 配置参数
    model_args = ModelArguments(
        model_name_or_path="Qwen/Qwen2.5-7B-Instruct"
    )
    
    data_args = DataArguments(
        train_file="data/training/sft/train.jsonl",
        validation_file="data/training/sft/val.jsonl",
        max_seq_length=2048
    )
    
    lora_args = LoraArguments(
        lora_r=16,
        lora_alpha=32,
        lora_dropout=0.1,
        target_modules="q_proj,k_proj,v_proj,o_proj"
    )
    
    # 创建训练器并开始训练
    trainer = IntentTrainer(model_args, data_args, lora_args)
    output_dir = "models/adapters/intent_parser_v1"
    
    trainer.train(output_dir)