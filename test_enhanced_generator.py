"""
测试增强数据生成器
"""

import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.enhanced_generator import EnhancedDataGenerator

async def test_enhanced_generator():
    """测试增强数据生成器"""
    print("开始测试增强数据生成器...")
    
    try:
        # 初始化增强生成器
        generator = EnhancedDataGenerator()
        
        # 测试seed生成
        print("\n测试seed样本生成...")
        seeds = generator.generate_seed_samples("ADD_COLUMN", seed_count=3)
        
        print(f"生成了 {len(seeds)} 个seed样本")
        for i, seed in enumerate(seeds):
            print(f"\nSeed {i+1}:")
            print(f"  指令: {seed.instruction}")
            print(f"  质量分数: {seed.metadata.get('quality_score', 'N/A'):.3f}")
            print(f"  是否为seed: {seed.metadata.get('is_seed', False)}")
        
        # 测试演化生成
        print("\n测试演化样本生成...")
        evolved_samples = generator.evolve_samples(
            seeds, 
            generations=2, 
            variants_per_generation=2
        )
        
        print(f"演化完成，总样本数: {len(evolved_samples)}")
        
        # 显示演化结果
        generation_counts = {}
        for sample in evolved_samples:
            gen = sample.metadata.get('generation', 0)
            generation_counts[gen] = generation_counts.get(gen, 0) + 1
        
        print(f"各代样本分布: {generation_counts}")
        
        # 显示一些变体样本
        variants = [s for s in evolved_samples if s.metadata.get('is_variant', False)]
        print(f"\n变体样本示例 (共{len(variants)}个):")
        
        for i, variant in enumerate(variants[:3]):
            print(f"\n变体 {i+1}:")
            print(f"  原始: {variant.metadata.get('parent_instruction', 'N/A')}")
            print(f"  变体: {variant.instruction}")
            print(f"  策略: {variant.metadata.get('applied_strategies', [])}")
            print(f"  代数: {variant.metadata.get('generation', 'N/A')}")
        
        # 获取生成指标
        metrics = generator.get_generation_metrics()
        print(f"\n生成指标:")
        print(f"  Seed样本数: {metrics.total_seeds}")
        print(f"  变体样本数: {metrics.total_variants}")
        print(f"  演化代数: {metrics.generations}")
        print(f"  多样性分数: {metrics.diversity_score:.3f}")
        print(f"  质量分数: {metrics.quality_score:.3f}")
        
        print("\n增强数据生成器测试完成！")
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_enhanced_generator())
    sys.exit(0 if success else 1)
