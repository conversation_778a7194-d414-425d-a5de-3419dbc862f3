# 增强采样系统配置文件
# 基础样本生成与多样性扩展配置

# 基础样本生成配置
base_generation:
  # 从ORM配置生成的基础样本数量
  base_samples_count: 200
  # ORM配置文件路径
  orm_config_path: "configs/orm_def.yaml"
  # 是否启用意图组合生成
  enable_intent_combination: true

# 多样化指令生成配置
paraphrase_generation:
  # 每个基础样本生成的变体数量
  paraphrase_variants: 4
  # 改写模型配置（禁用API调用）
  model_config:
    enabled: false  # 禁用模型调用
    name: "local"
    temperature: 0.7
    max_tokens: 150
  # 改写策略权重
  strategy_weights:
    formal: 0.25      # 正式表达
    casual: 0.35      # 口语化表达
    technical: 0.20   # 技术表达
    brief: 0.20       # 简洁表达
  # 并发控制
  max_concurrent: 5
  retry_attempts: 3

# 鲁棒性样本生成配置
robustness_generation:
  # 对抗样本生成比例（相对于基础样本）
  adversarial_ratio: 0.3
  # 边界样本数量
  boundary_samples: 50
  # 负样本数量
  negative_samples: 100
  
  # 扰动策略配置
  perturbation_strategies:
    entity_substitution:
      enabled: true
      weight: 0.3
      fake_entities: ["订购", "商品", "客人", "供货商", "物料"]
    
    field_type_confusion:
      enabled: true
      weight: 0.25
      confusion_mappings:
        "字段": ["属性", "列", "项目"]
        "类型": ["格式", "种类", "样式"]
    
    partial_instruction:
      enabled: true
      weight: 0.25
      min_word_ratio: 0.4  # 最少保留40%的词
      max_word_ratio: 0.8  # 最多保留80%的词
    
    ambiguous_reference:
      enabled: true
      weight: 0.2
      replacements:
        "客户表": ["这个表", "那张表", "表"]
        "订单": ["那个", "这个", "它"]

  # 边界样本模式
  boundary_patterns:
    incomplete:
      - "加字段"
      - "删除那个"
      - "修改一下"
      - "客户需要"
      - "订单表"
      - "给它添加"
    
    ambiguous:
      - "改一下那个东西"
      - "删掉不要的"
      - "加个什么字段"
      - "修改表结构"

  # 负样本类别
  negative_categories:
    irrelevant:
      - "今天天气怎么样"
      - "请帮我订机票"
      - "发送邮件给客户"
      - "生成月度报表"
    
    system_messages:
      - "数据库连接失败"
      - "系统重启中"
      - "用户权限不足"
      - "文件上传完成"

# 自适应采样配置
adaptive_sampling:
  # 学习率
  learning_rate: 0.1
  # 最小样本数（开始自适应调整前）
  min_samples_for_adaptation: 50
  # 错误率权重调整
  error_rate_adjustment:
    enabled: true
    # 错误率阈值，超过此值的样本类型将获得更高权重
    high_error_threshold: 0.6
    # 权重调整系数
    weight_boost_factor: 1.5
  
  # 难例挖掘配置
  hard_example_mining:
    enabled: true
    # 置信度阈值，低于此值的样本被认为是难例
    confidence_threshold: 0.5
    # 难例在下一轮训练中的权重倍数
    hard_example_weight_multiplier: 2.0
    # 最大难例数量
    max_hard_examples: 500

# 主动学习配置
active_learning:
  # 是否启用主动学习
  enabled: true
  # 不确定性采样策略
  uncertainty_sampling:
    strategy: "entropy"  # entropy, least_confidence, margin
    # 每轮选择的样本数量
    samples_per_round: 100
    # 不确定性阈值
    uncertainty_threshold: 0.3
  
  # 人工反馈机制
  human_feedback:
    enabled: true
    # 反馈收集接口
    feedback_api_endpoint: "http://localhost:8080/api/feedback"
    # 反馈样本的权重
    feedback_sample_weight: 3.0
    # 反馈样本存储路径
    feedback_storage_path: "data/feedback_samples.json"

# 数据质量控制配置
quality_control:
  # 去重配置
  deduplication:
    # 哈希去重
    hash_dedup:
      enabled: true
    
    # 语义去重
    semantic_dedup:
      enabled: true
      # 语义相似性阈值
      similarity_threshold: 0.85
      # 句子嵌入模型
      embedding_model: "all-MiniLM-L6-v2"
      # 批量处理大小
      batch_size: 32
    
    # BM25去重
    bm25_dedup:
      enabled: true
      # BM25相似性阈值
      bm25_threshold: 0.9
  
  # 样本验证
  validation:
    # 指令长度限制
    instruction_length:
      min_length: 2
      max_length: 200
    
    # 必填字段检查
    required_fields:
      - "instruction"
      - "sample_type"
    
    # JSON意图验证
    intent_validation:
      enabled: true
      required_intent_fields:
        - "intentType"
        - "targetConceptName"
      # 允许的意图类型
      allowed_intent_types:
        - "ADD_COLUMN"
        - "DELETE_COLUMN"
        - "MODIFY_COLUMN"
        - "ADD_RELATIONSHIP"
        - "ENABLE_SOFT_DELETE"

# Prompt蒸馏配置
prompt_distillation:
  # 教师模型配置
  teacher_model:
    name: "Qwen/Qwen2.5-72B-Instruct"
    temperature: 0.3
    max_tokens: 512
  
  # Chain-of-Thought生成
  cot_generation:
    enabled: true
    # CoT提示模板
    cot_prompt_template: |
      请分析以下自然语言指令，并生成结构化的推理过程：
      
      指令：{instruction}
      
      请按以下步骤分析：
      1. 识别操作类型
      2. 提取目标实体
      3. 确定操作参数
      4. 生成完整意图JSON
      
      推理过程：
    
    # 软标签生成
    soft_label_generation:
      enabled: true
      # 置信度分布生成
      confidence_distribution: true
      # 候选答案生成数量
      num_candidates: 5

# 管道流程配置
pipeline_flow:
  # 执行步骤顺序
  execution_steps:
    - "intent_driven_generation"
    - "paraphrase_generation"
    - "robustness_generation"
    - "quality_control"
    - "adaptive_sampling"
    - "export_results"
  
  # 并行处理配置
  parallel_processing:
    # 最大并发数
    max_workers: 4
    # 批量处理大小
    batch_size: 50
  
  # 检查点保存
  checkpointing:
    enabled: true
    # 检查点保存间隔（样本数）
    save_interval: 1000
    # 检查点存储路径
    checkpoint_dir: "checkpoints/sampling"
  
  # 监控与日志
  monitoring:
    # 详细日志
    verbose_logging: true
    # 性能指标收集
    collect_metrics: true
    # 指标保存路径
    metrics_output_path: "logs/sampling_metrics.json"
    
    # 实时监控配置
    real_time_monitoring:
      enabled: true
      # 监控端口
      monitor_port: 8888
      # 监控更新间隔（秒）
      update_interval: 30

# 输出配置
output:
  # 默认输出路径
  default_output_path: "data/enhanced_samples.json"
  
  # 输出格式
  formats:
    json:
      enabled: true
      pretty_print: true
    
    jsonl:
      enabled: true
      output_path: "data/enhanced_samples.jsonl"
    
    parquet:
      enabled: false
      output_path: "data/enhanced_samples.parquet"
  
  # 数据分片
  data_sharding:
    enabled: false
    # 每个分片的最大样本数
    max_samples_per_shard: 10000
    # 分片文件名模式
    shard_pattern: "enhanced_samples_shard_{:03d}.json"

# 实验配置
experiments:
  # A/B测试配置
  ab_testing:
    enabled: false
    # 测试组配置
    test_groups:
      group_a:
        paraphrase_variants: 4
        adversarial_ratio: 0.3
      group_b:
        paraphrase_variants: 6
        adversarial_ratio: 0.4
  
  # 样本质量评估实验
  quality_evaluation:
    enabled: true
    # 评估模型
    evaluation_model: "doubao"
    # 评估维度
    evaluation_criteria:
      - name: "semantic_consistency"
        weight: 0.4
      - name: "expression_naturalness"
        weight: 0.3
      - name: "ambiguity_level"
        weight: 0.3
    
    # 评估批量大小
    evaluation_batch_size: 20
    # 评估结果保存路径
    evaluation_results_path: "data/quality_evaluation.json"

# 资源配置
resources:
  # 内存配置
  memory:
    # 最大内存使用量（GB）
    max_memory_gb: 8
    # 内存使用监控
    memory_monitoring: true
  
  # GPU配置
  gpu:
    # 是否使用GPU
    use_gpu: true
    # GPU设备ID
    device_ids: [0]
    # GPU内存限制（GB）
    gpu_memory_limit: 6
  
  # 临时文件配置
  temp_files:
    # 临时文件目录
    temp_dir: "/tmp/enhanced_sampling"
    # 自动清理临时文件
    auto_cleanup: true

# 调试配置
debug:
  # 调试模式
  debug_mode: false
  # 样本数量限制（调试模式下）
  debug_sample_limit: 50
  # 保存中间结果
  save_intermediate_results: true
  # 中间结果保存目录
  intermediate_results_dir: "debug/intermediate"
  
  # 性能分析
  profiling:
    enabled: false
    # 分析工具
    profiler: "cProfile"  # cProfile, line_profiler
    # 分析结果保存路径
    profile_output: "debug/performance_profile.txt"