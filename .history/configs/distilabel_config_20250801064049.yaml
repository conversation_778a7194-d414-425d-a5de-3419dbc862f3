# distilabel Pipeline配置文件 - 支持多模型互评
# 扩展现有的pipeline_config.yaml，增加大模型互评功能

# 继承基础配置
pipeline:
  mode: "distilabel_multi_model"  # 多模型互评模式
  enable_logging: true
  save_intermediate: true
  use_cache: false

# distilabel特定配置
distilabel:
  # Pipeline配置
  pipeline_name: "MultiModelIntentGeneration"
  batch_size: 16  # 由于需要多次LLM调用，适当减小batch size
  num_workers: 2
  
  # 主生成模型配置（通义千问）
  primary_model:
    name: "Qwen/Qwen2.5-7B-Instruct"
    type: "transformers"  # transformers | inference_endpoints | openai
    generation_kwargs:
      max_new_tokens: 512
      temperature: 0.7
      do_sample: true
      top_p: 0.9
    
    # 模型加载配置
    device_map: "auto"
    torch_dtype: "float16"
    trust_remote_code: true

# G-Eval配置（豆包模型作为评判者）
g_eval:
  # 评判模型配置
  evaluator_model:
    name: "doubao"  # 豆包模型标识
    type: "inference_endpoints"  # 通过API调用
    api_endpoint: "https://ark.cn-beijing.volces.com/api/v3"  # 豆包API端点
    api_key: "${DOUBAO_API_KEY}"  # 从环境变量读取
    generation_kwargs:
      max_new_tokens: 256
      temperature: 0.3  # 评估任务使用较低温度
      top_p: 0.8
  
  # G-Eval评估标准
  criteria:
    - name: "semantic_consistency"
      description: "判断自然语言指令与JSON意图的语义是否完全一致，没有信息丢失或添加。评分1-5分，5分表示完全一致。"
      weight: 0.4
    
    - name: "expression_naturalness" 
      description: "评估指令的语言表达是否自然、符合用户日常说话习惯，避免过于正式或生硬。评分1-5分，5分表示非常自然。"
      weight: 0.3
    
    - name: "ambiguity_level"
      description: "评估指令中是否存在歧义、模糊不清或可能被误解的表达。评分1-5分，5分表示完全无歧义。"
      weight: 0.2
    
    - name: "overall_quality"
      description: "综合评估指令的整体质量，包括完整性、准确性和可用性。评分1-5分，5分表示质量优秀。"
      weight: 0.1
  
  # 质量阈值
  quality_thresholds:
    min_overall_score: 3.5      # G-Eval综合分数阈值
    min_semantic_consistency: 4.0  # 语义一致性最低要求
    min_expression_naturalness: 3.0  # 表达自然度最低要求
    max_ambiguity_tolerance: 4.0    # 歧义容忍度（分数越高歧义越少）

# 多模型验证配置
multi_model_validation:
  # 一致性验证配置
  consistency_check:
    min_consistency_score: 0.8    # 正向验证一致性阈值
    enable_cross_validation: true  # 启用交叉验证
  
  # 模型角色分配
  model_roles:
    generator: "qwen"      # 生成器：通义千问
    evaluator: "doubao"    # 评估器：豆包
    validator: "qwen"      # 验证器：通义千问（复用）
  
  # 互评策略
  evaluation_strategy:
    enable_bidirectional: false  # 是否双向评估（A评B，B评A）
    consensus_threshold: 0.8     # 共识阈值
    disagreement_handling: "conservative"  # 分歧处理：conservative|optimistic|majority

# 增强模式配置（复用现有配置并扩展）
enhanced_mode:
  seed_samples_per_intent: 50   # 减少数量以应对多模型调用开销
  evolution_generations: 2
  variants_per_generation: 2
  diversity_threshold: 0.8
  enable_llm_enhancement: true
  
  # 多模型增强特定配置
  multi_model_enhancement:
    enable_iterative_refinement: true   # 启用迭代优化
    max_refinement_rounds: 2           # 最大优化轮数
    refinement_trigger_score: 3.0      # 触发优化的分数阈值

# 数据生成策略（保持原有策略）
generation_strategy:
  # 语义矩阵权重
  semantic_matrix:
    atomic: 35
    composite: 30
    sequence: 20
    implicit: 15
  
  # 表达矩阵权重
  expression_matrix:
    formal: 30
    casual: 40      # 增加口语化权重
    technical: 30
  
  # 鲁棒性矩阵权重
  robustness_matrix:
    adversarial_ratio: 0.1
    boundary_cases: 0.05
    negative_samples: 0.05

# 质量控制配置（增强版）
quality_control:
  # 多重验证
  enable_consistency_check: true
  enable_g_eval_check: true
  enable_semantic_validation: true
  enable_structure_validation: true
  
  # 综合评分策略
  scoring_strategy:
    consistency_weight: 0.4      # 一致性权重
    g_eval_weight: 0.5          # G-Eval权重
    structure_weight: 0.1       # 结构验证权重
  
  # 去重配置
  deduplication:
    similarity_threshold: 0.85
    use_semantic_similarity: true
    use_exact_match: true
    enable_cross_model_dedup: true  # 跨模型去重

# 输出配置
output:
  save_format: ["json", "parquet", "csv"]
  output_dir: "./data/processed/multi_model_distilabel"
  filename_prefix: "multi_model_intent_dataset"
  
  # 数据分割
  train_ratio: 0.8
  val_ratio: 0.1
  test_ratio: 0.1
  
  # 详细元数据保存
  save_metadata: true
  save_quality_report: true
  save_pipeline_config: true
  save_g_eval_details: true    # 保存G-Eval详细结果
  save_model_responses: true   # 保存模型原始响应

# 监控和日志配置（增强版）
monitoring:
  enable_step_timing: true
  enable_memory_monitoring: true
  enable_llm_call_monitoring: true  # 监控LLM调用
  log_sample_examples: true
  sample_log_count: 5
  
  # 多模型监控
  multi_model_monitoring:
    track_model_performance: true
    track_agreement_rates: true     # 跟踪模型间一致性
    track_g_eval_distribution: true # G-Eval分数分布
  
  # 质量监控阈值
  quality_thresholds:
    min_samples_per_intent: 30
    max_consistency_score_std: 0.2
    min_avg_g_eval_score: 3.5
    max_model_disagreement_rate: 0.3

# 错误处理配置（增强版）
error_handling:
  max_retries: 3
  retry_delay: 2.0
  skip_failed_samples: true
  log_failed_samples: true
  
  # 多模型错误处理
  multi_model_fallback:
    enable_model_fallback: true      # 模型故障时的降级策略
    fallback_model: "Qwen/Qwen2.5-4B-Instruct"
    enable_single_model_mode: true   # 紧急情况下单模型运行
  
  # API错误处理
  api_handling:
    timeout: 30.0
    max_concurrent_requests: 5
    rate_limit_handling: "exponential_backoff"

# 成本控制配置
cost_control:
  # API调用限制
  max_api_calls_per_hour: 1000
  max_tokens_per_request: 1000
  
  # 预算控制
  enable_cost_tracking: true
  max_daily_cost: 50.0  # 美元
  cost_alert_threshold: 0.8
  
  # 优化策略
  batch_optimization: true
  cache_responses: true
  smart_retry: true  # 智能重试策略