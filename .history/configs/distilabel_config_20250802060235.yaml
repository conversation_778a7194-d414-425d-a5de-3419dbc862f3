pipeline:
  mode: "distilabel_multi_model"  # 多模型互评模式
  enable_logging: true
  save_intermediate: true
  use_cache: false

# distilabel特定配置
distilabel:
  # Pipeline配置
  pipeline_name: "QwenDoubaoMultiModelGeneration"
  batch_size: 8  # 适当减小以控制API调用频率
  num_workers: 2
  
  # 主生成模型配置（通义千问）
  primary_model:
    name: "qwen"  # 使用您的千问API
    type: "inference_endpoints"  # 使用API调用模式
    api_key: "${QWEN_API_KEY}"
    api_endpoint: "${QWEN_API_ENDPOINT}"
    generation_kwargs:
      max_new_tokens: 512
      temperature: 0.7
      do_sample: true
      top_p: 0.9
    
    # API调用配置
    timeout: 30
    max_retries: 3
    retry_delay: 2.0

# G-Eval配置（豆包模型作为评判者）
g_eval:
  # 评判模型配置
  evaluator_model:
    name: "Doubao-Seed-1.6"  # 使用您的豆包模型
    type: "inference_endpoints"
    api_key: "${DOUBAO_API_KEY}"
    api_endpoint: "${DOUBAO_API_ENDPOINT}"
    generation_kwargs:
      max_new_tokens: 256
      temperature: 0.3  # 评估任务使用较低温度
      top_p: 0.8
    
    # API调用配置
    timeout: 30
    max_retries: 3
    retry_delay: 2.0
  
  # G-Eval评估标准
  criteria:
    - name: "semantic_consistency"
      description: "判断自然语言指令与JSON意图的语义是否完全一致，没有信息丢失或添加。评分1-5分，5分表示完全一致。"
      weight: 0.4
    
    - name: "expression_naturalness" 
      description: "评估指令的语言表达是否自然、符合用户日常说话习惯，避免过于正式或生硬。评分1-5分，5分表示非常自然。"
      weight: 0.3
    
    - name: "ambiguity_level"
      description: "评估指令中是否存在歧义、模糊不清或可能被误解的表达。评分1-5分，5分表示完全无歧义。"
      weight: 0.2
    
    - name: "overall_quality"
      description: "综合评估指令的整体质量，包括完整性、准确性和可用性。评分1-5分，5分表示质量优秀。"
      weight: 0.1
  
  # 质量阈值
  quality_thresholds:
    min_overall_score: 3.5      # G-Eval综合分数阈值
    min_semantic_consistency: 4.0  # 语义一致性最低要求
    min_expression_naturalness: 3.0  # 表达自然度最低要求
    max_ambiguity_tolerance: 4.0    # 歧义容忍度（分数越高歧义越少）

# 多模型验证配置
multi_model_validation:
  # 一致性验证配置
  consistency_check:
    min_consistency_score: 0.8    # 正向验证一致性阈值
    enable_cross_validation: true  # 启用交叉验证
  
  # 模型角色分配
  model_roles:
    generator: "qwen"      # 生成器：通义千问
    evaluator: "doubao"    # 评估器：豆包
    validator: "qwen"      # 验证器：通义千问（复用）
  
  # 互评策略
  evaluation_strategy:
    enable_bidirectional: false  # 是否双向评估
    consensus_threshold: 0.8     # 共识阈值
    disagreement_handling: "conservative"  # 分歧处理策略

# 增强模式配置
enhanced_mode:
  seed_samples_per_intent: 30   # 适当减少以控制API调用成本
  evolution_generations: 2
  variants_per_generation: 2
  diversity_threshold: 0.8
  enable_llm_enhancement: true
  
  # 多模型增强特定配置
  multi_model_enhancement:
    enable_iterative_refinement: true   # 启用迭代优化
    max_refinement_rounds: 1           # 减少优化轮数以控制成本
    refinement_trigger_score: 3.0      # 触发优化的分数阈值

# 数据生成策略
generation_strategy:
  # 语义矩阵权重
  semantic_matrix:
    atomic: 35
    composite: 30
    sequence: 20
    implicit: 15
  
  # 表达矩阵权重
  expression_matrix:
    formal: 30
    casual: 40      # 增加口语化权重
    technical: 30
  
  # 鲁棒性矩阵权重
  robustness_matrix:
    adversarial_ratio: 0.1
    boundary_cases: 0.05
    negative_samples: 0.05

# 质量控制配置
quality_control:
  # 多重验证
  enable_consistency_check: true
  enable_g_eval_check: true
  enable_semantic_validation: true
  enable_structure_validation: true
  
  # 综合评分策略
  scoring_strategy:
    consistency_weight: 0.4      # 一致性权重
    g_eval_weight: 0.5          # G-Eval权重
    structure_weight: 0.1       # 结构验证权重
  
  # 去重配置
  deduplication:
    similarity_threshold: 0.85
    use_semantic_similarity: true
    use_exact_match: true
    enable_cross_model_dedup: true

# 输出配置
output:
  save_format: ["json", "parquet", "csv"]
  output_dir: "./data/processed/qwen_doubao_output"
  filename_prefix: "qwen_doubao_intent_dataset"
  
  # 数据分割
  train_ratio: 0.8
  val_ratio: 0.1
  test_ratio: 0.1
  
  # 详细元数据保存
  save_metadata: true
  save_quality_report: true
  save_pipeline_config: true
  save_g_eval_details: true
  save_model_responses: true

# 监控和日志配置
monitoring:
  enable_step_timing: true
  enable_memory_monitoring: true
  enable_llm_call_monitoring: true
  log_sample_examples: true
  sample_log_count: 3  # 减少示例日志数量
  
  # 多模型监控
  multi_model_monitoring:
    track_model_performance: true
    track_agreement_rates: true
    track_g_eval_distribution: true
    track_api_usage: true  # 新增API使用监控
  
  # 质量监控阈值
  quality_thresholds:
    min_samples_per_intent: 20  # 降低最小样本要求
    max_consistency_score_std: 0.2
    min_avg_g_eval_score: 3.5
    max_model_disagreement_rate: 0.3

# 错误处理配置
error_handling:
  max_retries: 3
  retry_delay: 2.0
  skip_failed_samples: true
  log_failed_samples: true
  
  # API错误处理
  api_handling:
    timeout: 30.0
    max_concurrent_requests: 3  # 降低并发以避免API限制
    rate_limit_handling: "exponential_backoff"
    enable_rate_limiting: true
    requests_per_second: 2  # 每秒最多2个请求
  
  # 多模型错误处理
  multi_model_fallback:
    enable_model_fallback: true
    fallback_to_single_model: true  # API失败时降级到单模型
    emergency_stop_threshold: 10    # 连续失败10次后停止

# 成本控制配置
cost_control:
  # API调用限制
  max_api_calls_per_hour: 500   # 根据您的API配额调整
  max_tokens_per_request: 800
  
  # 预算控制
  enable_cost_tracking: true
  max_daily_cost: 20.0  # 根据预算调整
  cost_alert_threshold: 0.8
  
  # 优化策略
  batch_optimization: true
  cache_responses: true
  smart_retry: true
  enable_request_deduplication: true  # 避免重复请求

# API特定配置
api_configs:
  # 千问API配置
  qwen:
    base_url: "https://dashscope.aliyuncs.com/api/v1"
    model_name: "qwen"
    headers:
      Authorization: "Bearer ${QWEN_API_KEY}"
      Content-Type: "application/json"
  
  # 豆包API配置
  doubao:
    base_url: "https://ark.cn-beijing.volces.com/api/v3"
    model_name: "Doubao-Seed-1.6"
    headers:
      Authorization: "Bearer ${DOUBAO_API_KEY}"
      Content-Type: "application/json"

# 环境特定配置
environment:
  # 从环境变量读取的配置
  api_keys:
    qwen: "${QWEN_API_KEY}"
    doubao: "${DOUBAO_API_KEY}"
  
  # 调试配置
  debug_mode: false
  save_debug_info: true
  log_api_requests: true
  log_api_responses: false  # 避免日志过大