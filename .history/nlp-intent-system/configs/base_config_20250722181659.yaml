# 项目基础配置文件

# 应用配置
app:
  name: "nlp-intent-system"
  version: "1.0.0"
  debug: true

# API服务配置
api:
  host: "127.0.0.1"
  port: 8000
  workers: 1
  reload: true

# 模型配置
model:
  base_model: "Qwen/Qwen2.5-7B-Instruct"
  device: "auto"  # auto, cpu, cuda
  max_length: 2048
  temperature: 0.1
  top_p: 0.9
  do_sample: true

# LoRA配置
lora:
  r: 16
  lora_alpha: 32
  lora_dropout: 0.1
  target_modules: ["q_proj", "k_proj", "v_proj", "o_proj"]
  task_type: "CAUSAL_LM"

# 数据配置
data:
  templates_path: "./data/templates"
  training_data_path: "./data/training"
  processed_data_path: "./data/processed"
  validation_split: 0.2
  max_samples_per_intent: 1000
  min_instruction_length: 5
  max_instruction_length: 200

# 训练配置
training:
  batch_size: 4
  learning_rate: 1e-4
  num_epochs: 3
  warmup_steps: 100
  save_steps: 500
  eval_steps: 100
  logging_steps: 10
  output_dir: "./models/checkpoints"

# 推理配置
inference:
  batch_size: 1
  max_new_tokens: 512
  use_cache: true
  return_dict_in_generate: true

# 质量控制配置
quality:
  similarity_threshold: 0.85
  min_confidence: 0.7
  enable_deduplication: true
  enable_quality_assessment: true

# 日志配置
logging:
  level: "INFO"
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
  file_path: "./logs/app.log"
  rotation: "10 MB"
  retention: "7 days"

# 系统配置
system:
  max_memory_usage: "8GB"
  cache_size: 1000
  timeout: 30