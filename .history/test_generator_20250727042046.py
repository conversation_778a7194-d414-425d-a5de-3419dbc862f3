"""
测试数据生成器
"""

import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.data_generator import ReverseDataGenerator

async def test_data_generator():
    """测试数据生成器功能"""
    print("开始测试数据生成器...")
    
    try:
        # 初始化生成器
        generator = ReverseDataGenerator()
        
        # 测试单个意图类型生成
        print("\n测试ADD_COLUMN类型样本生成...")
        add_column_samples = generator.generate_single_intent_samples("ADD_COLUMN", 10)
        
        print(f"生成了 {len(add_column_samples)} 个ADD_COLUMN样本")
        
        # 显示几个样本
        for i, sample in enumerate(add_column_samples[:3]):
            print(f"\n样本 {i+1}:")
            print(f"  指令: {sample.instruction}")
            print(f"  意图: {sample.output[0].intentType}")
            print(f"  实体: {sample.output[0].targetConceptName}")
            print(f"  属性: {sample.output[0].props}")
        
        # 测试模糊样本生成
        print("\n测试模糊样本生成...")
        fuzzy_samples = generator.generate_fuzzy_samples(10)
        
        print(f"生成了 {len(fuzzy_samples)} 个模糊样本")
        
        # 显示几个模糊样本
        for i, sample in enumerate(fuzzy_samples[:3]):
            print(f"\n模糊样本 {i+1}:")
            print(f"  指令: {sample.instruction}")
            print(f"  意图数量: {len(sample.output)}")
            print(f"  元数据: {sample.metadata}")
        
        print("\n数据生成器测试完成！")
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_data_generator())
    sys.exit(0 if success else 1)
