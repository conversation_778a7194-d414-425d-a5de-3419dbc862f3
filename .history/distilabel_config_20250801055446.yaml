# distilabel Pipeline配置文件
# 扩展现有的pipeline_config.yaml，增加distilabel特定配置

# 继承基础配置
pipeline:
  mode: "distilabel"  # 新增distilabel模式
  enable_logging: true
  save_intermediate: true
  use_cache: false

# distilabel特定配置
distilabel:
  # Pipeline配置
  pipeline_name: "ConfigDrivenIntentGeneration"
  batch_size: 32
  num_workers: 4
  
  # 模型配置
  model:
    name: "Qwen/Qwen2.5-7B-Instruct"
    type: "transformers"  # transformers | inference_endpoints | openai
    generation_kwargs:
      max_new_tokens: 512
      temperature: 0.7
      do_sample: true
      top_p: 0.9
    
    # 备用模型配置（支持多模型切换）
    fallback_models:
      - "Qwen/Qwen2.5-4B-Instruct"
      - "microsoft/DialoGPT-medium"

# 增强模式配置（复用现有配置）
enhanced_mode:
  seed_samples_per_intent: 100
  evolution_generations: 2
  variants_per_generation: 3
  diversity_threshold: 0.8
  enable_llm_enhancement: true
  
  # distilabel特定增强
  quality_filter:
    min_consistency_score: 0.7
    min_quality_score: 0.5
    enable_duplicate_detection: true

# 数据生成策略（复用现有权重配置）
generation_strategy:
  # 语义矩阵权重
  semantic_matrix:
    atomic: 35
    composite: 30
    sequence: 20
    implicit: 15
  
  # 表达矩阵权重
  expression_matrix:
    formal: 40
    casual: 35
    technical: 25
  
  # 鲁棒性矩阵权重
  robustness_matrix:
    adversarial_ratio: 0.1
    boundary_cases: 0.05
    negative_samples: 0.05

# 质量控制配置
quality_control:
  enable_consistency_check: true
  consistency_threshold: 0.7
  enable_semantic_validation: true
  enable_structure_validation: true
  
  # 去重配置
  deduplication:
    similarity_threshold: 0.85
    use_semantic_similarity: true
    use_exact_match: true

# 输出配置
output:
  save_format: ["json", "parquet"]  # distilabel支持的格式
  output_dir: "./data/processed/distilabel"
  filename_prefix: "intent_dataset"
  
  # 数据分割
  train_ratio: 0.8
  val_ratio: 0.1
  test_ratio: 0.1
  
  # 元数据保存
  save_metadata: true
  save_quality_report: true
  save_pipeline_config: true

# 监控和日志配置
monitoring:
  enable_step_timing: true
  enable_memory_monitoring: true
  log_sample_examples: true
  sample_log_count: 10
  
  # 质量监控
  quality_thresholds:
    min_samples_per_intent: 50
    max_consistency_score_std: 0.3
    min_avg_quality_score: 0.6

# 错误处理配置
error_handling:
  max_retries: 3
  retry_delay: 2.0
  skip_failed_samples: true
  log_failed_samples: true
  
  # 降级策略
  fallback_strategy:
    enable_rule_based_fallback: true
    enable_template_fallback: true