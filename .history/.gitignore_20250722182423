# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyTorch模型文件
*.pth
*.pt
*.bin
*.safetensors

# 虚拟环境
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# 日志文件
logs/
*.log

# 数据文件
data/raw/*
data/processed/*
!data/raw/.gitkeep
!data/processed/.gitkeep

# 模型文件
models/adapters/*
models/checkpoints/*
!models/adapters/.gitkeep
!models/checkpoints/.gitkeep

# 环境变量
.env
.env.local

# 临时文件
*.tmp
*.temp

# 操作系统
.DS_Store
Thumbs.db