## 快速开始

### 环境要求
- Python 3.9+
- PyTorch 2.0+
- CUDA (可选，用于GPU加速)

### 安装依赖
```bash
# 激活虚拟环境
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 启动API服务
python src/api/main.py

# 或使用uvicorn
uvicorn src.api.main:app --reload --host 127.0.0.1 --port 8000

# 测试健康检查
curl http://127.0.0.1:8000/health

# 意图解析
curl -X POST "http://127.0.0.1:8000/parse" \
  -H "Content-Type: application/json" \
  -d '{"text": "给客户表添加一个等级字段，类型为整型"}'

# 获取示例
curl http://127.0.0.1:8000/parse/examples