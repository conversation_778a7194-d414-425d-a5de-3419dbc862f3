#!/usr/bin/env python3
"""
阿里云存储使用示例
演示如何使用阿里云存储功能保存和管理训练数据
"""

import sys
import asyncio
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.data_pipeline import DataGenerationPipeline
from src.storage.storage_manager import AliyunStorageManager

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def example_1_basic_storage():
    """示例1: 基础存储功能"""
    print("\n" + "="*60)
    print("📝 示例1: 基础阿里云存储功能")
    print("="*60)
    
    try:
        # 初始化存储管理器
        storage_manager = AliyunStorageManager()
        
        # 检查存储状态
        status = storage_manager.get_storage_status()
        print(f"存储状态: {status}")
        
        # 准备示例数据
        sample_data = [
            {
                "instruction": "为用户表添加邮箱字段",
                "output": [
                    {
                        "intentType": "ADD_COLUMN",
                        "targetConceptName": "用户",
                        "props": {
                            "name": "邮箱",
                            "stdSqlType": "VARCHAR(255)"
                        }
                    }
                ],
                "metadata": {
                    "example": "basic_storage",
                    "quality_score": "HIGH"
                }
            }
        ]
        
        quality_report = {
            "total_samples": 1,
            "valid_samples": 1,
            "quality_metrics": {
                "average_score": 0.95
            }
        }
        
        # 保存数据
        results = storage_manager.save_training_data(
            samples=sample_data,
            quality_report=quality_report,
            metadata={"example": "basic_storage"}
        )
        
        print(f"保存结果: {results}")
        
    except Exception as e:
        logger.error(f"示例1执行失败: {e}")

async def example_2_pipeline_integration():
    """示例2: 与数据生成Pipeline集成"""
    print("\n" + "="*60)
    print("🔄 示例2: Pipeline集成存储")
    print("="*60)
    
    try:
        # 初始化数据生成Pipeline
        pipeline = DataGenerationPipeline()
        await pipeline.initialize()
        
        # 检查阿里云存储状态
        storage_status = pipeline.get_aliyun_storage_status()
        print(f"Pipeline中的存储状态: {storage_status}")
        
        if not storage_status.get("available"):
            print("⚠️ 阿里云存储不可用，将只保存到本地")
            return
        
        # 生成少量测试数据
        print("🔄 生成测试数据...")
        samples = await pipeline.generate_samples(
            intent_types=["ADD_COLUMN", "DELETE_COLUMN"],
            samples_per_intent=2,
            use_enhanced_sampling=True
        )
        
        if samples:
            print(f"✅ 生成了 {len(samples)} 个样本")
            
            # 质量评估
            quality_report = await pipeline.assess_quality(samples)
            print(f"质量评估完成: {quality_report.get('summary', {})}")
            
            # 保存结果（会自动上传到阿里云）
            saved_files = await pipeline.save_results(samples, quality_report)
            print(f"保存的文件: {saved_files}")
            
        else:
            print("❌ 未生成任何样本")
            
    except Exception as e:
        logger.error(f"示例2执行失败: {e}")

async def example_3_storage_strategies():
    """示例3: 不同存储策略演示"""
    print("\n" + "="*60)
    print("📊 示例3: 存储策略对比")
    print("="*60)
    
    strategies = [
        "oss_only",
        "rds_only", 
        "oss_primary_rds_index",
        "both_sync"
    ]
    
    for strategy in strategies:
        print(f"\n🔧 测试策略: {strategy}")
        
        try:
            # 创建临时配置
            temp_config = {
                "hybrid_storage": {
                    "strategy": strategy
                },
                "oss": {
                    "bucket_name": "test-bucket",
                    "endpoint": "oss-cn-hangzhou.aliyuncs.com"
                },
                "rds": {
                    "connection": {
                        "host": "localhost",
                        "port": 3306,
                        "database": "test_db",
                        "username": "test_user",
                        "password": "test_pass"
                    }
                }
            }
            
            print(f"策略 {strategy} 的特点:")
            if strategy == "oss_only":
                print("  - 仅使用OSS对象存储")
                print("  - 适合大文件存储，成本低")
                print("  - 不支持复杂查询")
            elif strategy == "rds_only":
                print("  - 仅使用RDS数据库")
                print("  - 支持复杂查询和事务")
                print("  - 适合结构化数据")
            elif strategy == "oss_primary_rds_index":
                print("  - OSS存储文件，RDS存储索引")
                print("  - 平衡存储成本和查询能力")
                print("  - 推荐策略")
            elif strategy == "both_sync":
                print("  - 同时存储到OSS和RDS")
                print("  - 最高可靠性")
                print("  - 成本较高")
                
        except Exception as e:
            print(f"  ❌ 策略 {strategy} 测试失败: {e}")

async def example_4_data_management():
    """示例4: 数据管理功能"""
    print("\n" + "="*60)
    print("🗂️ 示例4: 数据管理功能")
    print("="*60)
    
    try:
        storage_manager = AliyunStorageManager()
        
        # 1. 查看存储统计
        print("📊 存储统计信息:")
        if hasattr(storage_manager, 'rds_storage') and storage_manager.rds_storage:
            stats = storage_manager.rds_storage.get_batch_statistics()
            print(f"  总样本数: {stats.get('total_samples', 0)}")
            print(f"  意图分布: {stats.get('intent_distribution', {})}")
        
        # 2. 按意图类型查询数据
        print("\n🔍 按意图类型查询:")
        intent_types = ["ADD_COLUMN", "DELETE_COLUMN", "ADD_ENTITY"]
        
        for intent_type in intent_types:
            if hasattr(storage_manager, 'rds_storage') and storage_manager.rds_storage:
                samples = storage_manager.rds_storage.get_samples_by_intent(intent_type, limit=5)
                print(f"  {intent_type}: {len(samples)} 个样本")
        
        # 3. 列出OSS文件
        print("\nOSS文件列表:")
        if hasattr(storage_manager, 'oss_storage') and storage_manager.oss_storage:
            files = storage_manager.oss_storage.list_files()
            for file in files[:10]:  # 只显示前10个
                print(f"  - {file}")
            if len(files) > 10:
                print(f"  ... 还有 {len(files) - 10} 个文件")

        # 4. 数据清理建议
        print("\n数据清理建议:")
        print("  - 定期清理超过30天的临时数据")
        print("  - 压缩长期存储的历史数据")
        print("  - 监控存储使用量和成本")
        
    except Exception as e:
        logger.error(f"示例4执行失败: {e}")

async def example_5_monitoring_alerts():
    """示例5: 监控和告警"""
    print("\n" + "="*60)
    print("示例5: 监控和告警功能")
    print("="*60)

    try:
        storage_manager = AliyunStorageManager()

        # 获取详细状态
        status = storage_manager.get_storage_status()

        print("存储健康检查:")

        # OSS健康检查
        if status.get('oss_available'):
            oss_status = status.get('oss_status', {})
            if oss_status.get('connected'):
                print("  OSS连接正常")
                file_count = oss_status.get('file_count', 0)
                if file_count > 1000:
                    print(f"  OSS文件数量较多 ({file_count})，建议清理")
                else:
                    print(f"  OSS文件数量: {file_count}")
            else:
                print(f"  OSS连接失败: {oss_status.get('error', 'unknown')}")
        else:
            print("  OSS不可用")

        # RDS健康检查
        if status.get('rds_available'):
            rds_status = status.get('rds_status', {})
            if rds_status.get('connected'):
                print("  RDS连接正常")
            else:
                print("  RDS连接失败")
        else:
            print("  RDS不可用")
        
        # 存储策略建议
        strategy = status.get('strategy', 'unknown')
        print(f"\n💡 当前存储策略: {strategy}")
        
        if strategy == "oss_only" and not status.get('oss_available'):
            print("  ⚠️ 建议配置备用存储策略")
        elif strategy == "both_sync" and (not status.get('oss_available') or not status.get('rds_available')):
            print("  ⚠️ 同步策略要求OSS和RDS都可用")
        
        print("\n📋 监控建议:")
        print("  - 设置存储使用量告警")
        print("  - 监控上传失败率")
        print("  - 定期检查连接状态")
        print("  - 配置成本预算告警")
        
    except Exception as e:
        logger.error(f"示例5执行失败: {e}")

async def main():
    """主函数"""
    print("🚀 阿里云存储使用示例")
    print("="*60)
    
    examples = [
        ("基础存储功能", example_1_basic_storage),
        ("Pipeline集成存储", example_2_pipeline_integration),
        ("存储策略对比", example_3_storage_strategies),
        ("数据管理功能", example_4_data_management),
        ("监控和告警", example_5_monitoring_alerts)
    ]
    
    print("\n可用示例:")
    for i, (name, _) in enumerate(examples, 1):
        print(f"{i}. {name}")
    print("0. 运行所有示例")
    
    try:
        choice = input("\n请选择要运行的示例 (0-5): ").strip()
        
        if choice == "0":
            # 运行所有示例
            for name, example_func in examples:
                print(f"\n🔄 运行示例: {name}")
                await example_func()
        elif choice.isdigit() and 1 <= int(choice) <= len(examples):
            # 运行指定示例
            name, example_func = examples[int(choice) - 1]
            print(f"\n🔄 运行示例: {name}")
            await example_func()
        else:
            print("❌ 无效选择")
            
    except KeyboardInterrupt:
        print("\n👋 用户中断，退出程序")
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
    
    print("\n🎉 示例程序结束")

if __name__ == "__main__":
    asyncio.run(main())
