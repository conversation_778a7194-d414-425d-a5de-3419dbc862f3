"""
意图解析系统的数据模型定义
"""

from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from enum import Enum

class IntentType(str, Enum):
    """支持的意图类型枚举"""
    ADD_COLUMN = "ADD_COLUMN"
    DELETE_COLUMN = "DELETE_COLUMN"
    MODIFY_COLUMN = "MODIFY_COLUMN"
    ADD_RELATIONSHIP = "ADD_RELATIONSHIP"
    DELETE_RELATIONSHIP = "DELETE_RELATIONSHIP"
    ENABLE_SOFT_DELETE = "ENABLE_SOFT_DELETE"

class Intent(BaseModel):
    """单个意图的数据结构"""
    intentType: IntentType
    targetConceptName: str
    props: Dict[str, Any] = Field(default_factory=dict)
    confidence: Optional[float] = None

class TrainingSample(BaseModel):
    """训练样本数据结构"""
    instruction: str
    output: List[Intent]
    metadata: Dict[str, Any] = Field(default_factory=dict)

class ParseRequest(BaseModel):
    """API解析请求模型"""
    text: str
    domain: Optional[str] = None
    context: Dict[str, Any] = Field(default_factory=dict)

class ParseResponse(BaseModel):
    """API解析响应模型"""
    intents: List[Intent]
    confidence: float
    need_clarification: bool = False
    clarification_options: List[str] = Field(default_factory=list)
    processing_time_ms: float = 0.0

class DataQualityMetrics(BaseModel):
    """数据质量评估指标"""
    total_samples: int
    avg_instruction_length: float
    intent_distribution_entropy: float
    expression_diversity: float = 0.0
    duplicate_rate: float = 0.0

class ConfigUpdate(BaseModel):
    """配置更新请求"""
    intents: List[Intent]
    target_config_file: str
    backup_enabled: bool = True