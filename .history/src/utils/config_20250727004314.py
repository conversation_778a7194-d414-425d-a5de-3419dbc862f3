"""
增强的配置管理工具 - 支持ORM配置加载
"""

import yaml
import os
from typing import Dict, Any, Optional, List
from loguru import logger

class ConfigManager:
    """增强的配置管理器，支持ORM配置"""
    
    def __init__(self, base_config_path: str = "configs/base_config.yaml",
                 pipeline_config_path: str = "configs/pipeline_config.yaml",
                 orm_def_path: str = "data/templates/orm_def.yaml",
                 orm_metadata_path: str = "data/templates/orm_metadata.yaml", 
                 orm_weights_path: str = "data/templates/orm_weights.yaml"):
        self.base_config_path = base_config_path
        self.pipeline_config_path = pipeline_config_path
        self.orm_def_path = orm_def_path
        self.orm_metadata_path = orm_metadata_path
        self.orm_weights_path = orm_weights_path
        
        # 配置存储
        self.base_config = {}
        self.pipeline_config = {}
        self.orm_config = {}
        self.orm_metadata = {}
        self.orm_weights = {}
        
        self.load_all_configs()
    
    def load_all_configs(self):
        """加载所有配置文件"""
        self.load_configs()  # 加载原有的基础配置
        self.load_orm_configs()  # 加载新的ORM配置
    
    def load_configs(self):
        """加载基础配置（保持原有逻辑）"""
        # 加载基础配置
        try:
            if os.path.exists(self.base_config_path):
                with open(self.base_config_path, 'r', encoding='utf-8') as f:
                    self.base_config = yaml.safe_load(f)
                logger.info(f"基础配置加载成功: {self.base_config_path}")
        except Exception as e:
            logger.error(f"基础配置加载失败: {e}")
            self.base_config = {}
        
        # 加载Pipeline配置
        try:
            if os.path.exists(self.pipeline_config_path):
                with open(self.pipeline_config_path, 'r', encoding='utf-8') as f:
                    self.pipeline_config = yaml.safe_load(f)
                logger.info(f"Pipeline配置加载成功: {self.pipeline_config_path}")
        except Exception as e:
            logger.error(f"Pipeline配置加载失败: {e}")
            self.pipeline_config = {}
    
    def load_orm_configs(self):
        """加载ORM相关配置"""
        # 加载ORM定义
        try:
            if os.path.exists(self.orm_def_path):
                with open(self.orm_def_path, 'r', encoding='utf-8') as f:
                    self.orm_config = yaml.safe_load(f)
                logger.info(f"ORM配置加载成功: {self.orm_def_path}")
            else:
                logger.warning(f"ORM配置文件不存在: {self.orm_def_path}")
                self.orm_config = {}
        except Exception as e:
            logger.error(f"ORM配置加载失败: {e}")
            self.orm_config = {}
        
        # 加载ORM元数据
        try:
            if os.path.exists(self.orm_metadata_path):
                with open(self.orm_metadata_path, 'r', encoding='utf-8') as f:
                    self.orm_metadata = yaml.safe_load(f)
                logger.info(f"ORM元数据加载成功: {self.orm_metadata_path}")
            else:
                logger.warning(f"ORM元数据文件不存在: {self.orm_metadata_path}")
                self.orm_metadata = {}
        except Exception as e:
            logger.error(f"ORM元数据加载失败: {e}")
            self.orm_metadata = {}
        
        # 加载ORM权重
        try:
            if os.path.exists(self.orm_weights_path):
                with open(self.orm_weights_path, 'r', encoding='utf-8') as f:
                    self.orm_weights = yaml.safe_load(f)
                logger.info(f"ORM权重配置加载成功: {self.orm_weights_path}")
            else:
                logger.warning(f"ORM权重文件不存在: {self.orm_weights_path}")
                self.orm_weights = {}
        except Exception as e:
            logger.error(f"ORM权重配置加载失败: {e}")
            self.orm_weights = {}
    
    def get(self, key: str, default: Any = None, config_type: str = "base") -> Any:
        """获取配置值（扩展支持ORM配置）"""
        config_map = {
            "base": self.base_config,
            "pipeline": self.pipeline_config,
            "orm": self.orm_config,
            "orm_metadata": self.orm_metadata,
            "orm_weights": self.orm_weights
        }
        
        config = config_map.get(config_type, self.base_config)
        
        keys = key.split('.')
        value = config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def get_orm_entities(self) -> List[str]:
        """获取ORM实体列表"""
        try:
            # 尝试从权重配置中获取实体列表
            entities_config = self.get("base_object_hierarchy.entity", {}, "orm_weights")
            if entities_config and "min_count" in entities_config:
                # 返回默认实体列表
                return ["客户", "订单", "产品", "用户", "供应商", "员工", "部门", "项目"]
            else:
                # 从元数据或其他地方推断
                return ["客户", "订单", "产品", "用户", "供应商", "员工"]
        except Exception as e:
            logger.warning(f"获取实体列表失败: {e}")
            return ["客户", "订单", "产品", "用户"]
    
    def get_orm_field_types(self) -> List[str]:
        """获取ORM字段类型列表"""
        try:
            # 首先从元数据中获取stdSqlType的scope
            scope = self.get("base_object_hierarchy.entity.column.stdSqlType.scope", [], "orm_metadata")
            if scope:
                return [item["value"] for item in scope if "value" in item]
            
            # 如果元数据没有，从权重配置中获取
            fixed_scope = self.get("base_object_hierarchy.entity.column.stdSqlType.fixed_scope", [], "orm_weights")
            if fixed_scope:
                return fixed_scope
            
            # 默认字段类型
            return ["VARCHAR(50)", "INT", "DECIMAL(10,2)", "DATETIME", "TEXT", "BOOLEAN", "BIGINT"]
            
        except Exception as e:
            logger.warning(f"获取字段类型失败: {e}")
            return ["VARCHAR(50)", "INT", "DECIMAL(10,2)", "DATETIME", "TEXT", "BOOLEAN"]
    
    def get_field_names_by_category(self) -> Dict[str, List[str]]:
        """获取按类别分组的字段名"""
        # 可以从配置中获取，这里先提供默认值
        return {
            "标识类": ["ID", "编号", "代码", "序号", "等级"],
            "名称类": ["名称", "姓名", "标题", "title", "状态"], 
            "状态类": ["状态", "状况", "情况", "condition", "类型"],
            "时间类": ["时间", "日期", "创建时间", "更新时间"],
            "描述类": ["描述", "说明", "备注", "注释"],
            "数量类": ["数量", "总数", "计数", "count"],
            "金额类": ["金额", "价格", "费用", "cost"]
        }
    
    def get_weight(self, path: str, default: float = 1.0) -> float:
        """获取权重值"""
        weight = self.get(path, default, "orm_weights")
        return float(weight) if weight is not None else default
    
    def get_crud_weights(self, field_path: str) -> Dict[str, float]:
        """获取CRUD操作权重"""
        # 获取字段配置
        crud_config = self.get(f"{field_path}", {}, "orm_weights")
        
        if isinstance(crud_config, dict):
            # 如果配置中有权重信息
            weights = {}
            
            # 获取create权重
            create_weight = crud_config.get("create", {})
            if isinstance(create_weight, dict):
                weights["create"] = float(create_weight.get("weight", 25))
            else:
                weights["create"] = float(create_weight) if create_weight else 25.0
            
            # 获取其他操作权重
            weights["read"] = float(crud_config.get("read", {}).get("weight", 25) if isinstance(crud_config.get("read"), dict) else crud_config.get("read", 25))
            weights["update"] = float(crud_config.get("update", {}).get("weight", 25) if isinstance(crud_config.get("update"), dict) else crud_config.get("update", 25))
            weights["delete"] = float(crud_config.get("delete", {}).get("weight", 25) if isinstance(crud_config.get("delete"), dict) else crud_config.get("delete", 25))
            
            return weights
        else:
            # 默认权重
            return {"create": 40.0, "read": 25.0, "update": 20.0, "delete": 15.0}
    
    def get_generation_target_count(self) -> int:
        """获取总目标生成数量"""
        return self.get("global.generated.total_number", 10000, "orm_weights")
    
    def get_semantic_strategy_weights(self) -> Dict[str, float]:
        """获取语义策略权重"""
        try:
            return {
                "atomic": self.get("semantic_matrix.intent_strategy.atomic.weight", 35.0, "orm_weights"),
                "composite": self.get("semantic_matrix.intent_strategy.composite.weight", 30.0, "orm_weights"),
                "sequence": self.get("semantic_matrix.intent_strategy.sequence.weight", 20.0, "orm_weights"),
                "implicit": self.get("semantic_matrix.intent_strategy.implicit.weight", 15.0, "orm_weights")
            }
        except Exception as e:
            logger.warning(f"获取语义策略权重失败: {e}")
            return {"atomic": 35.0, "composite": 30.0, "sequence": 20.0, "implicit": 15.0}
    
    def get_expression_layer_weights(self) -> Dict[str, float]:
        """获取表达层权重"""
        try:
            return {
                "lexical": self.get("expression_matrix.lexical_layer.weight", 45.0, "orm_weights"),
                "syntactic": self.get("expression_matrix.syntactic_layer.weight", 30.0, "orm_weights"),
                "discourse": self.get("expression_matrix.discourse_layer.weight", 15.0, "orm_weights"),
                "multilingual": self.get("expression_matrix.multilingual_layer.weight", 10.0, "orm_weights")
            }
        except Exception as e:
            logger.warning(f"获取表达层权重失败: {e}")
            return {"lexical": 45.0, "syntactic": 30.0, "discourse": 15.0, "multilingual": 10.0}
    
    def get_intent_type_weights(self) -> Dict[str, float]:
        """获取意图类型权重（从字段操作映射）"""
        try:
            # 从column相关配置推导意图权重
            column_config = self.get("base_object_hierarchy.entity.column", {}, "orm_weights")
            
            if column_config:
                # 基于字段操作权重计算意图权重
                name_weight = self.get_weight("base_object_hierarchy.entity.column.name.weight", 8.53)
                
                crud_weights = self.get_crud_weights("base_object_hierarchy.entity.column.name")
                
                return {
                    "ADD_COLUMN": name_weight * crud_weights["create"] / 100,
                    "DELETE_COLUMN": name_weight * crud_weights["delete"] / 100,
                    "MODIFY_COLUMN": name_weight * crud_weights["update"] / 100,
                    "QUERY_COLUMN": name_weight * crud_weights["read"] / 100,
                    "ENABLE_SOFT_DELETE": self.get_weight("base_object_hierarchy.entity.useLogicalDelete.weight", 5.0)
                }
            else:
                # 默认权重
                return {
                    "ADD_COLUMN": 35.0,
                    "DELETE_COLUMN": 25.0, 
                    "MODIFY_COLUMN": 20.0,
                    "ENABLE_SOFT_DELETE": 20.0
                }
        except Exception as e:
            logger.warning(f"获取意图类型权重失败: {e}")
            return {"ADD_COLUMN": 35.0, "DELETE_COLUMN": 25.0, "MODIFY_COLUMN": 20.0, "ENABLE_SOFT_DELETE": 20.0}
    
    def get_robustness_weights(self) -> Dict[str, float]:
        """获取鲁棒性矩阵权重"""
        try:
            return {
                "noise_layer": self.get("robustness_matrix.noise_layer.weight", 40.0, "orm_weights"),
                "adversarial_layer": self.get("robustness_matrix.adversarial_layer.weight", 35.0, "orm_weights"),
                "negative_samples_layer": self.get("robustness_matrix.negative_samples_layer.weight", 25.0, "orm_weights")
            }
        except Exception as e:
            logger.warning(f"获取鲁棒性权重失败: {e}")
            return {"noise_layer": 40.0, "adversarial_layer": 35.0, "negative_samples_layer": 25.0}
    
    def get_pipeline_mode(self) -> str:
        """获取Pipeline模式"""
        return self.get("pipeline.mode", "basic", "pipeline")
    
    def get_generation_config(self, mode: Optional[str] = None) -> Dict[str, Any]:
        """获取生成配置"""
        if mode is None:
            mode = self.get_pipeline_mode()
            
        if mode == "enhanced":
            return self.get("enhanced_mode", {}, "pipeline")
        elif mode == "basic":
            return self.get("basic_mode", {}, "pipeline")
        else:  # hybrid
            return {
                **self.get("basic_mode", {}, "pipeline"),
                **self.get("enhanced_mode", {}, "pipeline")
            }
    
    def validate_orm_configs(self) -> Dict[str, bool]:
        """验证ORM配置文件的完整性"""
        validation_results = {}
        
        # 检查文件是否存在
        config_files = {
            "orm_def": self.orm_def_path,
            "orm_metadata": self.orm_metadata_path,
            "orm_weights": self.orm_weights_path
        }
        
        for name, path in config_files.items():
            validation_results[f"{name}_exists"] = os.path.exists(path)
        
        # 检查关键配置是否存在
        validation_results["has_entities"] = len(self.get_orm_entities()) > 0
        validation_results["has_field_types"] = len(self.get_orm_field_types()) > 0
        validation_results["has_target_count"] = self.get_generation_target_count() > 0
        
        # 检查权重配置
        semantic_weights = self.get_semantic_strategy_weights()
        validation_results["has_semantic_weights"] = sum(semantic_weights.values()) > 0
        
        return validation_results
    
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要信息"""
        return {
            "files_loaded": {
                "base_config": bool(self.base_config),
                "pipeline_config": bool(self.pipeline_config),
                "orm_config": bool(self.orm_config),
                "orm_metadata": bool(self.orm_metadata),
                "orm_weights": bool(self.orm_weights)
            },
            "entities_count": len(self.get_orm_entities()),
            "field_types_count": len(self.get_orm_field_types()),
            "target_generation_count": self.get_generation_target_count(),
            "semantic_strategies": list(self.get_semantic_strategy_weights().keys()),
            "intent_types": list(self.get_intent_type_weights().keys())
        }

# 全局配置管理器实例
config_manager = ConfigManager()