"""
日志配置工具
"""

import sys
from loguru import logger
from src.utils.config import config_manager

def setup_logger():
    """设置日志配置"""
    
    # 移除默认handler
    logger.remove()
    
    # 获取日志配置
    log_level = config_manager.get("logging.level", "INFO")
    log_format = config_manager.get("logging.format", 
        "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}")
    log_file = config_manager.get("logging.file_path", "./logs/app.log")
    
    # 控制台输出
    logger.add(
        sys.stdout,
        format=log_format,
        level=log_level,
        colorize=True
    )
    
    # 文件输出
    logger.add(
        log_file,
        format=log_format,
        level=log_level,
        rotation=config_manager.get("logging.rotation", "10 MB"),
        retention=config_manager.get("logging.retention", "7 days"),
        encoding="utf-8"
    )
    
    logger.info("日志系统初始化完成")

# 初始化日志
setup_logger()