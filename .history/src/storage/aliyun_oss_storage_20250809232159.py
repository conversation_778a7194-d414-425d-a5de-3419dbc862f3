#!/usr/bin/env python3
"""
阿里云OSS存储适配器
用于将生成的数据上传到阿里云对象存储
"""

import os
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
import oss2
from oss2.credentials import EnvironmentVariableCredentialsProvider

logger = logging.getLogger(__name__)

class AliyunOSSStorage:
    """阿里云OSS存储管理器"""
    
    def __init__(self, 
                 bucket_name: str,
                 endpoint: str = None,
                 access_key_id: str = None,
                 access_key_secret: str = None,
                 prefix: str = "intent-data"):
        """
        初始化OSS存储
        
        Args:
            bucket_name: OSS存储桶名称
            endpoint: OSS访问域名 (如: oss-cn-hangzhou.aliyuncs.com)
            access_key_id: 访问密钥ID (可从环境变量获取)
            access_key_secret: 访问密钥Secret (可从环境变量获取)
            prefix: 存储路径前缀
        """
        self.bucket_name = bucket_name
        self.prefix = prefix
        
        # 从环境变量或参数获取认证信息
        if access_key_id and access_key_secret:
            auth = oss2.Auth(access_key_id, access_key_secret)
        else:
            # 使用环境变量认证
            auth = oss2.ProviderAuth(EnvironmentVariableCredentialsProvider())
        
        # 设置endpoint
        if not endpoint:
            endpoint = os.getenv('ALIYUN_OSS_ENDPOINT', 'oss-cn-hangzhou.aliyuncs.com')
        
        # 创建Bucket对象
        self.bucket = oss2.Bucket(auth, endpoint, bucket_name)
        
        logger.info(f"OSS存储初始化完成: {bucket_name}")
    
    def upload_json_data(self, data: Dict[str, Any], 
                        filename: str, 
                        folder: str = "training-data") -> str:
        """
        上传JSON数据到OSS
        
        Args:
            data: 要上传的数据
            filename: 文件名
            folder: 文件夹名称
            
        Returns:
            上传后的OSS路径
        """
        try:
            # 构建OSS路径
            timestamp = datetime.now().strftime("%Y%m%d")
            oss_key = f"{self.prefix}/{folder}/{timestamp}/{filename}"
            
            # 转换为JSON字符串
            json_str = json.dumps(data, ensure_ascii=False, indent=2)
            
            # 上传到OSS
            result = self.bucket.put_object(oss_key, json_str.encode('utf-8'))
            
            if result.status == 200:
                logger.info(f"✅ JSON数据上传成功: {oss_key}")
                return oss_key
            else:
                logger.error(f"❌ JSON数据上传失败: {result.status}")
                return None
                
        except Exception as e:
            logger.error(f"❌ 上传JSON数据异常: {e}")
            return None
    
    def upload_file(self, local_file_path: str, 
                   oss_file_path: str = None,
                   folder: str = "training-data") -> str:
        """
        上传本地文件到OSS
        
        Args:
            local_file_path: 本地文件路径
            oss_file_path: OSS文件路径 (可选)
            folder: 文件夹名称
            
        Returns:
            上传后的OSS路径
        """
        try:
            local_path = Path(local_file_path)
            if not local_path.exists():
                logger.error(f"❌ 本地文件不存在: {local_file_path}")
                return None
            
            # 构建OSS路径
            if not oss_file_path:
                timestamp = datetime.now().strftime("%Y%m%d")
                oss_file_path = f"{self.prefix}/{folder}/{timestamp}/{local_path.name}"
            
            # 上传文件
            result = self.bucket.put_object_from_file(oss_file_path, local_file_path)
            
            if result.status == 200:
                logger.info(f"✅ 文件上传成功: {local_file_path} -> {oss_file_path}")
                return oss_file_path
            else:
                logger.error(f"❌ 文件上传失败: {result.status}")
                return None
                
        except Exception as e:
            logger.error(f"❌ 上传文件异常: {e}")
            return None
    
    def upload_training_batch(self, 
                             samples: List[Dict[str, Any]], 
                             quality_report: Dict[str, Any],
                             metadata: Dict[str, Any] = None) -> Dict[str, str]:
        """
        批量上传训练数据
        
        Args:
            samples: 训练样本列表
            quality_report: 质量报告
            metadata: 元数据信息
            
        Returns:
            上传结果字典 {文件类型: OSS路径}
        """
        upload_results = {}
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        try:
            # 1. 上传训练样本
            samples_filename = f"training_samples_{timestamp}.json"
            samples_path = self.upload_json_data(
                {"samples": samples, "count": len(samples)}, 
                samples_filename
            )
            if samples_path:
                upload_results["samples"] = samples_path
            
            # 2. 上传质量报告
            report_filename = f"quality_report_{timestamp}.json"
            report_path = self.upload_json_data(quality_report, report_filename, "reports")
            if report_path:
                upload_results["quality_report"] = report_path
            
            # 3. 上传元数据
            if metadata:
                metadata_filename = f"metadata_{timestamp}.json"
                metadata_path = self.upload_json_data(metadata, metadata_filename, "metadata")
                if metadata_path:
                    upload_results["metadata"] = metadata_path
            
            # 4. 按意图类型分类上传
            intent_groups = self._group_by_intent_type(samples)
            for intent_type, intent_samples in intent_groups.items():
                intent_filename = f"samples_{intent_type.lower()}_{timestamp}.json"
                intent_path = self.upload_json_data(
                    {"intent_type": intent_type, "samples": intent_samples}, 
                    intent_filename,
                    f"by-intent/{intent_type.lower()}"
                )
                if intent_path:
                    upload_results[f"intent_{intent_type}"] = intent_path
            
            logger.info(f"🎉 批量上传完成，共上传 {len(upload_results)} 个文件")
            return upload_results
            
        except Exception as e:
            logger.error(f"❌ 批量上传失败: {e}")
            return upload_results
    
    def _group_by_intent_type(self, samples: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """按意图类型分组样本"""
        groups = {}
        for sample in samples:
            # 假设样本中有intent_type字段
            intent_type = sample.get("intent_type", "UNKNOWN")
            if intent_type not in groups:
                groups[intent_type] = []
            groups[intent_type].append(sample)
        return groups
    
    def list_files(self, folder: str = None) -> List[str]:
        """列出OSS中的文件"""
        try:
            prefix = f"{self.prefix}/"
            if folder:
                prefix += f"{folder}/"
            
            files = []
            for obj in oss2.ObjectIterator(self.bucket, prefix=prefix):
                files.append(obj.key)
            
            return files
        except Exception as e:
            logger.error(f"❌ 列出文件失败: {e}")
            return []
    
    def download_file(self, oss_path: str, local_path: str) -> bool:
        """从OSS下载文件"""
        try:
            self.bucket.get_object_to_file(oss_path, local_path)
            logger.info(f"✅ 文件下载成功: {oss_path} -> {local_path}")
            return True
        except Exception as e:
            logger.error(f"❌ 文件下载失败: {e}")
            return False
    
    def get_file_url(self, oss_path: str, expires: int = 3600) -> str:
        """获取文件的临时访问URL"""
        try:
            url = self.bucket.sign_url('GET', oss_path, expires)
            return url
        except Exception as e:
            logger.error(f"❌ 生成访问URL失败: {e}")
            return None
