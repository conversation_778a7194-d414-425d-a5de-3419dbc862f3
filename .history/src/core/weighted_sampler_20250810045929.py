"""
增强的加权采样器：保留现有功能 + 添加自适应采样和分布监控
遵循KISS原则，提供简单高效的加权采样功能，同时支持高级采样策略
"""

import random
import numpy as np
from typing import Dict, List, Any, Optional, Tuple, Set
from loguru import logger
from dataclasses import dataclass, field
from collections import defaultdict
import json
import time
from pathlib import Path

# ==================== 原有的基础加权采样器（保留不变） ====================

class WeightedSampler:
    """基础加权采样器 - 保留原有的KISS设计原则"""
    
    @staticmethod
    def weighted_choice(choices: List[str], weights: List[float]) -> str:
        """基于权重的随机选择
        
        Args:
            choices: 选择项列表
            weights: 对应的权重列表
            
        Returns:
            随机选中的项目
        """
        if not choices:
            return ""
            
        if not weights or len(choices) != len(weights):
            logger.warning("选择项和权重数量不匹配，使用均匀分布")
            return random.choice(choices)
        
        total_weight = sum(weights)
        if total_weight <= 0:
            logger.warning("权重总和为0或负数，使用均匀分布")
            return random.choice(choices)
        
        # 归一化权重并累积
        rand_val = random.random() * total_weight
        cumulative = 0
        
        for i, weight in enumerate(weights):
            cumulative += weight
            if rand_val <= cumulative:
                return choices[i]
        
        # 兜底返回最后一个
        return choices[-1]
    
    @staticmethod
    def weighted_choice_dict(weight_dict: Dict[str, float]) -> str:
        """基于字典权重的随机选择
        
        Args:
            weight_dict: 键为选择项，值为权重的字典
            
        Returns:
            随机选中的键
        """
        if not weight_dict:
            return ""
            
        choices = list(weight_dict.keys())
        weights = list(weight_dict.values())
        return WeightedSampler.weighted_choice(choices, weights)
    
    @staticmethod
    def sample_by_crud_weights(intent_types: List[str], 
                              crud_weights: Dict[str, float], 
                              total_count: int) -> Dict[str, int]:
        """基于CRUD权重分配样本数量
        
        Args:
            intent_types: 意图类型列表
            crud_weights: CRUD操作权重 {"create": 40, "read": 25, "update": 20, "delete": 15}
            total_count: 总样本数量
            
        Returns:
            每种意图类型分配的样本数量
        """
        if not intent_types:
            return {}
            
        if not crud_weights or total_count <= 0:
            # 均匀分配
            count_per_type = total_count // len(intent_types)
            return {intent_type: count_per_type for intent_type in intent_types}
        
        # 简化映射：根据意图类型映射到CRUD操作
        intent_to_crud_mapping = {
            "ADD_COLUMN": "create",
            "ADD_RELATIONSHIP": "create", 
            "ADD_ENTITY": "create",
            "DELETE_COLUMN": "delete",
            "DELETE_RELATIONSHIP": "delete",
            "DELETE_ENTITY": "delete",
            "MODIFY_COLUMN": "update",
            "UPDATE_ENTITY": "update",
            "QUERY_ENTITY": "read"
        }
        
        # 计算各意图类型的权重
        intent_weights = {}
        for intent_type in intent_types:
            crud_operation = intent_to_crud_mapping.get(intent_type, "create")
            intent_weights[intent_type] = crud_weights.get(crud_operation, 25.0)
        
        # 按权重分配样本数量
        total_weight = sum(intent_weights.values())
        result = {}
        allocated = 0
        
        for i, (intent_type, weight) in enumerate(intent_weights.items()):
            if i == len(intent_weights) - 1:  # 最后一个取剩余
                result[intent_type] = total_count - allocated
            else:
                count = int(total_count * weight / total_weight)
                result[intent_type] = count
                allocated += count
        
        logger.info(f"CRUD权重分配结果: {result}")
        return result
    
    @staticmethod
    def sample_by_semantic_weights(weights: Dict[str, float], 
                                 total_count: int) -> Dict[str, int]:
        """基于语义权重分配样本
        
        Args:
            weights: 语义策略权重字典
            total_count: 总样本数量
            
        Returns:
            各策略分配的样本数量
        """
        if not weights or total_count <= 0:
            return {}
        
        total_weight = sum(weights.values())
        if total_weight <= 0:
            return {key: 0 for key in weights.keys()}
        
        result = {}
        allocated = 0
        
        items = list(weights.items())
        for i, (key, weight) in enumerate(items):
            if i == len(items) - 1:  # 最后一个取剩余
                result[key] = total_count - allocated
            else:
                count = int(total_count * weight / total_weight)
                result[key] = count
                allocated += count
        
        return result


# ==================== 新增的高级采样功能 ====================

@dataclass
class SamplingMetrics:
    """采样指标数据结构"""
    strategy: str
    samples_count: int
    average_quality: float = 0.0
    error_rate: float = 0.0
    timestamp: float = field(default_factory=time.time)
    success_rate: float = 1.0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "strategy": self.strategy,
            "samples_count": self.samples_count,
            "average_quality": self.average_quality,
            "error_rate": self.error_rate,
            "timestamp": self.timestamp,
            "success_rate": self.success_rate
        }


class DistributionSampler:
    """分布采样器 - 用于监控和优化采样分布"""
    
    def __init__(self):
        self.sampling_history = defaultdict(int)
        self.total_samples = 0
        self.quality_history = defaultdict(list)
        
    def record_sample(self, category: str, count: int = 1, quality_score: float = None):
        """记录采样情况
        
        Args:
            category: 样本类别
            count: 样本数量
            quality_score: 质量分数（可选）
        """
        self.sampling_history[category] += count
        self.total_samples += count
        
        if quality_score is not None:
            self.quality_history[category].append(quality_score)
        
        logger.debug(f"记录采样: {category} +{count}, 总计: {self.sampling_history[category]}")
    
    def get_sampling_statistics(self) -> Dict[str, Any]:
        """获取采样统计信息
        
        Returns:
            包含总数、分布和熵值的统计信息
        """
        return {
            "total_samples": self.total_samples,
            "distribution": dict(self.sampling_history),
            "entropy": self._calculate_entropy(),
            "categories_count": len(self.sampling_history),
            "quality_stats": self._get_quality_statistics()
        }
    
    def _calculate_entropy(self) -> float:
        """计算分布熵值
        
        Returns:
            分布熵值，越高表示分布越均匀
        """
        if not self.sampling_history or self.total_samples == 0:
            return 0.0
        
        entropy = 0.0
        for count in self.sampling_history.values():
            if count > 0:
                p = count / self.total_samples
                entropy -= p * np.log2(p)
        
        return float(entropy)
    
    def _get_quality_statistics(self) -> Dict[str, Dict[str, float]]:
        """获取质量统计信息"""
        quality_stats = {}
        
        for category, scores in self.quality_history.items():
            if scores:
                quality_stats[category] = {
                    "mean": float(np.mean(scores)),
                    "std": float(np.std(scores)),
                    "min": float(np.min(scores)),
                    "max": float(np.max(scores)),
                    "count": len(scores)
                }
        
        return quality_stats
    
    def get_distribution_balance_score(self) -> float:
        """获取分布均衡分数
        
        Returns:
            0-1之间的分数，1表示完全均匀分布
        """
        if len(self.sampling_history) <= 1:
            return 1.0
        
        max_entropy = np.log2(len(self.sampling_history))
        current_entropy = self._calculate_entropy()
        
        return float(current_entropy / max_entropy) if max_entropy > 0 else 0.0
    
    def suggest_rebalancing(self, target_distribution: Dict[str, float] = None) -> Dict[str, str]:
        """建议重新平衡策略
        
        Args:
            target_distribution: 目标分布（可选）
            
        Returns:
            重新平衡建议
        """
        suggestions = {}
        current_distribution = self.get_current_distribution_ratios()
        
        if target_distribution:
            for category, target_ratio in target_distribution.items():
                current_ratio = current_distribution.get(category, 0.0)
                diff = target_ratio - current_ratio
                
                if abs(diff) > 0.1:  # 10%的差异阈值
                    if diff > 0:
                        suggestions[category] = f"需要增加 {diff:.1%} 的采样比例"
                    else:
                        suggestions[category] = f"需要减少 {abs(diff):.1%} 的采样比例"
        
        return suggestions
    
    def get_current_distribution_ratios(self) -> Dict[str, float]:
        """获取当前分布比例"""
        if self.total_samples == 0:
            return {}
        
        return {
            category: count / self.total_samples 
            for category, count in self.sampling_history.items()
        }
    
    def reset_statistics(self):
        """重置统计信息"""
        self.sampling_history.clear()
        self.quality_history.clear()
        self.total_samples = 0
        logger.info("采样统计信息已重置")


class AdaptiveSampler:
    """自适应采样器 - 基于历史表现动态调整采样策略"""
    
    def __init__(self, learning_rate: float = 0.1, memory_size: int = 100):
        self.learning_rate = learning_rate
        self.memory_size = memory_size
        self.quality_history = defaultdict(list)
        self.performance_weights = {}
        self.error_rates = defaultdict(float)
        self.min_samples_for_adaptation = 5
        self.metrics_history = defaultdict(list)
        
    def record_sample_quality(self, strategy: str, quality_score: float, 
                            success: bool = True):
        """记录采样策略的质量分数
        
        Args:
            strategy: 策略名称
            quality_score: 质量分数 (0-1)
            success: 是否成功生成
        """
        # 记录质量分数
        if strategy not in self.quality_history:
            self.quality_history[strategy] = []
        
        self.quality_history[strategy].append(quality_score)
        
        # 保持历史记录在合理范围内
        if len(self.quality_history[strategy]) > self.memory_size:
            self.quality_history[strategy] = self.quality_history[strategy][-self.memory_size:]
        
        # 更新错误率
        if not success:
            total_attempts = len(self.quality_history[strategy])
            errors = sum(1 for score in self.quality_history[strategy] if score < 0.5)
            self.error_rates[strategy] = errors / total_attempts if total_attempts > 0 else 0.0
        
        # 记录指标
        metrics = SamplingMetrics(
            strategy=strategy,
            samples_count=1,
            average_quality=quality_score,
            error_rate=self.error_rates[strategy],
            success_rate=1.0 if success else 0.0
        )
        self.metrics_history[strategy].append(metrics)
        
        # 更新性能权重
        self._update_performance_weights(strategy)
        
        logger.debug(f"记录 {strategy} 质量: {quality_score:.3f}, 错误率: {self.error_rates[strategy]:.3f}")
    
    def _update_performance_weights(self, strategy: str):
        """更新策略的性能权重
        
        Args:
            strategy: 策略名称
        """
        history = self.quality_history[strategy]
        if len(history) < self.min_samples_for_adaptation:
            # 样本不足，使用默认权重
            self.performance_weights[strategy] = 1.0
            return
        
        # 计算指数移动平均，给最近的样本更高权重
        weights = np.array([0.9 ** (len(history) - i - 1) for i in range(len(history))])
        weights = weights / weights.sum()
        
        weighted_average = np.average(history, weights=weights)
        self.performance_weights[strategy] = float(weighted_average)
        
        logger.debug(f"更新 {strategy} 性能权重: {weighted_average:.3f}")
    
    def get_adaptive_weights(self, base_weights: Dict[str, float]) -> Dict[str, float]:
        """获取自适应调整后的权重
        
        Args:
            base_weights: 基础权重
            
        Returns:
            调整后的权重
        """
        adaptive_weights = base_weights.copy()
        
        # 应用性能调整
        for strategy, base_weight in base_weights.items():
            performance_weight = self.performance_weights.get(strategy, 1.0)
            error_rate = self.error_rates.get(strategy, 0.0)
            
            # 性能调整：性能好的策略权重增加
            performance_adjustment = 1.0 + self.learning_rate * (performance_weight - 1.0)
            
            # 错误率惩罚：错误率高的策略权重减少
            error_penalty = 1.0 - (error_rate * 0.5)  # 最多减少50%
            
            # 综合调整
            total_adjustment = performance_adjustment * error_penalty
            adaptive_weights[strategy] = base_weight * total_adjustment
        
        # 归一化权重
        total_weight = sum(adaptive_weights.values())
        if total_weight > 0:
            for strategy in adaptive_weights:
                adaptive_weights[strategy] /= total_weight
        
        logger.info(f"自适应权重调整: {adaptive_weights}")
        return adaptive_weights
    
    def suggest_sampling_strategy(self, available_strategies: List[str]) -> str:
        """建议最佳采样策略
        
        Args:
            available_strategies: 可用的策略列表
            
        Returns:
            推荐的策略名称
        """
        if not available_strategies:
            return ""
        
        # 基于历史性能选择最佳策略
        best_strategy = available_strategies[0]
        best_score = 0.0
        
        for strategy in available_strategies:
            if strategy in self.performance_weights:
                # 考虑性能权重和错误率
                performance_score = self.performance_weights[strategy]
                error_penalty = 1.0 - self.error_rates.get(strategy, 0.0)
                combined_score = performance_score * error_penalty
                
                if combined_score > best_score:
                    best_score = combined_score
                    best_strategy = strategy
            else:
                # 未知策略给予中等评分
                if 0.5 > best_score:
                    best_score = 0.5
                    best_strategy = strategy
        
        logger.info(f"推荐采样策略: {best_strategy} (分数: {best_score:.3f})")
        return best_strategy
    
    def mine_hard_examples(self, samples_data: List[Dict[str, Any]], 
                          confidence_threshold: float = 0.5) -> List[Dict[str, Any]]:
        """挖掘难例样本
        
        Args:
            samples_data: 样本数据列表
            confidence_threshold: 置信度阈值
            
        Returns:
            难例样本列表
        """
        hard_examples = []
        
        for sample in samples_data:
            confidence = sample.get("confidence", 1.0)
            quality_score = sample.get("quality_score", 1.0)
            
            # 多维度判断难例
            is_hard = (
                confidence < confidence_threshold or
                quality_score < confidence_threshold or
                sample.get("error_count", 0) > 0
            )
            
            if is_hard:
                sample["difficulty_score"] = 1.0 - min(confidence, quality_score)
                hard_examples.append(sample)
        
        # 按难度排序
        hard_examples.sort(key=lambda x: x.get("difficulty_score", 0), reverse=True)
        
        logger.info(f"挖掘到 {len(hard_examples)} 个难例样本")
        return hard_examples
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告
        
        Returns:
            包含各策略性能统计的报告
        """
        report = {
            "strategies_count": len(self.quality_history),
            "total_samples": sum(len(history) for history in self.quality_history.values()),
            "performance_weights": self.performance_weights.copy(),
            "error_rates": dict(self.error_rates),
            "strategy_statistics": {},
            "recommendations": {}
        }
        
        for strategy, history in self.quality_history.items():
            if history:
                report["strategy_statistics"][strategy] = {
                    "sample_count": len(history),
                    "average_quality": float(np.mean(history)),
                    "quality_std": float(np.std(history)),
                    "min_quality": float(np.min(history)),
                    "max_quality": float(np.max(history)),
                    "error_rate": self.error_rates.get(strategy, 0.0),
                    "performance_weight": self.performance_weights.get(strategy, 1.0)
                }
                
                # 生成建议
                avg_quality = np.mean(history)
                error_rate = self.error_rates.get(strategy, 0.0)
                
                if avg_quality < 0.6:
                    report["recommendations"][strategy] = "质量偏低，建议检查生成逻辑"
                elif error_rate > 0.3:
                    report["recommendations"][strategy] = "错误率过高，建议优化参数"
                elif avg_quality > 0.8 and error_rate < 0.1:
                    report["recommendations"][strategy] = "表现优秀，可增加权重"
        
        return report
    
    def save_metrics(self, filepath: str):
        """保存采样指标到文件
        
        Args:
            filepath: 保存路径
        """
        metrics_data = {
            "performance_weights": self.performance_weights,
            "error_rates": dict(self.error_rates),
            "quality_history": {k: list(v) for k, v in self.quality_history.items()},
            "metrics_history": {
                k: [m.to_dict() for m in v] 
                for k, v in self.metrics_history.items()
            },
            "timestamp": time.time()
        }
        
        Path(filepath).parent.mkdir(parents=True, exist_ok=True)
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(metrics_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"采样指标已保存到: {filepath}")
    
    def load_metrics(self, filepath: str):
        """从文件加载采样指标
        
        Args:
            filepath: 文件路径
        """
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                metrics_data = json.load(f)
            
            self.performance_weights = metrics_data.get("performance_weights", {})
            self.error_rates = defaultdict(float, metrics_data.get("error_rates", {}))
            
            # 重建质量历史
            quality_data = metrics_data.get("quality_history", {})
            for strategy, history in quality_data.items():
                self.quality_history[strategy] = history[-self.memory_size:]
            
            logger.info(f"采样指标已从 {filepath} 加载")
            
        except Exception as e:
            logger.warning(f"加载采样指标失败: {e}")


# ==================== 集成到EnhancedSamplingPipeline的适配器 ====================

class EnhancedSamplingAdapter:
    """增强采样适配器 - 连接原有系统和新系统"""
    
    def __init__(self):
        self.weighted_sampler = WeightedSampler()
        self.distribution_sampler = DistributionSampler()
        self.adaptive_sampler = AdaptiveSampler()
        self.enabled_features = {
            "adaptive_sampling": True,
            "distribution_monitoring": True,
            "hard_example_mining": True
        }
        
    def set_feature_enabled(self, feature_name: str, enabled: bool):
        """启用或禁用特定功能
        
        Args:
            feature_name: 功能名称
            enabled: 是否启用
        """
        if feature_name in self.enabled_features:
            self.enabled_features[feature_name] = enabled
            logger.info(f"功能 {feature_name} {'启用' if enabled else '禁用'}")
    
    def enhanced_weighted_choice(self, choices: List[str], weights: List[float], 
                                strategy_name: str = "default") -> str:
        """增强的加权选择（支持自适应调整）
        
        Args:
            choices: 选择项列表
            weights: 权重列表
            strategy_name: 策略名称
            
        Returns:
            选中的项目
        """
        # 基础加权选择
        choice = self.weighted_sampler.weighted_choice(choices, weights)
        
        # 记录分布信息
        if self.enabled_features["distribution_monitoring"]:
            self.distribution_sampler.record_sample(choice)
        
        return choice
    
    def enhanced_semantic_sampling(self, semantic_weights: Dict[str, float], 
                                 total_count: int, 
                                 quality_feedback: Dict[str, float] = None) -> Dict[str, int]:
        """增强的语义采样（支持质量反馈调整）
        
        Args:
            semantic_weights: 语义权重
            total_count: 总数量
            quality_feedback: 质量反馈分数
            
        Returns:
            分配结果
        """
        # 应用自适应调整
        if self.enabled_features["adaptive_sampling"] and quality_feedback:
            for strategy, quality in quality_feedback.items():
                self.adaptive_sampler.record_sample_quality(strategy, quality)
            
            adjusted_weights = self.adaptive_sampler.get_adaptive_weights(semantic_weights)
        else:
            adjusted_weights = semantic_weights
        
        # 执行分配
        distribution = self.weighted_sampler.sample_by_semantic_weights(
            adjusted_weights, total_count
        )
        
        # 记录分布信息
        if self.enabled_features["distribution_monitoring"]:
            for strategy, count in distribution.items():
                self.distribution_sampler.record_sample(strategy, count)
        
        return distribution
    
    def get_comprehensive_statistics(self) -> Dict[str, Any]:
        """获取综合统计信息"""
        stats = {
            "basic_stats": {
                "enabled_features": self.enabled_features,
                "timestamp": time.time()
            }
        }
        
        if self.enabled_features["distribution_monitoring"]:
            stats["distribution_stats"] = self.distribution_sampler.get_sampling_statistics()
        
        if self.enabled_features["adaptive_sampling"]:
            stats["adaptive_stats"] = self.adaptive_sampler.get_performance_report()
        
        return stats
    
    def suggest_optimizations(self) -> List[str]:
        """建议优化措施"""
        suggestions = []
        
        if self.enabled_features["distribution_monitoring"]:
            balance_score = self.distribution_sampler.get_distribution_balance_score()
            if balance_score < 0.7:
                suggestions.append(f"分布不够均匀 (均衡分数: {balance_score:.2f})，建议调整权重配置")
        
        if self.enabled_features["adaptive_sampling"]:
            performance_report = self.adaptive_sampler.get_performance_report()
            for strategy, recommendation in performance_report.get("recommendations", {}).items():
                suggestions.append(f"{strategy}: {recommendation}")
        
        return suggestions


# ==================== 兼容性函数（保持向后兼容） ====================

def create_legacy_sampler() -> WeightedSampler:
    """创建传统采样器（向后兼容）"""
    logger.info("创建传统加权采样器（兼容模式）")
    return WeightedSampler()

def create_enhanced_sampler(enable_adaptive: bool = True, 
                          enable_monitoring: bool = True) -> EnhancedSamplingAdapter:
    """创建增强采样器
    
    Args:
        enable_adaptive: 是否启用自适应采样
        enable_monitoring: 是否启用分布监控
        
    Returns:
        增强采样适配器
    """
    adapter = EnhancedSamplingAdapter()
    adapter.set_feature_enabled("adaptive_sampling", enable_adaptive)
    adapter.set_feature_enabled("distribution_monitoring", enable_monitoring)
    
    logger.info(f"创建增强采样器 (自适应: {enable_adaptive}, 监控: {enable_monitoring})")
    return adapter


# ==================== 测试和验证函数 ====================

def test_enhanced_sampler():
    """测试增强采样器功能"""
    logger.info("开始测试增强采样器...")
    
    # 测试基础功能
    sampler = WeightedSampler()
    choices = ["A", "B", "C"]
    weights = [0.5, 0.3, 0.2]
    result = sampler.weighted_choice(choices, weights)
    logger.info(f"基础加权选择测试: {result}")
    
    # 测试增强功能
    enhanced = create_enhanced_sampler()
    
    # 模拟质量反馈
    quality_feedback = {"atomic": 0.8, "composite": 0.6, "sequence": 0.7}
    semantic_weights = {"atomic": 0.4, "composite": 0.3, "sequence": 0.3}
    
    distribution = enhanced.enhanced_semantic_sampling(
        semantic_weights, 100, quality_feedback
    )
    logger.info(f"增强语义采样测试: {distribution}")
    
    # 获取统计信息
    stats = enhanced.get_comprehensive_statistics()
    logger.info(f"统计信息测试通过，包含 {len(stats)} 个统计项")
    
    # 获取优化建议
    suggestions = enhanced.suggest_optimizations()
    logger.info(f"优化建议: {suggestions}")
    
    logger.info("增强采样器测试完成！")


if __name__ == "__main__":
    # 运行测试
    test_enhanced_sampler()