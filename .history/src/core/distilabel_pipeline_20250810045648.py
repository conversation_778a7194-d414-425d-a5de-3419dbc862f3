"""
Distilabel 主流水线文件
基于 KISS、YAGNI 和 SOLID 原则设计的数据生成系统核心

文件位置: src/core/distilabel_pipeline.py
文件作用: 整个 Distilabel 系统的核心编排器，负责组装所有步骤并管理数据流

技术背景:
- 使用 Distilabel 框架的 Pipeline 模式实现流水线编排  
- 采用工厂方法模式创建不同类型的步骤组件
- 通过依赖注入实现组件的可替换性
- 配置驱动架构确保开闭原则

技术提升方向:
- 模块化设计: 便于单元测试和维护
- 依赖注入: 使得组件可替换，提升可扩展性
- 配置化部署: 降低运维复杂度，支持多环境部署
"""

import os
import yaml
import json
import logging
from typing import List, Dict, Any, Optional, Union
from pathlib import Path
from datetime import datetime

# Distilabel 核心导入
from distilabel.pipeline import Pipeline
from distilabel.steps import KeepColumns
from distilabel.models import TransformersLLM, InferenceEndpointsLLM

# 导入现有组件 (复用原有架构)
from src.core.weighted_sampler import WeightedSampler
from src.core.data_quality import DataQualityController
from src.utils.config import config_manager

# 设置日志
logger = logging.getLogger(__name__)


class DistilabelPipelineBuilder:
    """
    Distilabel 流水线构建器
    
    技术背景:
    - 实现建造者模式，分步骤构建复杂的流水线对象
    - 使用工厂方法模式创建不同类型的模型和步骤
    - 通过配置驱动实现组件的动态装配
    
    设计优势:
    - 单一职责: 只负责流水线的构建和配置
    - 开闭原则: 新增步骤类型无需修改此类
    - 依赖倒置: 依赖于抽象接口而非具体实现
    """
    
    def __init__(self, config_path: str = "configs/distilabel_config.yaml"):
        """
        初始化流水线构建器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.config = self._load_config()
        self.pipeline = None
        
    def _load_config(self) -> Dict[str, Any]:
        """
        加载配置文件
        
        技术要点:
        - 使用 YAML 格式便于人工编辑和版本控制
        - 异常处理确保配置文件问题时有明确提示
        - 支持环境变量替换，提升部署灵活性
        """
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 环境变量替换
            config = self._replace_env_variables(config)
            
            logger.info(f"成功加载配置文件: {self.config_path}")
            return config
            
        except FileNotFoundError:
            logger.error(f"配置文件未找到: {self.config_path}")
            raise
        except yaml.YAMLError as e:
            logger.error(f"配置文件格式错误: {e}")
            raise
    
    def _replace_env_variables(self, config: Any) -> Any:
        """
        递归替换配置中的环境变量
        
        技术要点:
        - 支持 ${VAR_NAME} 格式的环境变量引用
        - 递归处理嵌套的配置结构
        - 提供默认值机制避免环境变量缺失时的错误
        """
        if isinstance(config, dict):
            return {k: self._replace_env_variables(v) for k, v in config.items()}
        elif isinstance(config, list):
            return [self._replace_env_variables(item) for item in config]
        elif isinstance(config, str) and config.startswith("${") and config.endswith("}"):
            env_var = config[2:-1]
            return os.getenv(env_var, config)  # 如果环境变量不存在，返回原值
        else:
            return config
    
    def build_pipeline(self) -> Pipeline:
        """
        构建完整的 Distilabel 流水线
        
        技术架构:
        1. 创建各个步骤组件
        2. 配置步骤之间的数据流连接
        3. 设置流水线级别的参数
        
        设计模式:
        - 工厂方法: 根据配置创建不同类型的步骤
        - 建造者模式: 分步骤构建复杂的流水线对象
        - 策略模式: 根据配置选择不同的处理策略
        """
        logger.info("开始构建 Distilabel 流水线...")
        
        # 创建 Pipeline 实例
        self.pipeline = Pipeline(
            name=self.config.get("pipeline", {}).get("name", "distilabel_pipeline"),
            description=self.config.get("pipeline", {}).get("description", "ORM意图数据生成流水线")
        )
        
        # 第1步: 创建意图生成器
        intent_generator = self._create_intent_generator()
        self.pipeline.add_step(intent_generator)
        
        # 第2步: 创建指令重写器  
        instruction_rewriter = self._create_instruction_rewriter()
        self.pipeline.add_step(instruction_rewriter)
        
        # 第3步: 创建质量评估器
        quality_evaluator = self._create_quality_evaluator()
        self.pipeline.add_step(quality_evaluator)
        
        # 第4步: 创建数据过滤器
        data_filter = self._create_data_filter()
        self.pipeline.add_step(data_filter)
        
        # 第5步: 创建列选择器 (保留需要的字段)
        column_keeper = self._create_column_keeper()
        self.pipeline.add_step(column_keeper)
        
        # 配置步骤之间的连接关系
        self._connect_pipeline_steps()
        
        logger.info("Distilabel 流水线构建完成")
        return self.pipeline
    
    def _create_intent_generator(self):
        """
        创建意图生成器步骤
        
        技术要点:
        - 动态导入步骤类，避免循环依赖
        - 传入配置参数，实现配置驱动
        - 复用现有的 WeightedSampler 组件
        """
        from src.steps.intent_generator import ConfigDrivenIntentGenerator
        
        return ConfigDrivenIntentGenerator(
            name="intent_generator",
            config_path=self.config_path,
            batch_size=self.config.get("generation", {}).get("batch_size", 16)
        )
    
    def _create_instruction_rewriter(self):
        """
        创建指令重写器步骤
        
        技术要点:
        - 根据配置选择合适的 LLM 模型
        - 配置生成参数 (temperature, max_tokens 等)
        - 支持多种模型类型 (Transformers, API, 本地)
        """
        from src.steps.instruction_rewriter import QwenInstructionRewriter
        
        # 创建 Qwen 模型
        qwen_model = self._create_qwen_model()
        
        return QwenInstructionRewriter(
            name="instruction_rewriter",
            llm=qwen_model,
            config=self.config.get("instruction_rewrite", {}),
            batch_size=self.config.get("generation", {}).get("batch_size", 16)
        )
    
    def _create_quality_evaluator(self):
        """
        创建质量评估器步骤
        
        技术要点:
        - 支持多种评估模型 (豆包、Qwen、GPT等)
        - 实现 G-Eval 评估方法论
        - 可配置的评估维度和权重
        """
        from src.steps.quality_evaluator import DouBaoQualityEvaluator
        
        # 创建评估模型
        evaluator_model = self._create_evaluator_model()
        
        return DouBaoQualityEvaluator(
            name="quality_evaluator",
            llm=evaluator_model,
            config=self.config.get("quality_evaluation", {}),
            batch_size=self.config.get("evaluation", {}).get("batch_size", 8)
        )
    
    def _create_data_filter(self):
        """
        创建数据过滤器步骤
        
        技术要点:
        - 多维度质量过滤 (分数、长度、完整性)
        - 去重处理避免数据重复
        - 统计信息记录便于监控和调优
        """
        from src.steps.data_filter import HighQualityDataFilter
        
        return HighQualityDataFilter(
            name="data_filter",
            config=self.config.get("data_filtering", {}),
            quality_controller=DataQualityController()  # 复用现有组件
        )
    
    def _create_column_keeper(self):
        """
        创建列选择器步骤
        
        技术要点:
        - 只保留训练需要的字段，减少数据体积
        - 支持动态配置输出字段列表
        - 确保输出格式的一致性
        """
        output_columns = self.config.get("output", {}).get("columns", [
            "instruction", "intent_json", "quality_score", "metadata"
        ])
        
        return KeepColumns(
            name="column_keeper",
            columns=output_columns
        )
    
    def _connect_pipeline_steps(self):
        """
        配置流水线步骤之间的连接关系
        
        技术架构:
        意图生成器 → 指令重写器 → 质量评估器 → 数据过滤器 → 列选择器
        
        数据流设计:
        - 每个步骤只传递必要的字段，减少内存占用
        - 支持字段重命名和类型转换
        - 错误处理确保数据流的稳定性
        """
        # 意图生成器 → 指令重写器
        self.pipeline.connect(
            "intent_generator",
            "instruction_rewriter",
            from_step_outputs=["intent_json", "entity", "field_name", "data_type", "semantic_strategy"]
        )
        
        # 指令重写器 → 质量评估器
        self.pipeline.connect(
            "instruction_rewriter",
            "quality_evaluator",
            from_step_outputs=["instruction", "intent_json", "rewrite_metadata"]
        )
        
        # 质量评估器 → 数据过滤器
        self.pipeline.connect(
            "quality_evaluator",
            "data_filter",
            from_step_outputs=["instruction", "intent_json", "quality_score", "evaluation_details"]
        )
        
        # 数据过滤器 → 列选择器
        self.pipeline.connect(
            "data_filter",
            "column_keeper",
            from_step_outputs=["instruction", "intent_json", "quality_score", "metadata"]
        )
    
    def _create_qwen_model(self) -> Union[TransformersLLM, InferenceEndpointsLLM]:
        """
        创建 Qwen 模型实例
        
        技术要点:
        - 支持本地 Transformers 和 API 两种模式
        - 自动选择合适的设备配置 (CPU/GPU)
        - 优化生成参数提升质量和效率
        """
        model_config = self.config.get("models", {}).get("qwen", {})
        model_type = model_config.get("type", "transformers")
        
        if model_type == "transformers":
            return TransformersLLM(
                model=model_config.get("name", "Qwen/Qwen2.5-7B-Instruct"),
                device_map="auto",
                torch_dtype="auto",
                generation_kwargs={
                    "temperature": model_config.get("temperature", 0.7),
                    "max_new_tokens": model_config.get("max_tokens", 512),
                    "do_sample": True,
                    "top_p": model_config.get("top_p", 0.9),
                    "repetition_penalty": model_config.get("repetition_penalty", 1.1)
                }
            )
        else:
            # API 模式
            return InferenceEndpointsLLM(
                endpoint_name=model_config.get("endpoint_name"),
                api_key=model_config.get("api_key"),
                generation_kwargs={
                    "temperature": model_config.get("temperature", 0.7),
                    "max_new_tokens": model_config.get("max_tokens", 512)
                }
            )
    
    def _create_evaluator_model(self) -> Union[TransformersLLM, InferenceEndpointsLLM]:
        """
        创建评估模型实例
        
        技术要点:
        - 评估模型通常使用较低的 temperature 确保一致性
        - 支持不同于生成模型的配置参数
        - 可以复用生成模型或使用专门的评估模型
        """
        model_config = self.config.get("models", {}).get("evaluator", {})
        model_type = model_config.get("type", "api")
        
        if model_type == "transformers":
            return TransformersLLM(
                model=model_config.get("name", "Qwen/Qwen2.5-7B-Instruct"),
                device_map="auto",
                torch_dtype="auto",
                generation_kwargs={
                    "temperature": model_config.get("temperature", 0.1),
                    "max_new_tokens": model_config.get("max_tokens", 256),
                    "do_sample": True,
                    "top_p": 0.95
                }
            )
        else:
            # API 模式 (如豆包)
            return InferenceEndpointsLLM(
                endpoint_name=model_config.get("endpoint_name", "doubao"),
                api_key=model_config.get("api_key"),
                generation_kwargs={
                    "temperature": model_config.get("temperature", 0.1),
                    "max_new_tokens": model_config.get("max_tokens", 256)
                }
            )
    
    def get_pipeline_info(self) -> Dict[str, Any]:
        """
        获取流水线信息，用于监控和调试
        
        返回信息包括:
        - 步骤数量和名称
        - 模型配置信息  
        - 预估的处理量和时间
        - 配置摘要
        """
        if not self.pipeline:
            return {"error": "Pipeline 未构建"}
        
        # 估算样本数量
        enhanced_config = self.config.get("enhanced_mode", {})
        samples_per_intent = enhanced_config.get("seed_samples_per_intent", 100)
        intent_types = ["ADD_COLUMN", "DELETE_COLUMN", "MODIFY_COLUMN", "ENABLE_SOFT_DELETE"]
        estimated_samples = len(intent_types) * samples_per_intent
        
        return {
            "pipeline_name": self.pipeline.name,
            "steps_count": len(self.pipeline.dag.nodes),
            "step_names": list(self.pipeline.dag.nodes),
            "estimated_samples": estimated_samples,
            "model_config": {
                "qwen": self.config.get("models", {}).get("qwen", {}).get("name"),
                "evaluator": self.config.get("models", {}).get("evaluator", {}).get("name")
            },
            "config_summary": {
                "quality_threshold": self.config.get("quality_evaluation", {}).get("min_quality_score", 0.7),
                "batch_size": self.config.get("generation", {}).get("batch_size", 16),
                "output_formats": self.config.get("output", {}).get("formats", ["json"])
            }
        }


def create_pipeline(config_path: str = "configs/distilabel_config.yaml") -> Pipeline:
    """
    工厂函数：创建 Distilabel 流水线
    
    这是一个便捷的工厂函数，封装了流水线构建的复杂性
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        构建好的 Pipeline 实例
        
    技术优势:
    - 简化客户端代码，符合 KISS 原则
    - 隐藏构建复杂性，提供简洁的接口
    - 支持单元测试和集成测试
    """
    builder = DistilabelPipelineBuilder(config_path)
    return builder.build_pipeline()


def get_pipeline_info(config_path: str = "configs/distilabel_config.yaml") -> Dict[str, Any]:
    """
    工厂函数：获取流水线信息
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        流水线信息字典
    """
    builder = DistilabelPipelineBuilder(config_path)
    return builder.get_pipeline_info()


# 如果直接运行此文件，执行简单的测试
if __name__ == "__main__":
    import sys
    
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    try:
        # 测试流水线构建
        config_path = sys.argv[1] if len(sys.argv) > 1 else "configs/distilabel_config.yaml"
        
        print("🔧 测试 Distilabel 流水线构建...")
        pipeline = create_pipeline(config_path)
        
        print("✅ 流水线构建成功!")
        
        # 显示流水线信息
        info = get_pipeline_info(config_path)
        print(f"📊 流水线信息:")
        print(f"   名称: {info['pipeline_name']}")
        print(f"   步骤数: {info['steps_count']}")
        print(f"   预估样本数: {info['estimated_samples']}")
        print(f"   主要模型: {info['model_config']['qwen']}")
        
    except Exception as e:
        print(f"❌ 流水线构建失败: {e}")
        sys.exit(1)