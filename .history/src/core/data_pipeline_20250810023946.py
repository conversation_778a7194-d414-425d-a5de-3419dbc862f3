"""
数据生成Pipeline：整合所有数据生成和质量控制流程
支持配置驱动和多策略生成
"""

import asyncio
import json
import os
from datetime import datetime
from typing import List, Dict, Any, Optional
from loguru import logger

from src.models.intent_models import TrainingSample
from src.core.enhanced_sampling_system import ReverseDataGenerator
from src.core.data_augmenter import DataAugmenter
from src.core.data_quality import DataQualityController
from src.core.weighted_sampler import WeightedSampler, DistributionSampler, AdaptiveSampler
from src.utils.config import config_manager

# 尝试导入阿里云存储管理器
try:
    from src.storage.storage_manager import AliyunStorageManager
    ALIYUN_STORAGE_AVAILABLE = True
except ImportError:
    ALIYUN_STORAGE_AVAILABLE = False
    logger.warning("阿里云存储模块未安装，将跳过云端存储功能")

class DataGenerationPipeline:
    """数据生成Pipeline（配置驱动增强版）"""
    
    def __init__(self):
        self.generator = None
        self.augmenter = None
        self.quality_controller = None
        self.weighted_sampler = WeightedSampler()
        self.distribution_sampler = DistributionSampler()
        self.adaptive_sampler = AdaptiveSampler()

        self.config_manager = config_manager
        self.output_dir = config_manager.get("data.processed_data_path", "./data/processed")

        # 初始化阿里云存储管理器
        self.aliyun_storage = None
        if ALIYUN_STORAGE_AVAILABLE:
            try:
                self.aliyun_storage = AliyunStorageManager()
                logger.info("阿里云存储管理器初始化成功")
            except Exception as e:
                logger.warning(f"阿里云存储管理器初始化失败: {e}")

        # Pipeline执行统计
        self.execution_stats = {
            "total_runs": 0,
            "successful_runs": 0,
            "failed_runs": 0,
            "last_run_time": None,
            "average_generation_time": 0.0
        }
        
    async def initialize(self):
        """初始化所有组件"""
        logger.info("初始化配置驱动数据生成Pipeline...")
        
        # 验证配置文件
        config_validation = self.config_manager.validate_orm_configs()
        logger.info(f"配置验证结果: {config_validation}")
        
        if not all([
            config_validation.get("orm_def_exists", False),
            config_validation.get("has_entities", False),
            config_validation.get("has_field_types", False)
        ]):
            logger.warning("部分配置缺失，将使用默认配置")
        
        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 初始化组件
        self.generator = ReverseDataGenerator()
        self.augmenter = DataAugmenter()
        self.quality_controller = DataQualityController()
        
        # 初始化质量控制器
        await self.quality_controller.initialize()
        
        # 获取配置摘要
        config_summary = self.config_manager.get_config_summary()
        logger.info(f"配置摘要: {config_summary}")
        
        logger.info("配置驱动数据生成Pipeline初始化完成")
    
    async def run_full_pipeline(self, 
                               samples_per_intent: int = 500,
                               adversarial_ratio: float = 0.1,
                               boundary_cases: int = 100,
                               fuzzy_samples: int = 200,
                               negative_samples: int = 100,
                               use_config_targets: bool = True) -> Dict[str, Any]:
        """运行完整的数据生成Pipeline（配置驱动版本）"""
        
        start_time = datetime.now()
        self.execution_stats["total_runs"] += 1
        
        logger.info("开始运行配置驱动数据生成Pipeline...")
        
        try:
            # Step 0: 确定生成目标
            if use_config_targets:
                total_target = self.config_manager.get_generation_target_count()
                intent_weights = self.config_manager.get_intent_type_weights()
                
                # 根据配置权重重新计算每种意图的样本数
                intent_distribution = {}
                total_weight = sum(intent_weights.values())
                
                for intent_type, weight in intent_weights.items():
                    if intent_type in self.generator.templates:
                        target_count = int(total_target * weight / total_weight)
                        intent_distribution[intent_type] = max(target_count, 50)  # 最少50个
                
                logger.info(f"配置驱动的目标分布: {intent_distribution}")
                actual_samples_per_intent = intent_distribution
            else:
                logger.info(f"使用参数指定的目标: 每种意图 {samples_per_intent} 个样本")
                actual_samples_per_intent = {
                    intent_type: samples_per_intent 
                    for intent_type in self.generator.templates.keys()
                }
            
            # Step 1: 生成基础训练样本（配置驱动）
            logger.info("Step 1: 生成配置驱动的基础训练样本...")
            base_samples = await self._generate_config_driven_samples(actual_samples_per_intent)
            logger.info(f"基础样本生成完成，数量: {len(base_samples)}")
            
            # Step 2: 生成模糊样本（基于配置权重）
            logger.info("Step 2: 生成模糊样本...")
            fuzzy_samples_list = await self._generate_weighted_fuzzy_samples(fuzzy_samples)
            logger.info(f"模糊样本生成完成，数量: {len(fuzzy_samples_list)}")
            
            # Step 3: 数据增强（根据鲁棒性配置）
            logger.info("Step 3: 生成鲁棒性增强样本...")
            robustness_samples = await self._generate_robustness_samples(
                base_samples, adversarial_ratio, boundary_cases, negative_samples
            )
            
            logger.info(f"鲁棒性样本生成完成")
            logger.info(f"   - 对抗样本: {len(robustness_samples['adversarial'])}")
            logger.info(f"   - 边界样本: {len(robustness_samples['boundary'])}")
            logger.info(f"   - 负样本: {len(robustness_samples['negative'])}")
            
            # Step 4: 合并所有样本
            logger.info("Step 4: 合并所有样本...")
            all_samples = (base_samples + fuzzy_samples_list + 
                          robustness_samples['adversarial'] +
                          robustness_samples['boundary'] + 
                          robustness_samples['negative'])
            logger.info(f"样本合并完成，总数量: {len(all_samples)}")
            
            # Step 5: 质量控制
            logger.info("Step 5: 质量控制处理...")
            
            # 验证样本
            valid_samples, validation_errors = self.quality_controller.validate_samples(all_samples)
            logger.info(f"样本验证完成，有效样本: {len(valid_samples)}")
            
            # 去重处理
            clean_samples = self.quality_controller.deduplicate_samples(valid_samples)
            logger.info(f"去重处理完成，最终样本数: {len(clean_samples)}")
            
            # Step 6: 质量评估和自适应优化
            logger.info("Step 6: 质量评估和自适应优化...")
            quality_report = self.quality_controller.generate_quality_report(
                clean_samples, validation_errors
            )
            
            # 记录质量信息用于自适应优化
            await self._record_quality_feedback(clean_samples, quality_report)
            
            # Step 7: 保存结果
            logger.info("💾 Step 7: 保存生成结果...")
            saved_files = await self.save_results(clean_samples, quality_report)
            
            # 计算执行时间
            execution_time = (datetime.now() - start_time).total_seconds()
            self._update_execution_stats(execution_time, True)
            
            # 生成Pipeline报告
            pipeline_report = {
                "timestamp": datetime.now().isoformat(),
                "execution_time_seconds": execution_time,
                "configuration": {
                    "use_config_targets": use_config_targets,
                    "target_distribution": actual_samples_per_intent,
                    "adversarial_ratio": adversarial_ratio,
                    "boundary_cases": boundary_cases,
                    "fuzzy_samples": fuzzy_samples,
                    "negative_samples": negative_samples
                },
                "generation_statistics": {
                    "base_samples": len(base_samples),
                    "fuzzy_samples": len(fuzzy_samples_list),
                    "adversarial_samples": len(robustness_samples['adversarial']),
                    "boundary_samples": len(robustness_samples['boundary']),
                    "negative_samples": len(robustness_samples['negative']),
                    "total_before_quality_control": len(all_samples),
                    "valid_samples": len(valid_samples),
                    "final_samples": len(clean_samples)
                },
                "quality_report": quality_report,
                "saved_files": saved_files,
                "config_summary": self.config_manager.get_config_summary(),
                "execution_stats": self.execution_stats
            }
            
            logger.info("配置驱动数据生成Pipeline执行完成！")
            logger.info(f"最终生成样本数: {len(clean_samples)}")
            logger.info(f"执行时间: {execution_time:.2f}秒")
            logger.info(f"保存文件: {len(saved_files)}个")
            
            self.execution_stats["successful_runs"] += 1
            return pipeline_report
            
        except Exception as e:
            self.execution_stats["failed_runs"] += 1
            self._update_execution_stats((datetime.now() - start_time).total_seconds(), False)
            logger.error(f"配置驱动数据生成Pipeline执行失败: {e}")
            raise
    
    async def _generate_config_driven_samples(self, intent_distribution: Dict[str, int]) -> List[TrainingSample]:
        """生成配置驱动的基础样本"""
        all_samples = []
        
        for intent_type, target_count in intent_distribution.items():
            if intent_type not in self.generator.templates:
                logger.warning(f"跳过未知意图类型: {intent_type}")
                continue
            
            logger.info(f"  生成 {intent_type}: {target_count} 个样本")
            try:
                # 使用配置驱动的生成方法
                samples = self.generator.generate_single_intent_samples(intent_type, target_count)
                all_samples.extend(samples)
                
                # 记录生成质量用于自适应优化
                quality_score = self._estimate_sample_quality(samples)
                self.adaptive_sampler.record_sample_quality(intent_type, quality_score)
                
            except Exception as e:
                logger.error(f"生成 {intent_type} 类型样本失败: {e}")
                continue
        
        return all_samples
    
    async def _generate_weighted_fuzzy_samples(self, target_count: int) -> List[TrainingSample]:
        """生成基于权重的模糊样本"""
        # 根据意图权重分配模糊样本
        intent_weights = self.config_manager.get_intent_type_weights()
        
        fuzzy_distribution = self.weighted_sampler.sample_by_semantic_weights(
            intent_weights, target_count
        )
        
        # 按分配比例生成模糊样本
        total_fuzzy_samples = []
        
        for intent_type, count in fuzzy_distribution.items():
            if count > 0:
                try:
                    # 为每种意图类型生成对应数量的模糊样本
                    fuzzy_samples = self.generator.generate_fuzzy_samples(count)
                    # 过滤出当前意图类型的模糊样本
                    intent_fuzzy = [s for s in fuzzy_samples 
                                  if s.metadata.get("potential_intent") == intent_type]
                    total_fuzzy_samples.extend(intent_fuzzy[:count])
                except Exception as e:
                    logger.warning(f"生成 {intent_type} 模糊样本失败: {e}")
        
        # 如果按意图分配的数量不够，补充通用模糊样本
        remaining = target_count - len(total_fuzzy_samples)
        if remaining > 0:
            additional_fuzzy = self.generator.generate_fuzzy_samples(remaining)
            total_fuzzy_samples.extend(additional_fuzzy)
        
        return total_fuzzy_samples[:target_count]
    
    async def _generate_robustness_samples(self, base_samples: List[TrainingSample],
                                         adversarial_ratio: float,
                                         boundary_cases: int,
                                         negative_samples: int) -> Dict[str, List[TrainingSample]]:
        """生成鲁棒性样本（基于配置权重）"""
        
        # 获取鲁棒性权重配置
        robustness_weights = self.config_manager.get_robustness_weights()
        
        # 计算各类型样本的目标数量
        total_robustness_budget = int(len(base_samples) * adversarial_ratio) + boundary_cases + negative_samples
        
        robustness_distribution = self.weighted_sampler.sample_by_semantic_weights(
            robustness_weights, total_robustness_budget
        )
        
        # 生成对抗样本
        adversarial_target = robustness_distribution.get("adversarial_layer", 
                                                        int(len(base_samples) * adversarial_ratio))
        adversarial_samples = self.augmenter.generate_adversarial_samples(
            base_samples, adversarial_target / len(base_samples)
        )
        
        # 生成边界样本
        boundary_target = robustness_distribution.get("noise_layer", boundary_cases)
        boundary_samples = self.augmenter.generate_boundary_cases(base_samples, boundary_target)
        
        # 生成负样本
        negative_target = robustness_distribution.get("negative_samples_layer", negative_samples)
        negative_samples_list = self.augmenter.generate_negative_samples(negative_target)
        
        return {
            "adversarial": adversarial_samples,
            "boundary": boundary_samples, 
            "negative": negative_samples_list
        }
    
    def _estimate_sample_quality(self, samples: List[TrainingSample]) -> float:
        """估计样本质量分数"""
        if not samples:
            return 0.0
        
        quality_score = 0.0
        
        # 基于样本多样性
        instructions = [s.instruction for s in samples]
        unique_instructions = set(instructions)
        diversity_score = len(unique_instructions) / len(instructions)
        quality_score += diversity_score * 0.4
        
        # 基于平均指令长度
        avg_length = sum(len(inst) for inst in instructions) / len(instructions)
        length_score = min(1.0, avg_length / 50.0)  # 假设50字符为理想长度
        quality_score += length_score * 0.3
        
        # 基于意图结构完整性
        valid_intents = sum(1 for s in samples if s.output and len(s.output) > 0)
        intent_score = valid_intents / len(samples)
        quality_score += intent_score * 0.3
        
        return min(1.0, quality_score)
    
    async def _record_quality_feedback(self, samples: List[TrainingSample], 
                                     quality_report: Dict[str, Any]):
        """记录质量反馈用于自适应优化"""
        
        # 按意图类型分组并记录质量
        intent_groups = {}
        for sample in samples:
            intent_type = sample.metadata.get("intent_type", "UNKNOWN")
            if intent_type not in intent_groups:
                intent_groups[intent_type] = []
            intent_groups[intent_type].append(sample)
        
        # 记录各意图类型的质量分数
        for intent_type, intent_samples in intent_groups.items():
            quality_score = self._estimate_sample_quality(intent_samples)
            self.adaptive_sampler.record_sample_quality(intent_type, quality_score)
        
        # 记录语义策略的质量分数
        strategy_groups = {}
        for sample in samples:
            strategy = sample.metadata.get("semantic_strategy", "unknown")
            if strategy not in strategy_groups:
                strategy_groups[strategy] = []
            strategy_groups[strategy].append(sample)
        
        for strategy, strategy_samples in strategy_groups.items():
            quality_score = self._estimate_sample_quality(strategy_samples)
            self.adaptive_sampler.record_sample_quality(f"strategy_{strategy}", quality_score)
    
    def _update_execution_stats(self, execution_time: float, success: bool):
        """更新执行统计"""
        self.execution_stats["last_run_time"] = execution_time
        
        if success:
            # 更新平均执行时间
            total_successful = self.execution_stats["successful_runs"]
            current_avg = self.execution_stats["average_generation_time"]
            
            if total_successful > 0:
                new_avg = (current_avg * (total_successful - 1) + execution_time) / total_successful
            else:
                new_avg = execution_time
            
            self.execution_stats["average_generation_time"] = new_avg
    
    async def save_results(self, samples: List[TrainingSample], 
                          quality_report: Dict[str, Any]) -> List[str]:
        """保存生成结果（增强版）"""
        saved_files = []
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        try:
            # 1. 保存训练样本（JSON格式）
            samples_file = os.path.join(self.output_dir, f"training_samples_{timestamp}.json")
            samples_data = []

            for sample in samples:
                sample_dict = {
                    "instruction": sample.instruction,
                    "output": [intent.dict() if hasattr(intent, 'dict') else intent.__dict__
                              for intent in sample.output],
                    "metadata": sample.metadata
                }
                samples_data.append(sample_dict)

            with open(samples_file, 'w', encoding='utf-8') as f:
                json.dump(samples_data, f, ensure_ascii=False, indent=2)

            saved_files.append(samples_file)
            logger.info(f"训练样本已保存: {samples_file}")

            # 尝试上传到阿里云存储
            await self._upload_to_aliyun_storage(samples_data, quality_report, timestamp)
            
            # 2. 保存质量报告
            report_file = os.path.join(self.output_dir, f"quality_report_{timestamp}.json")
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(quality_report, f, ensure_ascii=False, indent=2)
            
            saved_files.append(report_file)
            logger.info(f"质量报告已保存: {report_file}")
            
            # 3. 按意图类型分类保存
            intent_samples = self._group_samples_by_intent(samples)
            for intent_type, intent_samples_list in intent_samples.items():
                intent_file = os.path.join(
                    self.output_dir, 
                    f"samples_{intent_type.lower()}_{timestamp}.json"
                )
                
                intent_data = []
                for sample in intent_samples_list:
                    sample_dict = {
                        "instruction": sample.instruction,
                        "output": [intent.dict() if hasattr(intent, 'dict') else intent.__dict__ 
                                  for intent in sample.output],
                        "metadata": sample.metadata
                    }
                    intent_data.append(sample_dict)
                
                with open(intent_file, 'w', encoding='utf-8') as f:
                    json.dump(intent_data, f, ensure_ascii=False, indent=2)
                
                saved_files.append(intent_file)
            
            # 4. 生成训练集和验证集
            train_samples, val_samples = self._split_train_validation(samples)
            
            # 保存训练集
            train_file = os.path.join(self.output_dir, f"train_set_{timestamp}.json")
            train_data = [
                {
                    "instruction": s.instruction,
                    "output": [intent.dict() if hasattr(intent, 'dict') else intent.__dict__ 
                              for intent in s.output],
                    "metadata": s.metadata
                }
                for s in train_samples
            ]
            
            with open(train_file, 'w', encoding='utf-8') as f:
                json.dump(train_data, f, ensure_ascii=False, indent=2)
            saved_files.append(train_file)
            
            # 保存验证集
            val_file = os.path.join(self.output_dir, f"val_set_{timestamp}.json")
            val_data = [
                {
                    "instruction": s.instruction,
                    "output": [intent.dict() if hasattr(intent, 'dict') else intent.__dict__ 
                              for intent in s.output],
                    "metadata": s.metadata
                }
                for s in val_samples
            ]
            
            with open(val_file, 'w', encoding='utf-8') as f:
                json.dump(val_data, f, ensure_ascii=False, indent=2)
            saved_files.append(val_file)
            
            # 5. 保存配置快照
            config_snapshot_file = os.path.join(self.output_dir, f"config_snapshot_{timestamp}.json")
            config_snapshot = {
                "config_summary": self.config_manager.get_config_summary(),
                "execution_stats": self.execution_stats,
                "generation_timestamp": timestamp
            }
            
            with open(config_snapshot_file, 'w', encoding='utf-8') as f:
                json.dump(config_snapshot, f, ensure_ascii=False, indent=2)
            saved_files.append(config_snapshot_file)
            
            logger.info(f"训练集({len(train_samples)})和验证集({len(val_samples)})已保存")
            logger.info(f"配置快照已保存")
            
            return saved_files
            
        except Exception as e:
            logger.error(f"❌ 保存结果失败: {e}")
            raise
    
    def _group_samples_by_intent(self, samples: List[TrainingSample]) -> Dict[str, List[TrainingSample]]:
        """按意图类型分组样本"""
        grouped = {}
        
        for sample in samples:
            intent_type = sample.metadata.get("intent_type", "UNKNOWN")
            if intent_type not in grouped:
                grouped[intent_type] = []
            grouped[intent_type].append(sample)
        
        return grouped
    
    def _split_train_validation(self, samples: List[TrainingSample], 
                               val_ratio: float = 0.2) -> tuple:
        """划分训练集和验证集（分层划分）"""
        # 按意图类型分层划分，确保每种意图在训练集和验证集中都有代表
        intent_groups = self._group_samples_by_intent(samples)
        
        train_samples = []
        val_samples = []
        
        for intent_type, intent_samples in intent_groups.items():
            # 随机打乱
            import random
            shuffled_samples = intent_samples.copy()
            random.shuffle(shuffled_samples)
            
            # 计算验证集数量
            val_count = max(1, int(len(shuffled_samples) * val_ratio))
            
            # 划分
            val_samples.extend(shuffled_samples[:val_count])
            train_samples.extend(shuffled_samples[val_count:])
        
        # 再次打乱
        import random
        random.shuffle(train_samples)
        random.shuffle(val_samples)
        
        return train_samples, val_samples
    
    async def run_incremental_generation(self, intent_types: List[str], 
                                       samples_per_intent: int = 100,
                                       use_adaptive_weights: bool = True) -> Dict[str, Any]:
        """增量生成特定意图类型的样本（支持自适应优化）"""
        logger.info(f"开始增量生成，意图类型: {intent_types}")
        
        all_samples = []
        
        # 获取自适应权重
        if use_adaptive_weights:
            base_weights = {intent_type: 1.0 for intent_type in intent_types}
            adaptive_weights = self.adaptive_sampler.get_adaptive_weights(base_weights)
            logger.info(f"使用自适应权重: {adaptive_weights}")
        else:
            adaptive_weights = {intent_type: 1.0 for intent_type in intent_types}
        
        for intent_type in intent_types:
            try:
                # 根据自适应权重调整样本数量
                weight = adaptive_weights.get(intent_type, 1.0)
                adjusted_count = int(samples_per_intent * weight)
                
                logger.info(f"📊 生成 {intent_type} 类型样本: {adjusted_count}")
                samples = self.generator.generate_single_intent_samples(intent_type, adjusted_count)
                all_samples.extend(samples)
                
                # 记录质量反馈
                quality_score = self._estimate_sample_quality(samples)
                self.adaptive_sampler.record_sample_quality(intent_type, quality_score)
                
                logger.info(f"{intent_type} 生成完成，数量: {len(samples)}, 质量分数: {quality_score:.3f}")
            except Exception as e:
                logger.error(f"生成 {intent_type} 失败: {e}")
                continue
        
        # 质量控制
        logger.info("执行质量控制...")
        valid_samples, validation_errors = self.quality_controller.validate_samples(all_samples)
        clean_samples = self.quality_controller.deduplicate_samples(valid_samples)
        
        # 保存结果
        quality_report = self.quality_controller.generate_quality_report(clean_samples, validation_errors)
        saved_files = await self.save_results(clean_samples, quality_report)
        
        result = {
            "intent_types": intent_types,
            "generated_samples": len(clean_samples),
            "saved_files": saved_files,
            "quality_report": quality_report,
            "adaptive_weights_used": adaptive_weights,
            "timestamp": datetime.now().isoformat()
        }
        
        logger.info(f"增量生成完成，生成样本数: {len(clean_samples)}")
        return result
    
    def get_pipeline_statistics(self) -> Dict[str, Any]:
        """获取Pipeline执行统计"""
        stats = self.execution_stats.copy()
        
        # 添加自适应采样统计
        sampling_stats = self.distribution_sampler.get_sampling_statistics()
        stats["sampling_statistics"] = sampling_stats
        
        # 添加配置验证状态
        config_validation = self.config_manager.validate_orm_configs()
        stats["config_status"] = config_validation
        
        return stats
    
    def suggest_optimization(self) -> Dict[str, Any]:
        """基于历史执行数据提供优化建议"""
        suggestions = []
        
        # 基于执行统计的建议
        if self.execution_stats["failed_runs"] > 0:
            failure_rate = self.execution_stats["failed_runs"] / self.execution_stats["total_runs"]
            if failure_rate > 0.1:
                suggestions.append("失败率较高，建议检查配置文件和依赖项")
        
        # 基于配置状态的建议
        config_validation = self.config_manager.validate_orm_configs()
        if not config_validation.get("has_target_count", False):
            suggestions.append("建议在orm_weights.yaml中设置global.generated.total_number")
        
        # 基于自适应采样的建议
        available_strategies = ["ADD_COLUMN", "DELETE_COLUMN", "MODIFY_COLUMN"]
        suggested_strategy = self.adaptive_sampler.suggest_sampling_strategy(available_strategies)
        suggestions.append(f"推荐优先生成 {suggested_strategy} 类型样本（基于历史质量表现）")
        
        return {
            "suggestions": suggestions,
            "recommended_strategy": suggested_strategy,
            "current_config_status": config_validation,
            "execution_performance": {
                "average_time": self.execution_stats["average_generation_time"],
                "success_rate": (self.execution_stats["successful_runs"] /
                               max(1, self.execution_stats["total_runs"]))
            }
        }

    async def _upload_to_aliyun_storage(self, samples_data: List[Dict[str, Any]],
                                       quality_report: Dict[str, Any],
                                       timestamp: str):
        """上传数据到阿里云存储"""
        if not self.aliyun_storage:
            logger.debug("阿里云存储未配置，跳过上传")
            return

        try:
            logger.info("🚀 开始上传数据到阿里云存储...")

            # 准备元数据
            metadata = {
                "timestamp": timestamp,
                "sample_count": len(samples_data),
                "pipeline_stats": self.execution_stats,
                "config_summary": self.config_manager.get_config_summary() if hasattr(self.config_manager, 'get_config_summary') else {}
            }

            # 上传到阿里云存储
            upload_results = self.aliyun_storage.save_training_data(
                samples=samples_data,
                quality_report=quality_report,
                metadata=metadata
            )

            if upload_results.get("success"):
                logger.info(f"✅ 数据上传成功，批次ID: {upload_results.get('batch_id')}")

                # 记录上传结果
                if upload_results.get("oss_results"):
                    logger.info(f"📁 OSS文件: {len(upload_results['oss_results'])} 个")
                if upload_results.get("rds_results"):
                    logger.info(f"🗄️ RDS记录: 已保存到数据库")

            else:
                logger.warning(f"⚠️ 数据上传部分失败: {upload_results.get('errors', [])}")

        except Exception as e:
            logger.error(f"❌ 上传到阿里云存储失败: {e}")

    def get_aliyun_storage_status(self) -> Dict[str, Any]:
        """获取阿里云存储状态"""
        if not self.aliyun_storage:
            return {
                "available": False,
                "reason": "阿里云存储未配置或初始化失败"
            }

        try:
            status = self.aliyun_storage.get_storage_status()
            status["available"] = True
            return status
        except Exception as e:
            return {
                "available": False,
                "error": str(e)
            }