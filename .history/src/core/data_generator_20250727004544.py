"""
反向数据生成器：从意图结构生成自然语言表达
支持配置驱动的权重采样和多策略生成
"""

import json
import random
import asyncio
import numpy as np
from typing import List, Dict, Any, Tuple
from itertools import product, combinations
import re
from loguru import logger

from src.models.intent_models import TrainingSample, Intent, IntentType
from src.utils.config import config_manager

class WeightedSampler:
    """加权采样器"""
    
    @staticmethod
    def weighted_choice(choices: List[Any], weights: List[float]) -> Any:
        """根据权重选择"""
        if not choices or not weights:
            return random.choice(choices) if choices else None
        
        # 归一化权重
        total_weight = sum(weights)
        if total_weight == 0:
            return random.choice(choices)
        
        normalized_weights = [w / total_weight for w in weights]
        return np.random.choice(choices, p=normalized_weights)
    
    @staticmethod
    def weighted_choice_dict(weight_dict: Dict[str, float]) -> str:
        """从权重字典中选择"""
        if not weight_dict:
            return None
        
        choices = list(weight_dict.keys())
        weights = list(weight_dict.values())
        return WeightedSampler.weighted_choice(choices, weights)

class ReverseDataGenerator:
    """反向数据生成器：从意图结构生成自然语言表达（配置驱动版）"""
    
    def __init__(self, template_path: str = "data/templates/intent_templates.json", 
                 terms_path: str = "data/templates/business_terms.json"):
        self.template_path = template_path
        self.terms_path = terms_path
        self.templates = {}
        self.business_terms = {}
        
        # 集成配置管理器
        self.config_manager = config_manager
        
        # 从配置中获取基础数据
        self.entities = self.config_manager.get_orm_entities()
        self.field_types = self.config_manager.get_orm_field_types()
        self.field_names_by_category = self.config_manager.get_field_names_by_category()
        
        # 加载模板和术语
        self.load_templates()
        self.load_business_terms()
        
        logger.info(f"配置驱动数据生成器初始化完成")
        logger.info(f"  - 实体类型: {len(self.entities)} 个")
        logger.info(f"  - 字段类型: {len(self.field_types)} 个") 
        logger.info(f"  - 意图模板: {len(self.templates)} 个")
    
    def load_templates(self):
        """加载意图模板"""
        try:
            with open(self.template_path, 'r', encoding='utf-8') as f:
                self.templates = json.load(f)
            logger.info(f"加载了 {len(self.templates)} 个意图模板")
        except Exception as e:
            logger.error(f"模板加载失败: {e}")
            self.templates = self._get_default_templates()
    
    def load_business_terms(self):
        """加载业务术语词典"""
        try:
            with open(self.terms_path, 'r', encoding='utf-8') as f:
                self.business_terms = json.load(f)
            logger.info("业务术语词典加载完成")
        except Exception as e:
            logger.error(f"业务术语加载失败: {e}")
            self.business_terms = self._get_default_business_terms()
    
    def _get_default_templates(self) -> Dict[str, Any]:
        """获取默认模板"""
        return {
            "ADD_COLUMN": {
                "template": {
                    "intentType": "ADD_COLUMN",
                    "targetConceptName": "{entity}",
                    "props": {
                        "name": "{field_name}",
                        "stdSqlType": "{data_type}",
                        "nullable": "{nullable}",
                        "defaultValue": "{default_value}",
                        "comment": "{comment}"
                    }
                },
                "expressions": [
                    "给{entity}表添加一个{field_name}字段",
                    "为{entity}增加{field_name}属性", 
                    "{entity}需要一个{field_name}字段",
                    "在{entity}表中加入{field_name}",
                    "添加{field_name}到{entity}表",
                    "{entity}表要有{field_name}字段",
                    "给{entity}加个{field_name}",
                    "新增{entity}的{field_name}属性"
                ],
                "fuzzy_expressions": [
                    "给{entity}加字段",
                    "{entity}要加个字段",
                    "添加字段到{entity}",
                    "新增字段"
                ]
            },
            "DELETE_COLUMN": {
                "template": {
                    "intentType": "DELETE_COLUMN",
                    "targetConceptName": "{entity}",
                    "props": {
                        "name": "{field_name}"
                    }
                },
                "expressions": [
                    "删除{entity}表的{field_name}字段",
                    "移除{entity}的{field_name}属性",
                    "{entity}不需要{field_name}字段了",
                    "去掉{entity}表中的{field_name}",
                    "把{entity}的{field_name}删了",
                    "取消{entity}的{field_name}字段"
                ],
                "fuzzy_expressions": [
                    "删除{entity}的字段",
                    "移除{entity}字段",
                    "{entity}删字段"
                ]
            },
            "MODIFY_COLUMN": {
                "template": {
                    "intentType": "MODIFY_COLUMN",
                    "targetConceptName": "{entity}",
                    "props": {
                        "name": "{field_name}",
                        "newName": "{new_field_name}",
                        "stdSqlType": "{data_type}",
                        "nullable": "{nullable}"
                    }
                },
                "expressions": [
                    "修改{entity}表的{field_name}字段类型为{data_type}",
                    "将{entity}的{field_name}改为{new_field_name}",
                    "更新{entity}表{field_name}字段",
                    "{entity}的{field_name}字段要改成{data_type}类型"
                ]
            },
            "ENABLE_SOFT_DELETE": {
                "template": {
                    "intentType": "ENABLE_SOFT_DELETE",
                    "targetConceptName": "{entity}",
                    "props": {
                        "deleteField": "is_deleted",
                        "deleteFieldType": "BOOLEAN"
                    }
                },
                "expressions": [
                    "启用{entity}表的逻辑删除",
                    "{entity}表需要软删除功能",
                    "给{entity}添加逻辑删除",
                    "{entity}要支持软删除",
                    "开启{entity}的逻辑删除"
                ]
            }
        }
    
    def _get_default_business_terms(self) -> Dict[str, Any]:
        """获取默认业务术语"""
        return {
            "entities": {
                "客户": ["客户", "顾客", "用户", "会员", "消费者"],
                "订单": ["订单", "工单", "单据", "交易"],
                "产品": ["产品", "商品", "货物", "物品", "SKU"],
                "用户": ["用户", "账户", "账号", "人员"]
            },
            "action_words": {
                "添加": ["添加", "增加", "新增", "加入", "创建", "建立"],
                "删除": ["删除", "移除", "去掉", "取消", "清除"],
                "修改": ["修改", "更改", "变更", "调整", "更新"],
                "关联": ["关联", "连接", "绑定", "链接", "关系"]
            }
        }
    
    def generate_single_intent_samples(self, intent_type: str, count: int = 100) -> List[TrainingSample]:
        """为单个意图类型生成样本（配置驱动版本）"""
        if intent_type not in self.templates:
            raise ValueError(f"未找到意图类型: {intent_type}")
        
        logger.info(f"开始生成 {intent_type} 类型的样本，目标数量: {count}")
        
        # 获取配置权重
        crud_weights = self.config_manager.get_crud_weights("base_object_hierarchy.entity.column.name")
        semantic_weights = self.config_manager.get_semantic_strategy_weights()
        
        samples = []
        
        # 按语义策略分配样本
        for strategy, strategy_weight in semantic_weights.items():
            strategy_count = int(count * strategy_weight / 100)
            if strategy_count > 0:
                strategy_samples = self._generate_strategy_samples(
                    intent_type, strategy, strategy_count, crud_weights
                )
                samples.extend(strategy_samples)
        
        # 应用表达多样性
        samples = self._apply_expression_diversity(samples)
        
        # 随机打乱
        random.shuffle(samples)
        
        logger.info(f"{intent_type} 类型生成了 {len(samples)} 个样本")
        return samples[:count]
    
    def _generate_strategy_samples(self, intent_type: str, strategy: str, 
                                 count: int, crud_weights: Dict[str, float]) -> List[TrainingSample]:
        """生成特定策略的样本"""
        samples = []
        
        for _ in range(count):
            try:
                if strategy == "atomic":
                    sample = self._generate_atomic_sample(intent_type, crud_weights)
                elif strategy == "composite":
                    sample = self._generate_composite_sample(intent_type, crud_weights)
                elif strategy == "sequence":
                    sample = self._generate_sequence_sample(intent_type, crud_weights)
                elif strategy == "implicit":
                    sample = self._generate_implicit_sample(intent_type, crud_weights)
                else:
                    sample = self._generate_atomic_sample(intent_type, crud_weights)
                
                if sample:
                    sample.metadata.update({
                        "semantic_strategy": strategy,
                        "intent_type": intent_type,
                        "generation_method": "config_driven"
                    })
                    samples.append(sample)
                    
            except Exception as e:
                logger.warning(f"生成 {strategy} 策略样本失败: {e}")
                continue
        
        return samples
    
    def _generate_atomic_sample(self, intent_type: str, crud_weights: Dict[str, float]) -> TrainingSample:
        """生成原子操作样本"""
        # 根据权重选择实体和字段
        entity = self._weighted_sample_entity()
        field_info = self._weighted_sample_field_info(crud_weights)
        
        # 生成指令文本
        instruction = self._generate_instruction_text(intent_type, entity, field_info)
        
        # 生成意图结构
        intent = self._generate_intent_structure(intent_type, entity, field_info)
        
        return TrainingSample(
            instruction=instruction,
            output=[Intent(**intent)],
            metadata={
                "complexity": "atomic",
                "entity": entity,
                "field_info": field_info
            }
        )
    
    def _generate_composite_sample(self, intent_type: str, crud_weights: Dict[str, float]) -> TrainingSample:
        """生成复合操作样本"""
        # 生成2-3个相关操作
        operations_count = random.randint(2, 3)
        entity = self._weighted_sample_entity()
        
        intents = []
        field_infos = []
        
        for _ in range(operations_count):
            field_info = self._weighted_sample_field_info(crud_weights)
            field_infos.append(field_info)
            
            intent = self._generate_intent_structure(intent_type, entity, field_info)
            intents.append(Intent(**intent))
        
        # 生成复合指令文本
        instruction = self._generate_composite_instruction(intent_type, entity, field_infos)
        
        return TrainingSample(
            instruction=instruction,
            output=intents,
            metadata={
                "complexity": "composite",
                "entity": entity,
                "operations_count": operations_count
            }
        )
    
    def _generate_sequence_sample(self, intent_type: str, crud_weights: Dict[str, float]) -> TrainingSample:
        """生成序列操作样本"""
        # 生成有先后顺序的操作
        entity = self._weighted_sample_entity()
        field_info = self._weighted_sample_field_info(crud_weights)
        
        # 添加序列标识词
        sequence_words = ["先", "然后", "接着", "最后", "首先"]
        sequence_word = random.choice(sequence_words)
        
        instruction = f"{sequence_word}" + self._generate_instruction_text(intent_type, entity, field_info)
        intent = self._generate_intent_structure(intent_type, entity, field_info)
        
        return TrainingSample(
            instruction=instruction,
            output=[Intent(**intent)],
            metadata={
                "complexity": "sequence",
                "sequence_indicator": sequence_word
            }
        )
    
    def _generate_implicit_sample(self, intent_type: str, crud_weights: Dict[str, float]) -> TrainingSample:
        """生成隐含操作样本"""
        entity = self._weighted_sample_entity()
        field_info = self._weighted_sample_field_info(crud_weights)
        
        # 生成更加隐含的表达
        implicit_templates = {
            "ADD_COLUMN": [
                "{entity}需要记录{field_name}信息",
                "系统要支持{entity}的{field_name}功能",
                "我们想知道{entity}的{field_name}情况",
                "{entity}应该有{field_name}这个属性",
                "要追踪{entity}的{field_name}数据"
            ],
            "DELETE_COLUMN": [
                "{entity}不再需要{field_name}了",
                "{field_name}对{entity}没用了",
                "取消{entity}的{field_name}支持",
                "{entity}的{field_name}可以去掉了",
                "不用管{entity}的{field_name}了"
            ],
            "MODIFY_COLUMN": [
                "{entity}的{field_name}需要调整",
                "优化{entity}的{field_name}设计",
                "{entity}的{field_name}要改进一下"
            ]
        }
        
        templates = implicit_templates.get(intent_type, [])
        if templates:
            template = random.choice(templates)
            instruction = template.format(entity=entity, field_name=field_info["name"])
        else:
            instruction = self._generate_instruction_text(intent_type, entity, field_info)
        
        intent = self._generate_intent_structure(intent_type, entity, field_info)
        
        return TrainingSample(
            instruction=instruction,
            output=[Intent(**intent)],
            metadata={
                "complexity": "implicit",
                "implicit_style": True
            }
        )
    
    def _weighted_sample_entity(self) -> str:
        """根据权重采样实体"""
        # 基于配置的实体权重采样（当前简化为均匀采样）
        # 可以根据需要从配置中获取具体的实体权重
        return random.choice(self.entities)
    
    def _weighted_sample_field_info(self, crud_weights: Dict[str, float]) -> Dict[str, Any]:
        """根据权重采样字段信息"""
        # 选择字段类别（基于配置）
        categories = list(self.field_names_by_category.keys())
        category = random.choice(categories)
        
        # 从类别中选择字段名
        field_names = self.field_names_by_category[category]
        field_name = random.choice(field_names)
        
        # 选择数据类型（基于配置）
        data_type = random.choice(self.field_types)
        
        return {
            "name": field_name,
            "stdSqlType": data_type,
            "category": category,
            "nullable": random.choice([True, False]),
            "defaultValue": self._generate_default_value(data_type)
        }
    
    def _generate_default_value(self, data_type: str) -> Any:
        """根据数据类型生成默认值"""
        if "INT" in data_type or "DECIMAL" in data_type or "NUMERIC" in data_type:
            return random.choice([None, 0, 1, -1, 100])
        elif "VARCHAR" in data_type or "TEXT" in data_type or "CHAR" in data_type:
            return random.choice([None, "", "默认值", "无"])
        elif "BOOLEAN" in data_type:
            return random.choice([None, True, False])
        elif "DATE" in data_type or "TIME" in data_type:
            return None
        else:
            return None
    
    def _generate_instruction_text(self, intent_type: str, entity: str, field_info: Dict[str, Any]) -> str:
        """生成指令文本"""
        templates = self.templates.get(intent_type, {}).get("expressions", [])
        
        if not templates:
            # 默认模板
            if intent_type == "ADD_COLUMN":
                templates = ["给{entity}表添加一个{field_name}字段"]
            elif intent_type == "DELETE_COLUMN":
                templates = ["删除{entity}表的{field_name}字段"]
            elif intent_type == "MODIFY_COLUMN":
                templates = ["修改{entity}表的{field_name}字段"]
            else:
                templates = ["操作{entity}表的{field_name}字段"]
        
        template = random.choice(templates)
        
        # 格式化模板
        variables = {
            "entity": entity,
            "field_name": field_info["name"],
            "data_type": field_info["stdSqlType"],
            "new_field_name": f"新{field_info['name']}"
        }
        
        return self._fill_template(template, variables)
    
    def _generate_composite_instruction(self, intent_type: str, entity: str, field_infos: List[Dict[str, Any]]) -> str:
        """生成复合指令文本"""
        field_names = [info["name"] for info in field_infos]
        
        if intent_type == "ADD_COLUMN":
            if len(field_names) == 2:
                return f"给{entity}表添加{field_names[0]}和{field_names[1]}字段"
            else:
                fields_str = "、".join(field_names[:-1]) + f"和{field_names[-1]}"
                return f"给{entity}表添加{fields_str}字段"
        elif intent_type == "DELETE_COLUMN":
            if len(field_names) == 2:
                return f"删除{entity}表的{field_names[0]}和{field_names[1]}字段"
            else:
                fields_str = "、".join(field_names[:-1]) + f"和{field_names[-1]}"
                return f"删除{entity}表的{fields_str}字段"
        else:
            return f"修改{entity}表的相关字段"
    
    def _generate_intent_structure(self, intent_type: str, entity: str, field_info: Dict[str, Any]) -> Dict[str, Any]:
        """生成意图结构"""
        base_intent = {
            "intentType": intent_type,
            "targetConceptName": entity,
            "confidence": random.uniform(0.8, 0.95)
        }
        
        # 根据意图类型生成不同的props
        if intent_type == "ADD_COLUMN":
            base_intent["props"] = {
                "name": field_info["name"],
                "stdSqlType": field_info["stdSqlType"],
                "nullable": field_info.get("nullable", True),
                "defaultValue": field_info.get("defaultValue")
            }
        elif intent_type == "DELETE_COLUMN":
            base_intent["props"] = {
                "name": field_info["name"]
            }
        elif intent_type == "MODIFY_COLUMN":
            base_intent["props"] = {
                "name": field_info["name"],
                "stdSqlType": field_info["stdSqlType"],
                "nullable": field_info.get("nullable", True)
            }
        elif intent_type == "ENABLE_SOFT_DELETE":
            base_intent["props"] = {
                "deleteField": "is_deleted",
                "deleteFieldType": "BOOLEAN"
            }
        else:
            base_intent["props"] = {}
        
        return base_intent
    
    def _fill_template(self, template: str, variables: Dict[str, str]) -> str:
        """填充模板变量"""
        result = template
        for key, value in variables.items():
            placeholder = "{" + key + "}"
            if placeholder in result:
                result = result.replace(placeholder, str(value))
        return result
    
    def _apply_expression_diversity(self, samples: List[TrainingSample]) -> List[TrainingSample]:
        """应用表达多样性"""
        diversity_weights = self.config_manager.get_expression_layer_weights()
        
        enhanced_samples = []
        
        for sample in samples:
            # 原始样本
            enhanced_samples.append(sample)
            
            # 根据权重决定是否应用各种变换
            if random.random() < diversity_weights["lexical"] / 100:
                variant = self._apply_lexical_variation(sample)
                if variant and variant.instruction != sample.instruction:
                    enhanced_samples.append(variant)
            
            if random.random() < diversity_weights["syntactic"] / 100:
                variant = self._apply_syntactic_variation(sample)
                if variant and variant.instruction != sample.instruction:
                    enhanced_samples.append(variant)
        
        return enhanced_samples
    
    def _apply_lexical_variation(self, sample: TrainingSample) -> TrainingSample:
        """应用词汇层变化"""
        instruction = sample.instruction
        
        # 实体同义词替换
        if "entities" in self.business_terms:
            for standard, synonyms in self.business_terms["entities"].items():
                if standard in instruction:
                    synonym = random.choice(synonyms)
                    instruction = instruction.replace(standard, synonym, 1)
                    break
        
        # 动作词同义词替换
        if "action_words" in self.business_terms:
            for standard, synonyms in self.business_terms["action_words"].items():
                if standard in instruction:
                    synonym = random.choice(synonyms)
                    instruction = instruction.replace(standard, synonym, 1)
                    break
        
        return TrainingSample(
            instruction=instruction,
            output=sample.output,
            metadata={
                **sample.metadata,
                "variation_type": "lexical",
                "original_instruction": sample.instruction
            }
        )
    
    def _apply_syntactic_variation(self, sample: TrainingSample) -> TrainingSample:
        """应用句法层变化"""
        instruction = sample.instruction
        
        # 语序调整
        patterns = [
            (r"给(.+?)表添加(.+?)字段", r"添加\2字段到\1表"),
            (r"删除(.+?)表的(.+?)字段", r"把\1表的\2字段删除"),
            (r"(.+?)需要(.+?)字段", r"需要给\1添加\2字段")
        ]
        
        for pattern, replacement in patterns:
            if re.search(pattern, instruction):
                instruction = re.sub(pattern, replacement, instruction)
                break
        
        return TrainingSample(
            instruction=instruction,
            output=sample.output,
            metadata={
                **sample.metadata,
                "variation_type": "syntactic",
                "original_instruction": sample.instruction
            }
        )
    
    def generate_fuzzy_samples(self, count: int = 200) -> List[TrainingSample]:
        """生成模糊/不完整样本，用于澄清推荐训练"""
        fuzzy_samples = []
        
        for intent_type, config in self.templates.items():
            fuzzy_expressions = config.get("fuzzy_expressions", [])
            
            # 如果没有fuzzy_expressions，跳过
            if not fuzzy_expressions:
                continue
            
            samples_per_intent = count // len(self.templates)
            
            for _ in range(samples_per_intent):
                try:
                    entity = random.choice(self.entities)
                    fuzzy_expr = random.choice(fuzzy_expressions)
                    
                    instruction = fuzzy_expr.format(entity=entity)
                    
                    # 模糊样本不返回具体意图，而是需要澄清
                    sample = TrainingSample(
                        instruction=instruction,
                        output=[],  # 空意图列表表示需要澄清
                        metadata={
                            "intent_type": "CLARIFICATION_NEEDED",
                            "potential_intent": intent_type,
                            "generation_method": "fuzzy",
                            "entity": entity
                        }
                    )
                    fuzzy_samples.append(sample)
                    
                except Exception as e:
                    logger.warning(f"生成模糊样本失败: {e}")
                    continue
        
        logger.info(f"生成了 {len(fuzzy_samples)} 个模糊样本")
        return fuzzy_samples
    
    async def generate_training_dataset(self, samples_per_intent: int = 500) -> List[TrainingSample]:
        """生成完整的训练数据集（配置驱动版本）"""
        all_samples = []
        
        # 获取配置中的意图类型权重
        intent_weights = self.config_manager.get_intent_type_weights()
        total_target = self.config_manager.get_generation_target_count()
        
        logger.info(f"开始生成训练数据集")
        logger.info(f"  配置目标总数: {total_target}")
        logger.info(f"  意图类型权重: {intent_weights}")
        
        # 根据权重分配各意图类型的样本数
        for intent_type in self.templates.keys():
            if intent_type in intent_weights:
                weight = intent_weights[intent_type]
                intent_count = int(total_target * weight / sum(intent_weights.values()))
                actual_count = max(intent_count, samples_per_intent // len(self.templates))
            else:
                actual_count = samples_per_intent
            
            logger.info(f"  {intent_type}: {actual_count} 个样本")
            
            try:
                samples = self.generate_single_intent_samples(intent_type, actual_count)
                all_samples.extend(samples)
            except Exception as e:
                logger.error(f"生成 {intent_type} 类型样本失败: {e}")
                continue
        
        logger.info(f"总共生成了 {len(all_samples)} 个训练样本")
        return all_samples