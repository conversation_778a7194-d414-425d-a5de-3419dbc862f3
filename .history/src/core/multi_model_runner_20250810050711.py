#!/usr/bin/env python3
"""
多模型数据生成运行器
集成增强采样系统，支持传统采样和增强采样两种模式
遵循KISS、YAGNI和SOLID原则的简洁实现

功能特性:
- 支持传统反向生成和增强采样两种模式  
- 多模型配置支持（本地/API）
- 成本估算和监控
- 详细的进度跟踪和错误处理
- 灵活的配置管理
"""

import asyncio
import argparse
import sys
import os
import json
import yaml
import time
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple
import traceback

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入项目模块
try:
    from src.core.enhanced_sampling_pipeline import (
        create_enhanced_pipeline, 
        generate_enhanced_dataset,
        run_quick_sampling
    )
    from src.core.distilabel_pipeline import create_enhanced_pipeline as create_distilabel_pipeline
    from src.core.data_generator import ReverseDataGenerator  # 传统生成器
    from src.models.intent_models import TrainingSample
    ENHANCED_SAMPLING_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 增强采样模块导入失败: {e}")
    ENHANCED_SAMPLING_AVAILABLE = False

try:
    from loguru import logger
    LOGGER_AVAILABLE = True
except ImportError:
    import logging
    logger = logging.getLogger(__name__)
    logging.basicConfig(level=logging.INFO)
    LOGGER_AVAILABLE = False


# ==================== 核心运行器类 ====================

class MultiModelRunner:
    """多模型数据生成运行器 - 单一职责原则"""
    
    def __init__(self):
        self.start_time = None
        self.config = None
        self.stats = {
            "total_samples": 0,
            "generation_time": 0.0,
            "success_rate": 1.0,
            "models_used": []
        }
    
    def create_argument_parser(self) -> argparse.ArgumentParser:
        """创建命令行参数解析器 - 保持简洁"""
        parser = argparse.ArgumentParser(
            description="🤖 多模型数据生成系统",
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
使用示例:

基础操作:
  python scripts/data_generation/multi_model_runner.py
  python scripts/data_generation/multi_model_runner.py --test-mode
  python scripts/data_generation/multi_model_runner.py --enhanced-sampling

高级操作:
  # 指定配置和输出
  python scripts/data_generation/multi_model_runner.py \\
    --config configs/sampling_config.yaml \\
    --output data/enhanced_output \\
    --samples 100 \\
    --enhanced-sampling

  # 成本控制模式
  python scripts/data_generation/multi_model_runner.py \\
    --cost-estimate \\
    --max-cost 10.0 \\
    --samples 200

模式说明:
  传统模式: 基于模板的反向生成
  增强模式: 六维采样系统（意图驱动+多样性扩展+鲁棒性增强）
            """
        )
        
        # 基础参数
        parser.add_argument(
            "--config", "-c",
            type=str,
            default="configs/sampling_config.yaml",
            help="配置文件路径 (默认: configs/sampling_config.yaml)"
        )
        
        parser.add_argument(
            "--output", "-o", 
            type=str,
            default=None,
            help="输出目录路径 (默认: 自动生成)"
        )
        
        parser.add_argument(
            "--samples", "-s",
            type=int,
            default=None,
            help="基础样本数量 (覆盖配置文件设置)"
        )
        
        # 关键的增强采样选项
        parser.add_argument(
            "--enhanced-sampling",
            action="store_true", 
            help="🚀 启用增强采样系统 (六维采样)"
        )
        
        # 运行模式
        parser.add_argument(
            "--test-mode", "-t",
            action="store_true",
            help="🧪 测试模式: 生成少量数据验证"
        )
        
        parser.add_argument(
            "--dry-run", "-d",
            action="store_true", 
            help="🔍 验证配置，不执行生成"
        )
        
        # 成本控制
        parser.add_argument(
            "--cost-estimate", "-e",
            action="store_true",
            help="💰 估算生成成本"
        )
        
        parser.add_argument(
            "--max-cost",
            type=float,
            default=None,
            help="最大成本限制 (美元)"
        )
        
        # 调试选项
        parser.add_argument(
            "--verbose", "-v",
            action="store_true",
            help="🔊 详细输出模式"
        )
        
        parser.add_argument(
            "--debug",
            action="store_true",
            help="🐛 调试模式"
        )
        
        # 输出格式
        parser.add_argument(
            "--format",
            choices=["json", "jsonl", "yaml"],
            default="json",
            help="输出格式 (默认: json)"
        )
        
        return parser
    
    def load_and_validate_config(self, config_path: str) -> Dict[str, Any]:
        """加载和验证配置 - 单一职责"""
        if not Path(config_path).exists():
            logger.error(f"❌ 配置文件不存在: {config_path}")
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 基础验证
            self._validate_config_structure(config)
            
            logger.info(f"✅ 配置文件加载成功: {config_path}")
            return config
            
        except yaml.YAMLError as e:
            logger.error(f"❌ 配置文件格式错误: {e}")
            raise
        except Exception as e:
            logger.error(f"❌ 配置文件加载失败: {e}")
            raise
    
    def _validate_config_structure(self, config: Dict[str, Any]):
        """验证配置结构 - YAGNI原则，只检查必需的"""
        required_sections = []
        
        # 根据功能需求检查不同section
        if "base_generation" not in config:
            config["base_generation"] = {"base_samples_count": 100}
        
        if "output" not in config:
            config["output"] = {"format": "json", "include_metadata": True}
    
    def estimate_cost(self, config: Dict[str, Any], samples: int) -> Dict[str, Any]:
        """估算生成成本 - 简单有效"""
        # 基础成本估算
        base_samples = samples or config.get("base_generation", {}).get("base_samples_count", 100)
        
        # 不同采样方式的成本系数
        if config.get("enhanced_sampling", {}).get("enabled", False):
            # 增强采样：基础+改写+鲁棒性
            total_samples_estimated = base_samples * 6  # 扩展系数
            complexity_factor = 2.0  # 增强采样更复杂
        else:
            # 传统采样
            total_samples_estimated = base_samples
            complexity_factor = 1.0
        
        # 成本估算（如果使用API模型）
        cost_per_1k_tokens = 0.002  # 默认成本
        avg_tokens_per_sample = 150
        
        total_tokens = total_samples_estimated * avg_tokens_per_sample
        estimated_cost = (total_tokens / 1000) * cost_per_1k_tokens * complexity_factor
        
        return {
            "base_samples": base_samples,
            "estimated_total_samples": total_samples_estimated,
            "estimated_tokens": total_tokens,
            "estimated_cost_usd": estimated_cost,
            "complexity_factor": complexity_factor
        }
    
    def run_traditional_generation(self, config: Dict[str, Any], output_path: str, args: argparse.Namespace) -> List[TrainingSample]:
        """运行传统生成模式"""
        logger.info("📊 启动传统反向生成模式...")
        
        try:
            from src.core.data_generator import ReverseDataGenerator
            
            generator = ReverseDataGenerator()
            
            # 确定样本数量
            sample_count = args.samples or config.get("base_generation", {}).get("base_samples_count", 100)
            if args.test_mode:
                sample_count = min(20, sample_count)
            
            logger.info(f"生成 {sample_count} 个传统样本...")
            
            # 生成样本
            samples = []
            for intent_type in ["ADD_COLUMN", "DELETE_COLUMN", "MODIFY_COLUMN"]:
                type_samples = generator.generate_single_intent_samples(
                    intent_type, sample_count // 3
                )
                samples.extend(type_samples)
            
            logger.info(f"✅ 传统生成完成: {len(samples)} 个样本")
            return samples
            
        except ImportError:
            logger.error("❌ 传统生成器不可用")
            return []
        except Exception as e:
            logger.error(f"❌ 传统生成失败: {e}")
            return []
    
    def run_enhanced_generation(self, config: Dict[str, Any], output_path: str, args: argparse.Namespace) -> List[TrainingSample]:
        """运行增强生成模式"""
        logger.info("🚀 启动增强采样模式...")
        
        if not ENHANCED_SAMPLING_AVAILABLE:
            logger.error("❌ 增强采样系统不可用，请检查依赖")
            return []
        
        try:
            # 确定样本数量
            sample_count = args.samples or config.get("base_generation", {}).get("base_samples_count", 100)
            if args.test_mode:
                sample_count = min(20, sample_count)
            
            logger.info(f"使用增强采样系统生成数据...")
            
            # 使用快速采样接口
            samples = run_quick_sampling(
                base_samples_count=sample_count,
                enable_paraphrase=True,
                enable_robustness=True,
                output_path=None  # 暂不导出，后续统一处理
            )
            
            # 转换为TrainingSample格式
            training_samples = []
            for sample in samples:
                if hasattr(sample, 'to_training_sample'):
                    training_samples.append(sample.to_training_sample())
                else:
                    # 兼容性处理
                    training_sample = TrainingSample(
                        instruction=sample.instruction,
                        intent=sample.intent_json,
                        metadata=sample.metadata
                    )
                    training_samples.append(training_sample)
            
            logger.info(f"✅ 增强生成完成: {len(training_samples)} 个样本")
            return training_samples
            
        except Exception as e:
            logger.error(f"❌ 增强生成失败: {e}")
            if args.debug:
                traceback.print_exc()
            return []
    
    def run_distilabel_generation(self, config: Dict[str, Any], output_path: str, args: argparse.Namespace) -> List[TrainingSample]:
        """运行distilabel模式（如果配置了的话）"""
        logger.info("🔄 启动distilabel管道模式...")
        
        try:
            # 检查是否配置了distilabel模式
            if config.get("pipeline", {}).get("mode") != "distilabel":
                logger.info("未配置distilabel模式，跳过")
                return []
            
            pipeline = create_distilabel_pipeline(args.config)
            # 这里需要调用实际的distilabel运行逻辑
            logger.info("⚠️ distilabel模式需要进一步实现")
            return []
            
        except Exception as e:
            logger.error(f"❌ distilabel生成失败: {e}")
            return []
    
    def save_results(self, samples: List[TrainingSample], output_path: str, format_type: str = "json") -> str:
        """保存生成结果 - 简单可靠"""
        if not samples:
            logger.warning("⚠️ 没有样本需要保存")
            return ""
        
        # 确保输出目录存在
        output_dir = Path(output_path)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 转换数据格式
        export_data = []
        for sample in samples:
            if hasattr(sample, '__dict__'):
                # 使用对象属性
                data = {
                    "instruction": getattr(sample, 'instruction', ''),
                    "intent": getattr(sample, 'intent', {}),
                    "metadata": getattr(sample, 'metadata', {})
                }
            else:
                # 兼容性处理
                data = sample
            
            export_data.append(data)
        
        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"generated_samples_{timestamp}.{format_type}"
        file_path = output_dir / filename
        
        try:
            if format_type == "json":
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, ensure_ascii=False, indent=2)
                    
            elif format_type == "jsonl":
                with open(file_path, 'w', encoding='utf-8') as f:
                    for item in export_data:
                        f.write(json.dumps(item, ensure_ascii=False) + '\n')
                        
            elif format_type == "yaml":
                with open(file_path, 'w', encoding='utf-8') as f:
                    yaml.dump(export_data, f, allow_unicode=True, default_flow_style=False)
            
            logger.info(f"💾 结果已保存: {file_path}")
            logger.info(f"📊 样本数量: {len(export_data)}")
            
            # 保存统计信息
            self._save_statistics(output_dir, len(export_data))
            
            return str(file_path)
            
        except Exception as e:
            logger.error(f"❌ 保存失败: {e}")
            return ""
    
    def _save_statistics(self, output_dir: Path, sample_count: int):
        """保存统计信息"""
        try:
            stats = {
                "generation_info": {
                    "total_samples": sample_count,
                    "generation_time": self.stats["generation_time"],
                    "success_rate": self.stats["success_rate"],
                    "timestamp": datetime.now().isoformat()
                },
                "performance": {
                    "samples_per_second": sample_count / max(1, self.stats["generation_time"]),
                    "models_used": self.stats.get("models_used", [])
                }
            }
            
            stats_path = output_dir / "generation_stats.json"
            with open(stats_path, 'w', encoding='utf-8') as f:
                json.dump(stats, f, ensure_ascii=False, indent=2)
                
            logger.info(f"📈 统计信息已保存: {stats_path}")
            
        except Exception as e:
            logger.warning(f"保存统计信息失败: {e}")
    
    def print_summary(self, samples: List[TrainingSample], args: argparse.Namespace):
        """打印执行摘要"""
        print("\n" + "="*60)
        print("📊 生成摘要")
        print("="*60)
        
        print(f"🎯 生成模式: {'增强采样' if args.enhanced_sampling else '传统生成'}")
        print(f"📦 总样本数: {len(samples)}")
        print(f"⏱️  生成时间: {self.stats['generation_time']:.2f}秒")
        
        if samples:
            print(f"⚡ 生成速度: {len(samples) / max(1, self.stats['generation_time']):.1f} 样本/秒")
        
        # 样本类型分布（如果有metadata）
        if samples and hasattr(samples[0], 'metadata') and samples[0].metadata:
            sample_types = {}
            for sample in samples:
                sample_type = sample.metadata.get("sample_type", "unknown")
                sample_types[sample_type] = sample_types.get(sample_type, 0) + 1
            
            if sample_types:
                print(f"📈 样本分布:")
                for sample_type, count in sample_types.items():
                    print(f"   {sample_type}: {count}")
        
        print("="*60)
        
        # 后续建议
        if args.enhanced_sampling:
            print("\n💡 增强采样后续建议:")
            print("1. 检查样本多样性和质量分布")
            print("2. 验证边界样本和对抗样本的有效性")
            print("3. 根据需要调整采样配置参数")
        else:
            print("\n💡 传统生成后续建议:")
            print("1. 检查生成样本的覆盖面")
            print("2. 考虑升级到增强采样获得更好的多样性")
            print("3. 验证样本质量是否满足训练需求")
    
    async def main(self):
        """主程序入口 - 协调所有组件"""
        parser = self.create_argument_parser()
        args = parser.parse_args()
        
        self.start_time = datetime.now()
        
        try:
            logger.info("🚀 启动多模型数据生成系统...")
            
            # 设置日志级别
            if args.verbose:
                logger.info("🔊 详细输出模式已启用")
            
            # 加载配置
            config = self.load_and_validate_config(args.config)
            self.config = config
            
            # 成本估算
            if args.cost_estimate:
                cost_info = self.estimate_cost(config, args.samples)
                print("\n💰 成本估算:")
                print(f"   基础样本: {cost_info['base_samples']}")
                print(f"   预计总样本: {cost_info['estimated_total_samples']}")
                print(f"   预计成本: ${cost_info['estimated_cost_usd']:.4f}")
                
                if args.max_cost and cost_info['estimated_cost_usd'] > args.max_cost:
                    logger.error(f"❌ 预计成本 ${cost_info['estimated_cost_usd']:.4f} 超过限制 ${args.max_cost}")
                    return
                
                if not args.dry_run:
                    confirm = input("\n继续执行吗? (y/N): ")
                    if confirm.lower() != 'y':
                        logger.info("❌ 用户取消执行")
                        return
            
            # 干运行模式
            if args.dry_run:
                logger.info("✅ 配置验证完成（干运行模式）")
                return
            
            # 确定输出路径
            output_path = args.output
            if not output_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                mode = "enhanced" if args.enhanced_sampling else "traditional"
                test_suffix = "_test" if args.test_mode else ""
                output_path = f"data/processed/{mode}_output_{timestamp}{test_suffix}"
            
            logger.info(f"📁 输出路径: {output_path}")
            
            # 记录开始时间
            start_time = time.time()
            
            # 根据模式选择生成方法
            samples = []
            
            if args.enhanced_sampling:
                # 增强采样模式
                samples = self.run_enhanced_generation(config, output_path, args)
                self.stats["models_used"].append("enhanced_sampling_pipeline")
                
            elif config.get("pipeline", {}).get("mode") == "distilabel":
                # distilabel模式
                samples = self.run_distilabel_generation(config, output_path, args)
                self.stats["models_used"].append("distilabel_pipeline")
                
            else:
                # 传统模式
                samples = self.run_traditional_generation(config, output_path, args)
                self.stats["models_used"].append("traditional_generator")
            
            # 记录统计
            self.stats["generation_time"] = time.time() - start_time
            self.stats["total_samples"] = len(samples)
            self.stats["success_rate"] = 1.0 if samples else 0.0
            
            # 保存结果
            if samples:
                saved_path = self.save_results(samples, output_path, args.format)
                if saved_path:
                    logger.info(f"✅ 数据生成完成！文件已保存到: {saved_path}")
                else:
                    logger.error("❌ 文件保存失败")
            else:
                logger.error("❌ 未生成任何样本")
            
            # 打印摘要
            self.print_summary(samples, args)
            
        except KeyboardInterrupt:
            logger.warning("❌ 用户中断执行")
            sys.exit(1)
        except Exception as e:
            logger.error(f"❌ 执行失败: {e}")
            if args.debug:
                traceback.print_exc()
            sys.exit(1)


# ==================== 便捷函数 ====================

def quick_test():
    """快速测试函数"""
    print("🧪 运行快速测试...")
    
    try:
        if ENHANCED_SAMPLING_AVAILABLE:
            samples = run_quick_sampling(base_samples_count=5)
            print(f"✅ 增强采样测试通过: {len(samples)} 个样本")
        else:
            print("⚠️ 增强采样不可用，跳过测试")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")


def print_help():
    """打印帮助信息"""
    print("""
🤖 多模型数据生成系统

主要功能:
1. 传统反向生成：基于模板的指令生成
2. 增强采样：六维采样系统（基础+改写+鲁棒性+边界+负样本+自适应）
3. 多格式输出：JSON、JSONL、YAML
4. 成本估算和控制
5. 详细的进度监控

快速开始:
  python scripts/data_generation/multi_model_runner.py --test-mode
  python scripts/data_generation/multi_model_runner.py --enhanced-sampling --test-mode

配置文件:
  configs/sampling_config.yaml - 增强采样配置
  configs/distilabel_config.yaml - distilabel管道配置

注意事项:
  1. 首次使用建议先运行 --test-mode 验证配置
  2. 大规模生成前先使用 --cost-estimate 评估成本
  3. 增强采样模式会生成更多样本，注意存储空间
    """)


# ==================== 主程序入口 ====================

if __name__ == "__main__":
    # 检查命令行参数
    if len(sys.argv) == 1:
        print_help()
        sys.exit(0)
    
    # 特殊命令处理
    if len(sys.argv) == 2:
        if sys.argv[1] == "--help" or sys.argv[1] == "-h":
            print_help()
            sys.exit(0)
        elif sys.argv[1] == "--quick-test":
            quick_test()
            sys.exit(0)
    
    # 启动主程序
    runner = MultiModelRunner()
    
    try:
        asyncio.run(runner.main())
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)