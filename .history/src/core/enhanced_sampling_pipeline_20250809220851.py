"""
增强采样管道模块
专注于采样逻辑，便于集成到distilabel系统中
遵循KISS、YAGNI和SOLID原则的简洁实现
"""

import json
import yaml
import random
import hashlib
import asyncio
from typing import Dict, List, Any, Optional, Tuple, Set, Union
from dataclasses import dataclass, field
from pathlib import Path
from loguru import logger
from collections import defaultdict
import time

# 导入现有组件
from src.core.weighted_sampler import WeightedSampler, EnhancedSamplingAdapter
from src.models.intent_models import TrainingSample, Intent


# ==================== 核心数据结构 ====================

@dataclass
class SampleConfig:
    """采样配置 - Single Responsibility Principle"""
    base_samples_count: int = 200
    paraphrase_variants: int = 4
    adversarial_ratio: float = 0.3
    boundary_samples: int = 50
    negative_samples: int = 100
    quality_threshold: float = 0.6


@dataclass
class IntentSample:
    """意图样本数据结构 - 保持简单"""
    intent_json: Dict[str, Any]
    instruction: str
    sample_type: str  # base, paraphrase, adversarial, boundary, negative
    confidence: float = 1.0
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """生成唯一标识符用于去重"""
        content = f"{self.intent_json}_{self.instruction}"
        self.hash_id = hashlib.md5(content.encode()).hexdigest()
    
    def to_training_sample(self) -> TrainingSample:
        """转换为TrainingSample格式"""
        return TrainingSample(
            instruction=self.instruction,
            intent=Intent(**self.intent_json),
            metadata={
                **self.metadata,
                "sample_type": self.sample_type,
                "confidence": self.confidence,
                "hash_id": self.hash_id
            }
        )


# ==================== 基础生成器接口 ====================

class BaseGenerator:
    """基础生成器抽象类 - Open-Closed Principle"""
    
    def generate(self, config: SampleConfig) -> List[IntentSample]:
        """生成样本的主方法"""
        raise NotImplementedError
    
    def validate_sample(self, sample: IntentSample) -> bool:
        """验证样本质量"""
        return (
            sample.instruction.strip() != "" and
            bool(sample.intent_json) and
            sample.confidence >= 0.1
        )


# ==================== 具体生成器实现 ====================

class IntentBasedGenerator(BaseGenerator):
    """意图驱动的反向生成器 - Single Responsibility"""
    
    def __init__(self, orm_config_path: str = "configs/orm_def.yaml"):
        self.templates = self._load_intent_templates(orm_config_path)
        self.intent_types = list(self.templates.keys())
    
    def _load_intent_templates(self, config_path: str) -> Dict[str, Any]:
        """加载意图模板"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            return config.get("intent_templates", {})
        except Exception as e:
            logger.warning(f"加载意图模板失败: {e}, 使用默认模板")
            return self._get_default_templates()
    
    def _get_default_templates(self) -> Dict[str, Any]:
        """默认意图模板"""
        return {
            "ADD_COLUMN": {
                "intent": "ADD_COLUMN",
                "table": "default_table",
                "instructions": [
                    "添加一个{data_type}类型的{field_name}字段",
                    "新增{field_name}列，类型为{data_type}",
                    "给表增加{field_name}字段"
                ]
            },
            "DELETE_COLUMN": {
                "intent": "DELETE_COLUMN", 
                "table": "default_table",
                "instructions": [
                    "删除{field_name}字段",
                    "移除{field_name}列",
                    "去掉{field_name}这个字段"
                ]
            }
        }
    
    def generate(self, config: SampleConfig) -> List[IntentSample]:
        """生成基础意图样本"""
        samples = []
        samples_per_type = config.base_samples_count // len(self.intent_types)
        
        for intent_type in self.intent_types:
            type_samples = self._generate_for_intent_type(intent_type, samples_per_type)
            samples.extend(type_samples)
        
        return samples
    
    def _generate_for_intent_type(self, intent_type: str, count: int) -> List[IntentSample]:
        """为特定意图类型生成样本"""
        samples = []
        template = self.templates.get(intent_type, {})
        instructions = template.get("instructions", [])
        
        for _ in range(count):
            # 生成随机字段名和数据类型
            field_name = self._generate_field_name()
            data_type = random.choice(["VARCHAR(100)", "INTEGER", "BOOLEAN", "TIMESTAMP"])
            
            # 选择指令模板
            instruction_template = random.choice(instructions)
            instruction = instruction_template.format(
                field_name=field_name,
                data_type=data_type
            )
            
            # 构建意图JSON
            intent_json = self._build_intent_json(intent_type, field_name, data_type)
            
            sample = IntentSample(
                intent_json=intent_json,
                instruction=instruction,
                sample_type="base",
                confidence=1.0,
                metadata={"generated_by": "intent_based_generator"}
            )
            
            if self.validate_sample(sample):
                samples.append(sample)
        
        return samples
    
    def _generate_field_name(self) -> str:
        """生成随机字段名"""
        prefixes = ["user", "order", "product", "account", "system"]
        suffixes = ["id", "name", "status", "time", "count", "flag"]
        return f"{random.choice(prefixes)}_{random.choice(suffixes)}"
    
    def _build_intent_json(self, intent_type: str, field_name: str, data_type: str) -> Dict[str, Any]:
        """构建意图JSON结构"""
        base_intent = {
            "intent": intent_type,
            "table": "default_table"
        }
        
        if intent_type == "ADD_COLUMN":
            base_intent["props"] = {
                "name": field_name,
                "stdSqlType": data_type,
                "nullable": True,
                "comment": f"{field_name}字段"
            }
        elif intent_type == "DELETE_COLUMN":
            base_intent["props"] = {"name": field_name}
        
        return base_intent


class ParaphraseGenerator(BaseGenerator):
    """改写生成器 - 创建多样化表达"""
    
    def __init__(self):
        self.paraphrase_patterns = {
            "formal": ["请{action}{object}", "需要{action}{object}", "要求{action}{object}"],
            "casual": ["{action}一下{object}", "帮忙{action}{object}", "{action}{object}吧"],
            "brief": ["{action}{object}", "{object}{action}"]
        }
    
    def generate_from_base(self, base_samples: List[IntentSample], variants: int = 4) -> List[IntentSample]:
        """从基础样本生成改写变体"""
        paraphrased = []
        
        for base_sample in base_samples:
            for i in range(variants):
                variant = self._create_paraphrase(base_sample, i)
                if variant and self.validate_sample(variant):
                    paraphrased.append(variant)
        
        return paraphrased
    
    def _create_paraphrase(self, base_sample: IntentSample, variant_id: int) -> Optional[IntentSample]:
        """创建单个改写样本"""
        # 简单的改写策略：修改表达方式
        original_instruction = base_sample.instruction
        
        # 基于变体ID选择不同的改写策略
        strategies = ["formal", "casual", "brief"]
        strategy = strategies[variant_id % len(strategies)]
        
        # 简单的改写逻辑
        paraphrased_instruction = self._apply_paraphrase_strategy(
            original_instruction, strategy
        )
        
        if paraphrased_instruction == original_instruction:
            return None
        
        return IntentSample(
            intent_json=base_sample.intent_json.copy(),
            instruction=paraphrased_instruction,
            sample_type="paraphrase",
            confidence=base_sample.confidence * 0.9,  # 略降低置信度
            metadata={
                **base_sample.metadata,
                "paraphrase_strategy": strategy,
                "base_sample_hash": base_sample.hash_id
            }
        )
    
    def _apply_paraphrase_strategy(self, instruction: str, strategy: str) -> str:
        """应用改写策略"""
        # 简化的改写逻辑
        if strategy == "casual" and "添加" in instruction:
            return instruction.replace("添加", "加上")
        elif strategy == "brief" and "字段" in instruction:
            return instruction.replace("字段", "列")
        elif strategy == "formal" and not instruction.startswith("请"):
            return f"请{instruction}"
        
        return instruction


class RobustnessGenerator(BaseGenerator):
    """鲁棒性样本生成器 - 生成对抗和边界样本"""
    
    def generate_adversarial(self, base_samples: List[IntentSample], ratio: float = 0.3) -> List[IntentSample]:
        """生成对抗样本"""
        count = int(len(base_samples) * ratio)
        adversarial = []
        
        selected_samples = random.sample(base_samples, min(count, len(base_samples)))
        
        for sample in selected_samples:
            adversarial_sample = self._create_adversarial_sample(sample)
            if adversarial_sample:
                adversarial.append(adversarial_sample)
        
        return adversarial
    
    def _create_adversarial_sample(self, base_sample: IntentSample) -> Optional[IntentSample]:
        """创建对抗样本"""
        # 扰动策略：添加干扰词
        disturbed_instruction = self._add_noise_to_instruction(base_sample.instruction)
        
        return IntentSample(
            intent_json=base_sample.intent_json.copy(),
            instruction=disturbed_instruction,
            sample_type="adversarial",
            confidence=base_sample.confidence * 0.7,
            metadata={
                **base_sample.metadata,
                "disturbance_type": "noise_injection"
            }
        )
    
    def _add_noise_to_instruction(self, instruction: str) -> str:
        """向指令添加噪声"""
        noise_words = ["额外的", "临时", "可能", "大概", "或许"]
        noise = random.choice(noise_words)
        
        # 简单的噪声注入
        if "字段" in instruction:
            return instruction.replace("字段", f"{noise}字段")
        
        return f"{noise}{instruction}"
    
    def generate_boundary_samples(self, count: int = 50) -> List[IntentSample]:
        """生成边界样本"""
        boundary_samples = []
        
        # 生成模糊、不完整的指令
        ambiguous_instructions = [
            "添加字段",  # 缺少字段名
            "删除列",    # 缺少列名
            "修改",      # 极度不完整
            "表操作",    # 模糊意图
        ]
        
        for i, instruction in enumerate(ambiguous_instructions * (count // len(ambiguous_instructions) + 1))[:count]:
            sample = IntentSample(
                intent_json={"intent": "UNKNOWN", "table": "unknown"},
                instruction=instruction,
                sample_type="boundary",
                confidence=0.1,
                metadata={"boundary_type": "incomplete_instruction"}
            )
            boundary_samples.append(sample)
        
        return boundary_samples


# ==================== 主要的增强采样管道 ====================

class EnhancedSamplingPipeline:
    """增强采样管道 - Dependency Inversion Principle"""
    
    def __init__(self, config_path: str = "configs/sampling_config.yaml"):
        self.config = self._load_config(config_path)
        self.sample_config = self._create_sample_config()
        
        # 依赖注入 - 可以替换具体实现
        self.intent_generator = IntentBasedGenerator()
        self.paraphrase_generator = ParaphraseGenerator()
        self.robustness_generator = RobustnessGenerator()
        self.weighted_sampler = EnhancedSamplingAdapter()
        
        # 去重缓存
        self.seen_hashes: Set[str] = set()
        
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.warning(f"加载配置失败: {e}, 使用默认配置")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """默认配置"""
        return {
            "base_generation": {"base_samples_count": 200},
            "paraphrase_generation": {"paraphrase_variants": 4},
            "robustness_generation": {
                "adversarial_ratio": 0.3,
                "boundary_samples": 50,
                "negative_samples": 100
            },
            "quality_control": {"quality_threshold": 0.6}
        }
    
    def _create_sample_config(self) -> SampleConfig:
        """创建采样配置对象"""
        base_config = self.config.get("base_generation", {})
        paraphrase_config = self.config.get("paraphrase_generation", {})
        robustness_config = self.config.get("robustness_generation", {})
        quality_config = self.config.get("quality_control", {})
        
        return SampleConfig(
            base_samples_count=base_config.get("base_samples_count", 200),
            paraphrase_variants=paraphrase_config.get("paraphrase_variants", 4),
            adversarial_ratio=robustness_config.get("adversarial_ratio", 0.3),
            boundary_samples=robustness_config.get("boundary_samples", 50),
            negative_samples=robustness_config.get("negative_samples", 100),
            quality_threshold=quality_config.get("quality_threshold", 0.6)
        )
    
    def generate_complete_dataset(self) -> List[TrainingSample]:
        """生成完整的训练数据集 - 主要接口方法"""
        logger.info("开始生成增强采样数据集...")
        
        all_samples = []
        
        # 1. 基础样本生成
        logger.info("生成基础意图样本...")
        base_samples = self.intent_generator.generate(self.sample_config)
        all_samples.extend(base_samples)
        
        # 2. 多样化指令生成（改写）
        logger.info("生成改写变体...")
        paraphrase_samples = self.paraphrase_generator.generate_from_base(
            base_samples, self.sample_config.paraphrase_variants
        )
        all_samples.extend(paraphrase_samples)
        
        # 3. 鲁棒性样本生成
        logger.info("生成对抗样本...")
        adversarial_samples = self.robustness_generator.generate_adversarial(
            base_samples, self.sample_config.adversarial_ratio
        )
        all_samples.extend(adversarial_samples)
        
        logger.info("生成边界样本...")
        boundary_samples = self.robustness_generator.generate_boundary_samples(
            self.sample_config.boundary_samples
        )
        all_samples.extend(boundary_samples)
        
        # 4. 去重和质量控制
        logger.info("执行去重和质量控制...")
        filtered_samples = self._apply_quality_control(all_samples)
        
        # 5. 转换为TrainingSample格式
        training_samples = [sample.to_training_sample() for sample in filtered_samples]
        
        logger.info(f"生成完成！总样本数: {len(training_samples)}")
        return training_samples
    
    def _apply_quality_control(self, samples: List[IntentSample]) -> List[IntentSample]:
        """应用质量控制和去重"""
        filtered = []
        
        for sample in samples:
            # 去重检查
            if sample.hash_id in self.seen_hashes:
                continue
            
            # 质量检查
            if sample.confidence < self.sample_config.quality_threshold:
                continue
            
            # 基础验证
            if not self._validate_sample_content(sample):
                continue
            
            self.seen_hashes.add(sample.hash_id)
            filtered.append(sample)
        
        return filtered
    
    def _validate_sample_content(self, sample: IntentSample) -> bool:
        """验证样本内容"""
        # 基本内容检查
        if not sample.instruction.strip():
            return False
        
        if not sample.intent_json:
            return False
        
        # 长度检查
        if len(sample.instruction) < 3 or len(sample.instruction) > 200:
            return False
        
        return True
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取生成统计信息"""
        return {
            "total_samples": len(self.seen_hashes),
            "config": self.sample_config.__dict__,
            "generators": {
                "intent_generator": type(self.intent_generator).__name__,
                "paraphrase_generator": type(self.paraphrase_generator).__name__,
                "robustness_generator": type(self.robustness_generator).__name__
            }
        }


# ==================== 便捷接口函数 ====================

def create_enhanced_pipeline(config_path: str = "configs/sampling_config.yaml") -> EnhancedSamplingPipeline:
    """创建增强采样管道的便捷函数"""
    return EnhancedSamplingPipeline(config_path)


def generate_enhanced_dataset(config_path: str = "configs/sampling_config.yaml") -> List[TrainingSample]:
    """一键生成增强数据集"""
    pipeline = create_enhanced_pipeline(config_path)
    return pipeline.generate_complete_dataset()


# ==================== 测试函数 ====================

def test_pipeline():
    """测试管道功能"""
    logger.info("开始测试增强采样管道...")
    
    # 创建临时配置
    test_config = {
        "base_generation": {"base_samples_count": 10},
        "paraphrase_generation": {"paraphrase_variants": 2},
        "robustness_generation": {
            "adversarial_ratio": 0.2,
            "boundary_samples": 5,
            "negative_samples": 5
        },
        "quality_control": {"quality_threshold": 0.1}
    }
    
    # 保存临时配置
    temp_config_path = "test_sampling_config.yaml"
    with open(temp_config_path, 'w', encoding='utf-8') as f:
        yaml.dump(test_config, f, ensure_ascii=False)
    
    try:
        # 测试管道
        pipeline = EnhancedSamplingPipeline(temp_config_path)
        samples = pipeline.generate_complete_dataset()
        
        logger.info(f"测试完成！生成了 {len(samples)} 个样本")
        logger.info(f"统计信息: {pipeline.get_statistics()}")
        
        # 检查样本类型分布
        sample_types = defaultdict(int)
        for sample in samples:
            sample_type = sample.metadata.get("sample_type", "unknown")
            sample_types[sample_type] += 1
        
        logger.info(f"样本类型分布: {dict(sample_types)}")
        
    finally:
        # 清理临时文件
        Path(temp_config_path).unlink(missing_ok=True)


if __name__ == "__main__":
    test_pipeline()