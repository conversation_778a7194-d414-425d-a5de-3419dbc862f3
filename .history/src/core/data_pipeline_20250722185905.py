"""
数据生成Pipeline：整合所有数据生成和质量控制流程
"""

import asyncio
import json
import os
from datetime import datetime
from typing import List, Dict, Any, Optional
from loguru import logger

from src.models.intent_models import TrainingSample
from src.core.data_generator import ReverseDataGenerator
from src.core.data_augmenter import DataAugmenter
from src.core.data_quality import DataQualityController
from src.utils.config import config_manager

class DataGenerationPipeline:
    """数据生成Pipeline"""
    
    def __init__(self):
        self.generator = None
        self.augmenter = None
        self.quality_controller = None
        self.output_dir = config_manager.get("data.processed_data_path", "./data/processed")
        
    async def initialize(self):
        """初始化所有组件"""
        logger.info("🚀 初始化数据生成Pipeline...")
        
        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 初始化组件
        self.generator = ReverseDataGenerator()
        self.augmenter = DataAugmenter()
        self.quality_controller = DataQualityController()
        
        # 初始化质量控制器
        await self.quality_controller.initialize()
        
        logger.info("✅ 数据生成Pipeline初始化完成")
    
    async def run_full_pipeline(self, 
                               samples_per_intent: int = 500,
                               adversarial_ratio: float = 0.1,
                               boundary_cases: int = 100,
                               fuzzy_samples: int = 200,
                               negative_samples: int = 100) -> Dict[str, Any]:
        """运行完整的数据生成Pipeline"""
        
        logger.info("🔄 开始运行完整数据生成Pipeline...")
        
        try:
            # Step 1: 生成基础训练样本
            logger.info("📊 Step 1: 生成基础训练样本...")
            base_samples = await self.generator.generate_training_dataset(samples_per_intent)
            logger.info(f"✅ 基础样本生成完成，数量: {len(base_samples)}")
            
            # Step 2: 生成模糊样本
            logger.info("🔍 Step 2: 生成模糊样本...")
            fuzzy_samples_list = self.generator.generate_fuzzy_samples(fuzzy_samples)
            logger.info(f"✅ 模糊样本生成完成，数量: {len(fuzzy_samples_list)}")
            
            # Step 3: 数据增强
            logger.info("🎯 Step 3: 生成增强样本...")
            adversarial_samples = self.augmenter.generate_adversarial_samples(
                base_samples, adversarial_ratio
            )
            boundary_samples = self.augmenter.generate_boundary_cases(
                base_samples, boundary_cases
            )
            negative_samples_list = self.augmenter.generate_negative_samples(negative_samples)
            
            logger.info(f"✅ 增强样本生成完成")
            logger.info(f"   - 对抗样本: {len(adversarial_samples)}")
            logger.info(f"   - 边界样本: {len(boundary_samples)}")
            logger.info(f"   - 负样本: {len(negative_samples_list)}")
            
            # Step 4: 合并所有样本
            logger.info("🔗 Step 4: 合并所有样本...")
            all_samples = (base_samples + fuzzy_samples_list + adversarial_samples + 
                          boundary_samples + negative_samples_list)
            logger.info(f"✅ 样本合并完成，总数量: {len(all_samples)}")
            
            # Step 5: 质量控制
            logger.info("🔧 Step 5: 质量控制处理...")
            
            # 验证样本
            valid_samples, validation_errors = self.quality_controller.validate_samples(all_samples)
            logger.info(f"✅ 样本验证完成，有效样本: {len(valid_samples)}")
            
            # 去重处理
            clean_samples = self.quality_controller.deduplicate_samples(valid_samples)
            logger.info(f"✅ 去重处理完成，最终样本数: {len(clean_samples)}")
            
            # Step 6: 质量评估
            logger.info("📈 Step 6: 质量评估...")
            quality_report = self.quality_controller.generate_quality_report(
                clean_samples, validation_errors
            )
            
            # Step 7: 保存结果
            logger.info("💾 Step 7: 保存生成结果...")
            saved_files = await self.save_results(clean_samples, quality_report)
            
            # 生成Pipeline报告
            pipeline_report = {
                "timestamp": datetime.now().isoformat(),
                "configuration": {
                    "samples_per_intent": samples_per_intent,
                    "adversarial_ratio": adversarial_ratio,
                    "boundary_cases": boundary_cases,
                    "fuzzy_samples": fuzzy_samples,
                    "negative_samples": negative_samples
                },
                "generation_statistics": {
                    "base_samples": len(base_samples),
                    "fuzzy_samples": len(fuzzy_samples_list),
                    "adversarial_samples": len(adversarial_samples),
                    "boundary_samples": len(boundary_samples),
                    "negative_samples": len(negative_samples_list),
                    "total_before_quality_control": len(all_samples),
                    "valid_samples": len(valid_samples),
                    "final_samples": len(clean_samples)
                },
                "quality_report": quality_report,
                "saved_files": saved_files
            }
            
            logger.info("🎉 数据生成Pipeline执行完成！")
            logger.info(f"📊 最终生成样本数: {len(clean_samples)}")
            logger.info(f"📁 保存文件: {saved_files}")
            
            return pipeline_report
            
        except Exception as e:
            logger.error(f"❌ 数据生成Pipeline执行失败: {e}")
            raise
    
    async def save_results(self, samples: List[TrainingSample], 
                          quality_report: Dict[str, Any]) -> List[str]:
        """保存生成结果"""
        saved_files = []
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        try:
            # 1. 保存训练样本
            samples_file = os.path.join(self.output_dir, f"training_samples_{timestamp}.json")
            samples_data = []
            
            for sample in samples:
                sample_dict = {
                    "instruction": sample.instruction,
                    "output": [intent.dict() if hasattr(intent, 'dict') else intent 
                              for intent in sample.output],
                    "metadata": sample.metadata
                }
                samples_data.append(sample_dict)
            
            with open(samples_file, 'w', encoding='utf-8') as f:
                json.dump(samples_data, f, ensure_ascii=False, indent=2)
            
            saved_files.append(samples_file)
            logger.info(f"✅ 训练样本已保存: {samples_file}")
            
            # 2. 保存质量报告
            report_file = os.path.join(self.output_dir, f"quality_report_{timestamp}.json")
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(quality_report, f, ensure_ascii=False, indent=2)
            
            saved_files.append(report_file)
            logger.info(f"✅ 质量报告已保存: {report_file}")
            
            # 3. 按意图类型分类保存
            intent_samples = self._group_samples_by_intent(samples)
            for intent_type, intent_samples_list in intent_samples.items():
                intent_file = os.path.join(
                    self.output_dir, 
                    f"samples_{intent_type.lower()}_{timestamp}.json"
                )
                
                intent_data = []
                for sample in intent_samples_list:
                    sample_dict = {
                        "instruction": sample.instruction,
                        "output": [intent.dict() if hasattr(intent, 'dict') else intent 
                                  for intent in sample.output],
                        "metadata": sample.metadata
                    }
                    intent_data.append(sample_dict)
                
                with open(intent_file, 'w', encoding='utf-8') as f:
                    json.dump(intent_data, f, ensure_ascii=False, indent=2)
                
                saved_files.append(intent_file)
            
            # 4. 生成训练集和验证集
            train_samples, val_samples = self._split_train_validation(samples)
            
            # 保存训练集
            train_file = os.path.join(self.output_dir, f"train_set_{timestamp}.json")
            train_data = [
                {
                    "instruction": s.instruction,
                    "output": [intent.dict() if hasattr(intent, 'dict') else intent 
                              for intent in s.output],
                    "metadata": s.metadata
                }
                for s in train_samples
            ]
            
            with open(train_file, 'w', encoding='utf-8') as f:
                json.dump(train_data, f, ensure_ascii=False, indent=2)
            saved_files.append(train_file)
            
            # 保存验证集
            val_file = os.path.join(self.output_dir, f"val_set_{timestamp}.json")
            val_data = [
                {
                    "instruction": s.instruction,
                    "output": [intent.dict() if hasattr(intent, 'dict') else intent 
                              for intent in s.output],
                    "metadata": s.metadata
                }
                for s in val_samples
            ]
            
            with open(val_file, 'w', encoding='utf-8') as f:
                json.dump(val_data, f, ensure_ascii=False, indent=2)
            saved_files.append(val_file)
            
            logger.info(f"✅ 训练集({len(train_samples)})和验证集({len(val_samples)})已保存")
            
            return saved_files
            
        except Exception as e:
            logger.error(f"❌ 保存结果失败: {e}")
            raise
    
    def _group_samples_by_intent(self, samples: List[TrainingSample]) -> Dict[str, List[TrainingSample]]:
        """按意图类型分组样本"""
        grouped = {}
        
        for sample in samples:
            intent_type = sample.metadata.get("intent_type", "UNKNOWN")
            if intent_type not in grouped:
                grouped[intent_type] = []
            grouped[intent_type].append(sample)
        
        return grouped
    
    def _split_train_validation(self, samples: List[TrainingSample], 
                               val_ratio: float = 0.2) -> tuple:
        """划分训练集和验证集"""
        # 按意图类
    def _split_train_validation(self, samples: List[TrainingSample], 
                                val_ratio: float = 0.2) -> tuple:
            """划分训练集和验证集"""
            # 按意图类型分层划分，确保每种意图在训练集和验证集中都有代表
            intent_groups = self._group_samples_by_intent(samples)
            
            train_samples = []
            val_samples = []
            
            for intent_type, intent_samples in intent_groups.items():
                # 随机打乱
                import random
                shuffled_samples = intent_samples.copy()
                random.shuffle(shuffled_samples)
                
                # 计算验证集数量
                val_count = max(1, int(len(shuffled_samples) * val_ratio))
                
                # 划分
                val_samples.extend(shuffled_samples[:val_count])
                train_samples.extend(shuffled_samples[val_count:])
            
            # 再次打乱
            random.shuffle(train_samples)
            random.shuffle(val_samples)
            
            return train_samples, val_samples
        
        async def run_incremental_generation(self, intent_types: List[str], 
                                        samples_per_intent: int = 100) -> Dict[str, Any]:
            """增量生成特定意图类型的样本"""
            logger.info(f"🔄 开始增量生成，意图类型: {intent_types}")
            
            all_samples = []
            
            for intent_type in intent_types:
                try:
                    logger.info(f"📊 生成 {intent_type} 类型样本...")
                    samples = self.generator.generate_single_intent_samples(intent_type, samples_per_intent)
                    all_samples.extend(samples)
                    logger.info(f"✅ {intent_type} 生成完成，数量: {len(samples)}")
                except Exception as e:
                    logger.error(f"❌ 生成 {intent_type} 失败: {e}")
                    continue
            
            # 质量控制
            logger.info("🔧 执行质量控制...")
            valid_samples, validation_errors = self.quality_controller.validate_samples(all_samples)
            clean_samples = self.quality_controller.deduplicate_samples(valid_samples)
            
            # 保存结果
            saved_files = await self.save_results(clean_samples, {})
            
            result = {
                "intent_types": intent_types,
                "generated_samples": len(clean_samples),
                "saved_files": saved_files,
                "timestamp": datetime.now().isoformat()
            }
            
            logger.info(f"✅ 增量生成完成，生成样本数: {len(clean_samples)}")
            return result