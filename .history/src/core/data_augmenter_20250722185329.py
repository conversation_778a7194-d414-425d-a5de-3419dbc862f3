"""
数据增强器：生成对抗样本和边界案例
"""

import random
import re
from typing import List, Dict, Any
from loguru import logger

from src.models.intent_models import TrainingSample, Intent

class DataAugmenter:
    """数据增强器：生成对抗样本和边界案例"""
    
    def __init__(self):
        self.noise_patterns = {
            "spelling_errors": ["字段", "字端", "字断"],
            "punctuation": ["。", "，", "！", "？", "、"],
            "extra_spaces": ["  ", "   ", " "],
            "synonyms": {
                "添加": ["加上", "增加", "新增", "加入"],
                "删除": ["移除", "去掉", "清除", "取消"],
                "字段": ["属性", "列", "field"],
                "表": ["表格", "table"]
            }
        }
    
    def generate_adversarial_samples(self, samples: List[TrainingSample], ratio: float = 0.1) -> List[TrainingSample]:
        """生成对抗样本"""
        adversarial_samples = []
        sample_count = int(len(samples) * ratio)
        
        logger.info(f"开始生成对抗样本，目标数量: {sample_count}")
        
        selected_samples = random.sample(samples, min(sample_count, len(samples)))
        
        for sample in selected_samples:
            try:
                # 应用不同类型的噪声
                noisy_instructions = []
                
                # 1. 拼写错误
                noisy_instructions.append(self._add_spelling_errors(sample.instruction))
                
                # 2. 标点符号噪声
                noisy_instructions.append(self._add_punctuation_noise(sample.instruction))
                
                # 3. 空格噪声
                noisy_instructions.append(self._add_space_noise(sample.instruction))
                
                # 4. 词序混乱
                noisy_instructions.append(self._shuffle_words(sample.instruction))
                
                # 5. 同义词干扰
                noisy_instructions.append(self._add_synonym_confusion(sample.instruction))
                
                # 为每个噪声版本创建样本
                for noisy_instruction in noisy_instructions:
                    if noisy_instruction != sample.instruction:  # 确保确实发生了变化
                        adversarial_sample = TrainingSample(
                            instruction=noisy_instruction,
                            output=sample.output,
                            metadata={
                                **sample.metadata,
                                "is_adversarial": True,
                                "original_instruction": sample.instruction,
                                "noise_type": "mixed"
                            }
                        )
                        adversarial_samples.append(adversarial_sample)
                
            except Exception as e:
                logger.warning(f"生成对抗样本失败: {e}")
                continue
        
        logger.info(f"成功生成 {len(adversarial_samples)} 个对抗样本")
        return adversarial_samples
    
    def _add_spelling_errors(self, text: str) -> str:
        """添加拼写错误"""
        for correct, errors in self.noise_patterns["spelling_errors"]:
            if correct in text and random.random() < 0.3:
                error = random.choice(errors)
                text = text.replace(correct, error, 1)
                break
        return text
    
    def _add_punctuation_noise(self, text: str) -> str:
        """添加标点符号噪声"""
        if random.random() < 0.4:
            # 随机添加标点
            punct = random.choice(self.noise_patterns["punctuation"])
            position = random.randint(0, len(text))
            text = text[:position] + punct + text[position:]
        
        if random.random() < 0.3:
            # 移除现有标点
            text = re.sub(r'[。，！？、]', '', text)
        
        return text
    
    def _add_space_noise(self, text: str) -> str:
        """添加空格噪声"""
        if random.random() < 0.5:
            # 添加额外空格
            words = text.split()
            space_type = random.choice(self.noise_patterns["extra_spaces"])
            text = space_type.join(words)
        
        if random.random() < 0.3:
            # 移除所有空格
            text = text.replace(" ", "")
        
        return text
    
    def _shuffle_words(self, text: str) -> str:
        """打乱词序"""
        if random.random() < 0.2:  # 较低概率，因为这会显著改变语义
            words = text.split()
            if len(words) > 2:
                # 只交换相邻的词
                idx = random.randint(0, len(words) - 2)
                words[idx], words[idx + 1] = words[idx + 1], words[idx]
                text = " ".join(words)
        return text
    
    def _add_synonym_confusion(self, text: str) -> str:
        """添加同义词混淆"""
        for original, synonyms in self.noise_patterns["synonyms"].items():
            if original in text and random.random() < 0.4:
                synonym = random.choice(synonyms)
                text = text.replace(original, synonym, 1)
                break
        return text
    
    def generate_boundary_cases(self, samples: List[TrainingSample], count: int = 100) -> List[TrainingSample]:
        """生成边界案例"""
        boundary_samples = []
        
        logger.info(f"开始生成边界案例，目标数量: {count}")
        
        for _ in range(count):
            try:
                sample = random.choice(samples)
                boundary_instruction = self._create_boundary_case(sample.instruction)
                
                boundary_sample = TrainingSample(
                    instruction=boundary_instruction,
                    output=sample.output,
                    metadata={
                        **sample.metadata,
                        "is_boundary_case": True,
                        "original_instruction": sample.instruction
                    }
                )
                boundary_samples.append(boundary_sample)
                
            except Exception as e:
                logger.warning(f"生成边界案例失败: {e}")
                continue
        
        logger.info(f"成功生成 {len(boundary_samples)} 个边界案例")
        return boundary_samples
    
    def _create_boundary_case(self, text: str) -> str:
        """创建边界案例"""
        boundary_strategies = [
            self._make_very_short,
            self._make_very_long,
            self._add_irrelevant_content,
            self._make_incomplete,
            self._add_ambiguous_terms
        ]
        
        strategy = random.choice(boundary_strategies)
        return strategy(text)
    
    def _make_very_short(self, text: str) -> str:
        """创建超短文本"""
        words = text.split()
        if len(words) > 2:
            return " ".join(words[:2])
        return text
    
    def _make_very_long(self, text: str) -> str:
        """创建超长文本"""
        fillers = ["请帮我", "麻烦您", "我想要", "能否", "可以吗", "谢谢", "在系统中"]
        filler = random.choice(fillers)
        return f"{filler}{text}，{random.choice(['谢谢', '请确认', '尽快处理'])}"
    
    def _add_irrelevant_content(self, text: str) -> str:
        """添加无关内容"""
        irrelevant = ["顺便问一下", "另外", "还有", "对了"]
        irrelevant_content = ["天气怎么样", "吃饭了吗", "今天是星期几"]
        
        prefix = random.choice(irrelevant)
        suffix = random.choice(irrelevant_content)
        return f"{text}，{prefix}{suffix}"
    
    def _make_incomplete(self, text: str) -> str:
        """创建不完整文本"""
        if "字段" in text:
            # 移除字段名或表名
            text = re.sub(r'[一-龯