#!/usr/bin/env python3
"""
完整的多模型互评Pipeline启动脚本
支持通义千问(<PERSON>wen) + 豆包(Doubao)的大模型互评数据生成系统

功能特性:
- 配置验证和模型连接检查
- 成本估算和预算控制
- 测试模式和大规模生成
- 详细的执行监控和结果分析
- 完整的错误处理和降级策略
"""

import asyncio
import argparse
import sys
import os
import json
import yaml
import tempfile
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List, Tuple
import traceback

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入必要模块
try:
    from src.core.distilabel_pipeline import (
        create_multi_model_pipeline, 
        run_multi_model_pipeline,
        _create_llm_instance
    )
    from src.utils.logger import logger
    from src.utils.config import config_manager
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保已安装所有依赖并正确设置项目路径")
    sys.exit(1)


class MultiModelRunner:
    """多模型互评Pipeline运行器"""
    
    def __init__(self):
        self.start_time = None
        self.config = None
        self.temp_config_path = None
        
    def create_arg_parser(self) -> argparse.ArgumentParser:
        """创建命令行参数解析器"""
        parser = argparse.ArgumentParser(
            description="🤖 多模型互评数据生成系统 - 通义千问 + 豆包",
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
🚀 使用示例:

基础操作:
  python scripts/data_generation/multi_model_runner.py
  python scripts/data_generation/multi_model_runner.py --test-mode
  python scripts/data_generation/multi_model_runner.py --check-models

高级操作:
  # 指定配置和输出路径
  python scripts/data_generation/multi_model_runner.py \\
    --config configs/my_config.yaml \\
    --output data/my_dataset \\
    --samples 100

  # 成本控制模式
  python scripts/data_generation/multi_model_runner.py \\
    --cost-estimate \\
    --max-cost 10.0 \\
    --samples 200

  # 调试模式
  python scripts/data_generation/multi_model_runner.py \\
    --test-mode \\
    --samples 5 \\
    --verbose \\
    --save-debug-info

🔧 配置管理:
  # 验证配置文件
  python scripts/data_generation/multi_model_runner.py --validate-config
  
  # 生成默认配置
  python scripts/data_generation/multi_model_runner.py --generate-config

📊 监控功能:
  # 实时监控
  python scripts/data_generation/multi_model_runner.py --monitor-only
  
  # 质量分析
  python scripts/data_generation/multi_model_runner.py --analyze-quality data/output
            """
        )
        
        # 基础参数
        parser.add_argument(
            "--config", "-c",
            type=str,
            default="configs/distilabel_config.yaml",
            help="配置文件路径 (默认: configs/distilabel_config.yaml)"
        )
        
        parser.add_argument(
            "--output", "-o",
            type=str,
            default=None,
            help="输出目录路径 (默认: 自动生成时间戳目录)"
        )
        
        parser.add_argument(
            "--samples", "-s",
            type=int,
            default=None,
            help="每种意图的样本数量 (覆盖配置文件设置)"
        )
        
        # 运行模式
        parser.add_argument(
            "--test-mode", "-t",
            action="store_true",
            help="🧪 测试模式: 生成少量数据用于验证"
        )
        
        parser.add_argument(
            "--dry-run", "-d",
            action="store_true",
            help="🔍 空运行: 只验证配置，不执行实际生成"
        )
        
        parser.add_argument(
            "--check-models", "-k",
            action="store_true",
            help="🔌 检查模型连接状态"
        )
        
        # 质量控制
        parser.add_argument(
            "--min-quality",
            type=float,
            default=None,
            help="最低质量分数阈值 (1.0-5.0)"
        )
        
        parser.add_argument(
            "--max-samples",
            type=int,
            default=None,
            help="总样本数量上限"
        )
        
        # 成本控制
        parser.add_argument(
            "--cost-estimate", "-e",
            action="store_true",
            help="💰 估算API调用成本"
        )
        
        parser.add_argument(
            "--max-cost",
            type=float,
            default=None,
            help="最大预算限制 (美元)"
        )
        
        parser.add_argument(
            "--cost-per-1k",
            type=float,
            default=0.005,
            help="每1K token的成本 (默认: $0.005)"
        )
        
        # 调试和监控
        parser.add_argument(
            "--verbose", "-v",
            action="store_true",
            help="🔊 详细输出模式"
        )
        
        parser.add_argument(
            "--save-debug-info",
            action="store_true",
            help="💾 保存调试信息和中间结果"
        )
        
        parser.add_argument(
            "--monitor-only",
            action="store_true",
            help="📊 仅监控模式，不执行生成"
        )
        
        # 配置管理
        parser.add_argument(
            "--validate-config",
            action="store_true",
            help="✅ 验证配置文件"
        )
        
        parser.add_argument(
            "--generate-config",
            type=str,
            metavar="OUTPUT_PATH",
            help="📄 生成默认配置文件"
        )
        
        # 数据分析
        parser.add_argument(
            "--analyze-quality",
            type=str,
            metavar="DATA_PATH",
            help="📈 分析数据质量"
        )
        
        parser.add_argument(
            "--compare-models",
            nargs=2,
            metavar=("PATH1", "PATH2"),
            help="⚖️ 对比两个模型的生成结果"
        )
        
        # 高级选项
        parser.add_argument(
            "--batch-size",
            type=int,
            default=None,
            help="批处理大小"
        )
        
        parser.add_argument(
            "--max-retries",
            type=int,
            default=3,
            help="最大重试次数"
        )
        
        parser.add_argument(
            "--timeout",
            type=int,
            default=300,
            help="超时时间(秒)"
        )
        
        return parser
    
    def validate_multi_model_config(self, config_path: str) -> Tuple[bool, List[str]]:
        """验证多模型配置"""
        errors = []
        
        try:
            if not os.path.exists(config_path):
                errors.append(f"配置文件不存在: {config_path}")
                return False, errors
            
            with open(config_path, 'r', encoding='utf-8') as f:
                self.config = yaml.safe_load(f)
            
            # 检查必需的配置节
            required_sections = {
                'pipeline': '基础Pipeline配置',
                'distilabel': 'distilabel特定配置',
                'g_eval': 'G-Eval评估配置',
                'enhanced_mode': '增强模式配置',
                'quality_control': '质量控制配置',
                'output': '输出配置'
            }
            
            for section, desc in required_sections.items():
                if section not in self.config:
                    errors.append(f"缺少必需配置节 '{section}': {desc}")
            
            # 检查Pipeline模式
            pipeline_mode = self.config.get('pipeline', {}).get('mode', '')
            if 'multi_model' not in pipeline_mode:
                errors.append("Pipeline模式未设置为多模型模式，请设置 pipeline.mode 为 'distilabel_multi_model'")
            
            # 检查模型配置
            distilabel_config = self.config.get('distilabel', {})
            if 'primary_model' not in distilabel_config:
                errors.append("缺少主生成模型配置 (distilabel.primary_model)")
            else:
                primary_model = distilabel_config['primary_model']
                if 'name' not in primary_model:
                    errors.append("主模型缺少名称配置")
                if 'type' not in primary_model:
                    errors.append("主模型缺少类型配置")
            
            # 检查G-Eval配置
            g_eval_config = self.config.get('g_eval', {})
            if 'evaluator_model' not in g_eval_config:
                errors.append("缺少G-Eval评估模型配置 (g_eval.evaluator_model)")
            else:
                evaluator_model = g_eval_config['evaluator_model']
                if evaluator_model.get('type') == 'inference_endpoints':
                    api_key = evaluator_model.get('api_key', '')
                    if not api_key:
                        errors.append("豆包模型需要配置API密钥")
                    elif api_key.startswith('${') and api_key.endswith('}'):
                        # 检查环境变量
                        env_var = api_key[2:-1]
                        if not os.getenv(env_var):
                            errors.append(f"环境变量 {env_var} 未设置")
            
            if 'criteria' not in g_eval_config:
                errors.append("缺少G-Eval评估标准配置 (g_eval.criteria)")
            else:
                criteria = g_eval_config['criteria']
                if not isinstance(criteria, list) or len(criteria) == 0:
                    errors.append("G-Eval评估标准必须是非空列表")
            
            # 检查数值范围
            enhanced_mode = self.config.get('enhanced_mode', {})
            samples_per_intent = enhanced_mode.get('seed_samples_per_intent', 0)
            if not isinstance(samples_per_intent, int) or samples_per_intent <= 0:
                errors.append("seed_samples_per_intent 必须是正整数")
            
            # 检查质量阈值
            quality_thresholds = g_eval_config.get('quality_thresholds', {})
            for threshold_name, threshold_value in quality_thresholds.items():
                if isinstance(threshold_value, (int, float)):
                    if threshold_value < 0 or threshold_value > 5:
                        errors.append(f"质量阈值 {threshold_name} 超出有效范围 (0-5): {threshold_value}")
            
        except yaml.YAMLError as e:
            errors.append(f"YAML格式错误: {e}")
        except Exception as e:
            errors.append(f"配置验证失败: {e}")
        
        return len(errors) == 0, errors
    
    async def check_model_connectivity(self, config_path: str) -> Dict[str, Any]:
        """检查模型连接状态"""
        logger.info("🔍 检查模型连接状态...")
        
        results = {
            "qwen_model": {"status": False, "error": None, "response_time": None},
            "doubao_model": {"status": False, "error": None, "response_time": None},
            "overall_status": False
        }
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 检查通义千问模型
            try:
                logger.info("测试通义千问连接...")
                start_time = datetime.now()
                
                primary_model_config = config.get('distilabel', {}).get('primary_model', {})
                qwen_llm = _create_llm_instance(primary_model_config, "Qwen")
                
                # 简单测试
                test_prompt = "请回答: 1+1等于多少?"
                if hasattr(qwen_llm, 'generate'):
                    response = qwen_llm.generate([test_prompt])
                else:
                    response = await qwen_llm.agenerate([test_prompt])
                
                response_time = (datetime.now() - start_time).total_seconds()
                
                if response:
                    results["qwen_model"]["status"] = True
                    results["qwen_model"]["response_time"] = response_time
                    logger.info(f"✅ 通义千问连接正常 (响应时间: {response_time:.2f}s)")
                
            except Exception as e:
                results["qwen_model"]["error"] = str(e)
                logger.error(f"❌ 通义千问连接失败: {e}")
            
            # 检查豆包模型
            try:
                logger.info("测试豆包连接...")
                start_time = datetime.now()
                
                evaluator_config = config.get('g_eval', {}).get('evaluator_model', {})
                doubao_llm = _create_llm_instance(evaluator_config, "Doubao")
                
                # 简单测试
                test_prompt = "请评分: 这是一个测试"
                if hasattr(doubao_llm, 'generate'):
                    response = doubao_llm.generate([test_prompt])
                else:
                    response = await doubao_llm.agenerate([test_prompt])
                
                response_time = (datetime.now() - start_time).total_seconds()
                
                if response:
                    results["doubao_model"]["status"] = True
                    results["doubao_model"]["response_time"] = response_time
                    logger.info(f"✅ 豆包连接正常 (响应时间: {response_time:.2f}s)")
                
            except Exception as e:
                results["doubao_model"]["error"] = str(e)
                logger.error(f"❌ 豆包连接失败: {e}")
            
            # 设置总体状态
            results["overall_status"] = (
                results["qwen_model"]["status"] and 
                results["doubao_model"]["status"]
            )
            
        except Exception as e:
            logger.error(f"模型连接检查失败: {e}")
        
        return results
    
    def estimate_api_costs(self, config_path: str, samples: int, cost_per_1k: float = 0.005) -> Dict[str, Any]:
        """估算API调用成本"""
        logger.info("💰 估算API调用成本...")
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 估算参数
            intent_types = 4  # 意图类型数量
            avg_tokens_per_request = 350  # 平均每次请求token数
            
            # 计算请求数量
            total_intent_samples = samples * intent_types
            
            # Qwen调用: 生成 + 验证
            qwen_requests = total_intent_samples * 2
            qwen_tokens = qwen_requests * avg_tokens_per_request
            
            # Doubao调用: G-Eval评估
            doubao_requests = total_intent_samples
            doubao_tokens = doubao_requests * (avg_tokens_per_request + 150)  # 评估prompt更长
            
            # 成本计算
            qwen_cost = (qwen_tokens / 1000) * cost_per_1k
            doubao_cost = (doubao_tokens / 1000) * cost_per_1k
            total_cost = qwen_cost + doubao_cost
            
            # 时间估算
            avg_request_time = 2.5  # 秒
            estimated_time_minutes = (qwen_requests + doubao_requests) * avg_request_time / 60
            
            return {
                "samples_per_intent": samples,
                "total_intent_samples": total_intent_samples,
                "qwen_requests": qwen_requests,
                "doubao_requests": doubao_requests,
                "total_requests": qwen_requests + doubao_requests,
                "qwen_tokens": qwen_tokens,
                "doubao_tokens": doubao_tokens,
                "total_tokens": qwen_tokens + doubao_tokens,
                "qwen_cost": qwen_cost,
                "doubao_cost": doubao_cost,
                "total_cost": total_cost,
                "estimated_time_minutes": estimated_time_minutes,
                "cost_per_1k_tokens": cost_per_1k
            }
            
        except Exception as e:
            logger.error(f"成本估算失败: {e}")
            return {}
    
    def modify_config_for_options(self, config_path: str, args: argparse.Namespace) -> str:
        """根据命令行参数修改配置"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            modified = False
            
            # 修改样本数量
            if args.samples:
                config.setdefault('enhanced_mode', {})['seed_samples_per_intent'] = args.samples
                modified = True
            
            # 测试模式调整
            if args.test_mode:
                samples = args.samples or 5
                config.setdefault('enhanced_mode', {})['seed_samples_per_intent'] = samples
                config.setdefault('enhanced_mode', {})['evolution_generations'] = 1
                config.setdefault('enhanced_mode', {})['variants_per_generation'] = 1
                config.setdefault('distilabel', {})['batch_size'] = 2  # 小批次
                modified = True
                logger.info(f"🧪 测试模式: 每种意图{samples}个样本")
            
            # 调整批次大小
            if args.batch_size:
                config.setdefault('distilabel', {})['batch_size'] = args.batch_size
                modified = True
            
            # 调整质量阈值
            if args.min_quality:
                config.setdefault('g_eval', {}).setdefault('quality_thresholds', {})['min_overall_score'] = args.min_quality
                modified = True
            
            # 调整重试次数
            if args.max_retries:
                config.setdefault('error_handling', {})['max_retries'] = args.max_retries
                modified = True
            
            # 成本控制
            if args.max_cost:
                config.setdefault('cost_control', {})['max_daily_cost'] = args.max_cost
                modified = True
            
            # 如果有修改，创建临时配置文件
            if modified:
                temp_config = tempfile.NamedTemporaryFile(
                    mode='w', suffix='.yaml', delete=False, encoding='utf-8'
                )
                yaml.dump(config, temp_config, default_flow_style=False, allow_unicode=True)
                temp_config.flush()
                temp_config.close()
                
                self.temp_config_path = temp_config.name
                logger.info(f"📝 已创建修改后的配置文件: {temp_config.name}")
                return temp_config.name
            
            return config_path
            
        except Exception as e:
            logger.error(f"配置修改失败: {e}")
            return config_path
    
    def print_pipeline_summary(self, config_path: str):
        """打印Pipeline配置摘要"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            print("\n" + "="*70)
            print("🤖 多模型互评Pipeline配置摘要")
            print("="*70)
            
            # 基础信息
            pipeline_name = config.get('distilabel', {}).get('pipeline_name', 'Unknown')
            print(f"📋 Pipeline名称: {pipeline_name}")
            
            # 模型配置
            primary_model = config.get('distilabel', {}).get('primary_model', {})
            evaluator_model = config.get('g_eval', {}).get('evaluator_model', {})
            
            print(f"🔧 生成模型 (Qwen): {primary_model.get('name', 'Unknown')}")
            print(f"🔧 评估模型 (Doubao): {evaluator_model.get('name', 'Unknown')}")
            
            # G-Eval配置
            criteria = config.get('g_eval', {}).get('criteria', [])
            print(f"📊 G-Eval评估维度: {len(criteria)}个")
            for i, criterion in enumerate(criteria, 1):
                name = criterion.get('name', 'Unknown')
                weight = criterion.get('weight', 0)
                print(f"  {i}. {name} (权重: {weight})")
            
            # 生成配置
            enhanced_mode = config.get('enhanced_mode', {})
            samples_per_intent = enhanced_mode.get('seed_samples_per_intent', 'Unknown')
            batch_size = config.get('distilabel', {}).get('batch_size', 'Unknown')
            
            print(f"📈 每种意图样本数: {samples_per_intent}")
            print(f"📦 批处理大小: {batch_size}")
            
            # 质量阈值
            quality_thresholds = config.get('g_eval', {}).get('quality_thresholds', {})
            print(f"🎯 质量阈值:")
            for threshold_name, threshold_value in quality_thresholds.items():
                print(f"  • {threshold_name}: {threshold_value}")
            
            # 输出配置
            output_config = config.get('output', {})
            output_dir = output_config.get('output_dir', 'Unknown')
            save_format = output_config.get('save_format', [])
            
            print(f"📁 输出目录: {output_dir}")
            print(f"💾 保存格式: {', '.join(save_format)}")
            
            print("="*70)
            
        except Exception as e:
            logger.warning(f"无法读取配置摘要: {e}")
    
    def analyze_data_quality(self, data_path: str):
        """分析数据质量"""
        try:
            logger.info(f"📈 分析数据质量: {data_path}")
            
            # 检查路径类型
            data_path = Path(data_path)
            if not data_path.exists():
                logger.error(f"数据路径不存在: {data_path}")
                return
            
            # 加载数据
            if data_path.is_file() and data_path.suffix == '.json':
                # 单个JSON文件
                with open(data_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if isinstance(data, list):
                        samples = data
                    else:
                        samples = [data]
            elif data_path.is_dir():
                # 目录，查找数据文件
                json_files = list(data_path.glob("*.json"))
                if not json_files:
                    logger.error(f"目录中未找到JSON文件: {data_path}")
                    return
                
                samples = []
                for json_file in json_files:
                    if "quality_report" in json_file.name or "execution_summary" in json_file.name:
                        continue
                    with open(json_file, 'r', encoding='utf-8') as f:
                        file_data = json.load(f)
                        if isinstance(file_data, list):
                            samples.extend(file_data)
                        else:
                            samples.append(file_data)
            else:
                logger.error(f"不支持的数据格式: {data_path}")
                return
            
            if not samples:
                logger.error("未找到有效的数据样本")
                return
            
            # 分析统计
            total_samples = len(samples)
            print(f"\n📊 数据质量分析报告")
            print("="*50)
            print(f"📁 数据路径: {data_path}")
            print(f"📋 总样本数: {total_samples}")
            
            # G-Eval分数统计
            g_eval_scores = []
            consistency_scores = []
            quality_ranks = {}
            
            for sample in samples:
                # G-Eval分数
                overall_score = sample.get('overall_score', 0)
                if isinstance(overall_score, (int, float)):
                    g_eval_scores.append(overall_score)
                
                # 一致性分数
                consistency_score = sample.get('consistency_score', 0)
                if isinstance(consistency_score, (int, float)):
                    consistency_scores.append(consistency_score)
                
                # 质量等级
                quality_rank = sample.get('quality_rank', 'unknown')
                quality_ranks[quality_rank] = quality_ranks.get(quality_rank, 0) + 1
            
            # G-Eval分数分析
            if g_eval_scores:
                avg_g_eval = sum(g_eval_scores) / len(g_eval_scores)
                min_g_eval = min(g_eval_scores)
                max_g_eval = max(g_eval_scores)
                
                print(f"\n🎯 G-Eval分数分析:")
                print(f"  平均分数: {avg_g_eval:.2f}")
                print(f"  最低分数: {min_g_eval:.2f}")
                print(f"  最高分数: {max_g_eval:.2f}")
                
                # 分数分布
                score_ranges = {
                    "优秀 (4.0-5.0)": len([s for s in g_eval_scores if s >= 4.0]),
                    "良好 (3.0-4.0)": len([s for s in g_eval_scores if 3.0 <= s < 4.0]),
                    "一般 (2.0-3.0)": len([s for s in g_eval_scores if 2.0 <= s < 3.0]),
                    "较差 (1.0-2.0)": len([s for s in g_eval_scores if s < 2.0])
                }
                
                print(f"  分数分布:")
                for range_name, count in score_ranges.items():
                    percentage = (count / len(g_eval_scores)) * 100
                    print(f"    {range_name}: {count} ({percentage:.1f}%)")
            
            # 一致性分数分析
            if consistency_scores:
                avg_consistency = sum(consistency_scores) / len(consistency_scores)
                print(f"\n🔄 一致性分析:")
                print(f"  平均一致性: {avg_consistency:.2f}")
                
                high_consistency = len([s for s in consistency_scores if s >= 0.8])
                print(f"  高一致性样本 (≥0.8): {high_consistency} ({high_consistency/len(consistency_scores)*100:.1f}%)")
            
            # 质量等级分布
            if quality_ranks:
                print(f"\n🏆 质量等级分布:")
                for rank, count in sorted(quality_ranks.items()):
                    percentage = (count / total_samples) * 100
                    print(f"  {rank}: {count} ({percentage:.1f}%)")
            
            # 推荐建议
            print(f"\n💡 改进建议:")
            
            if g_eval_scores:
                if avg_g_eval < 3.5:
                    print("  • G-Eval平均分偏低，建议调整生成策略或提高质量阈值")
                if score_ranges.get("较差 (1.0-2.0)", 0) > total_samples * 0.1:
                    print("  • 低质量样本比例较高，建议加强质量过滤")
            
            if consistency_scores and avg_consistency < 0.7:
                print("  • 一致性分数偏低，建议检查模型间的理解差异")
            
            excellent_ratio = quality_ranks.get('excellent', 0) / total_samples
            if excellent_ratio < 0.3:
                print("  • 优秀质量样本比例偏低，建议优化生成参数")
                
            print("="*50)
                
        except Exception as e:
            logger.error(f"数据质量分析失败: {e}")
    
    def generate_default_config(self, output_path: str):
        """生成默认配置文件"""
        try:
            default_config = {
                "pipeline": {
                    "mode": "distilabel_multi_model",
                    "enable_logging": True,
                    "save_intermediate": True,
                    "use_cache": False
                },
                "distilabel": {
                    "pipeline_name": "MultiModelIntentGeneration",
                    "batch_size": 16,
                    "num_workers": 2,
                    "primary_model": {
                        "name": "Qwen/Qwen2.5-7B-Instruct",
                        "type": "transformers",
                        "generation_kwargs": {
                            "max_new_tokens": 512,
                            "temperature": 0.7,
                            "do_sample": True,
                            "top_p": 0.9
                        },
                        "device_map": "auto",
                        "torch_dtype": "float16",
                        "trust_remote_code": True
                    }
                },
                "g_eval": {
                    "evaluator_model": {
                        "name": "doubao",
                        "type": "inference_endpoints",
                        "api_key": "${DOUBAO_API_KEY}",
                        "generation_kwargs": {
                            "max_new_tokens": 256,
                            "temperature": 0.3,
                            "top_p": 0.8
                        }
                    },
                    "criteria": [
                        {
                            "name": "semantic_consistency",
                            "description": "判断自然语言指令与JSON意图的语义是否完全一致，没有信息丢失或添加。评分1-5分，5分表示完全一致。",
                            "weight": 0.4
                        },
                        {
                            "name": "expression_naturalness",
                            "description": "评估指令的语言表达是否自然、符合用户日常说话习惯，避免过于正式或生硬。评分1-5分，5分表示非常自然。",
                            "weight": 0.3
                        },
                        {
                            "name": "ambiguity_level",
                            "description": "评估指令中是否存在歧义、模糊不清或可能被误解的表达。评分1-5分，5分表示完全无歧义。",
                            "weight": 0.2
                        },
                        {
                            "name": "overall_quality",
                            "description": "综合评估指令的整体质量，包括完整性、准确性和可用性。评分1-5分，5分表示质量优秀。",
                            "weight": 0.1
                        }
                    ],
                    "quality_thresholds": {
                        "min_overall_score": 3.5,
                        "min_semantic_consistency": 4.0,
                        "min_expression_naturalness": 3.0,
                        "max_ambiguity_tolerance": 4.0
                    }
                },
                "enhanced_mode": {
                    "seed_samples_per_intent": 50,
                    "evolution_generations": 2,
                    "variants_per_generation": 2,
                    "diversity_threshold": 0.8,
                    "enable_llm_enhancement": True
                },
                "quality_control": {
                    "enable_consistency_check": True,
                    "enable_g_eval_check": True,
                    "enable_semantic_validation": True,
                    "enable_structure_validation": True,
                    "scoring_strategy": {
                        "consistency_weight": 0.4,
                        "g_eval_weight": 0.5,
                        "structure_weight": 0.1
                    }
                },
                "output": {
                    "save_format": ["json", "parquet"],
                    "output_dir": "./data/processed/multi_model_distilabel",
                    "filename_prefix": "multi_model_intent_dataset",
                    "save_metadata": True,
                    "save_quality_report": True,
                    "save_g_eval_details": True
                },
                "error_handling": {
                    "max_retries": 3,
                    "retry_delay": 2.0,
                    "skip_failed_samples": True,
                    "log_failed_samples": True
                },
                "cost_control": {
                    "max_api_calls_per_hour": 1000,
                    "max_daily_cost": 50.0,
                    "enable_cost_tracking": True
                }
            }
            
            # 创建输出目录
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            # 保存配置文件
            with open(output_file, 'w', encoding='utf-8') as f:
                yaml.dump(default_config, f, default_flow_style=False, allow_unicode=True, indent=2)
            
            logger.info(f"✅ 默认配置文件已生成: {output_path}")
            logger.info("📝 请根据实际情况修改配置，特别是API密钥部分")
            
        except Exception as e:
            logger.error(f"生成默认配置失败: {e}")
    
    def cleanup_temp_files(self):
        """清理临时文件"""
        if self.temp_config_path and os.path.exists(self.temp_config_path):
            try:
                os.unlink(self.temp_config_path)
                logger.info(f"🧹 清理临时配置文件: {self.temp_config_path}")
            except Exception as e:
                logger.warning(f"清理临时文件失败: {e}")
    
    async def run_pipeline_with_monitoring(self, config_path: str, output_path: str, args: argparse.Namespace):
        """运行Pipeline并进行监控"""
        try:
            self.start_time = datetime.now()
            logger.info("🚀 开始执行多模型互评Pipeline...")
            
            # 显示执行流程
            logger.info("📋 Pipeline执行流程:")
            logger.info("  1️⃣ 配置驱动意图生成")
            logger.info("  2️⃣ 通义千问反向生成指令")  
            logger.info("  3️⃣ 豆包G-Eval质量评估")
            logger.info("  4️⃣ 通义千问正向验证")
            logger.info("  5️⃣ 综合质量控制")
            logger.info("  6️⃣ 高质量数据筛选")
            
            # 执行Pipeline
            distiset = await run_multi_model_pipeline(config_path, output_path)
            
            # 计算执行时间
            end_time = datetime.now()
            duration = (end_time - self.start_time).total_seconds()
            
            logger.info("🎉 多模型Pipeline执行完成！")
            logger.info(f"⏱️  总执行时间: {duration:.2f} 秒 ({duration/60:.1f} 分钟)")
            
            # 分析结果
            await self.analyze_pipeline_results(distiset, output_path, duration, args)
            
            # 保存执行摘要
            self.save_execution_summary(output_path, duration, args, distiset)
            
            return distiset
            
        except Exception as e:
            logger.error(f"❌ Pipeline执行失败: {e}")
            if args.verbose:
                traceback.print_exc()
            raise
    
    async def analyze_pipeline_results(self, distiset: Any, output_path: str, duration: float, args: argparse.Namespace):
        """分析Pipeline执行结果"""
        try:
            if not distiset or 'default' not in distiset:
                logger.warning("⚠️ 未找到生成的数据集")
                return
            
            dataset = distiset['default']
            total_samples = len(dataset)
            
            logger.info(f"📊 生成结果统计:")
            logger.info(f"  最终样本数: {total_samples}")
            
            if total_samples == 0:
                logger.warning("⚠️ 未生成任何有效样本")
                return
            
            # 统计质量分布
            quality_stats = self.calculate_quality_statistics(dataset)
            
            # 显示统计结果
            self.display_quality_statistics(quality_stats, total_samples)
            
            # 显示性能指标
            self.display_performance_metrics(total_samples, duration)
            
            # 显示示例数据
            if args.verbose:
                self.display_sample_data(dataset, quality_stats)
                
        except Exception as e:
            logger.error(f"结果分析失败: {e}")
    
    def calculate_quality_statistics(self, dataset: List[Dict]) -> Dict[str, Any]:
        """计算质量统计信息"""
        stats = {
            "quality_ranks": {},
            "g_eval_scores": [],
            "consistency_scores": [],
            "validation_passed": 0,
            "intent_distribution": {},
            "g_eval_criteria_scores": {}
        }
        
        for sample in dataset:
            # 质量等级统计
            rank = sample.get('quality_rank', 'unknown')
            stats["quality_ranks"][rank] = stats["quality_ranks"].get(rank, 0) + 1
            
            # G-Eval分数统计
            overall_score = sample.get('overall_score', 0)
            if isinstance(overall_score, (int, float)):
                stats["g_eval_scores"].append(overall_score)
            
            # 一致性分数统计
            consistency_score = sample.get('consistency_score', 0)
            if isinstance(consistency_score, (int, float)):
                stats["consistency_scores"].append(consistency_score)
            
            # 验证通过统计
            if sample.get('validation_passed', False):
                stats["validation_passed"] += 1
            
            # 意图分布统计
            try:
                intent_json = json.loads(sample.get('intent_json', '{}'))
                intent_type = intent_json.get('intentType', 'Unknown')
                stats["intent_distribution"][intent_type] = stats["intent_distribution"].get(intent_type, 0) + 1
            except:
                pass
            
            # G-Eval各维度分数统计
            g_eval_scores = sample.get('g_eval_scores', {})
            for criterion, score in g_eval_scores.items():
                if criterion not in stats["g_eval_criteria_scores"]:
                    stats["g_eval_criteria_scores"][criterion] = []
                if isinstance(score, (int, float)):
                    stats["g_eval_criteria_scores"][criterion].append(score)
        
        return stats
    
    def display_quality_statistics(self, stats: Dict[str, Any], total_samples: int):
        """显示质量统计信息"""
        logger.info("📈 质量分布统计:")
        
        # 质量等级分布
        for rank, count in stats["quality_ranks"].items():
            percentage = (count / total_samples) * 100
            logger.info(f"  {rank}: {count} ({percentage:.1f}%)")
        
        # G-Eval分数统计
        if stats["g_eval_scores"]:
            avg_g_eval = sum(stats["g_eval_scores"]) / len(stats["g_eval_scores"])
            min_g_eval = min(stats["g_eval_scores"])
            max_g_eval = max(stats["g_eval_scores"])
            logger.info(f"📊 G-Eval分数: 平均{avg_g_eval:.2f} (范围: {min_g_eval:.2f}-{max_g_eval:.2f})")
        
        # 一致性分数统计
        if stats["consistency_scores"]:
            avg_consistency = sum(stats["consistency_scores"]) / len(stats["consistency_scores"])
            logger.info(f"🔄 平均一致性分数: {avg_consistency:.2f}")
        
        # 验证通过率
        validation_rate = (stats["validation_passed"] / total_samples) * 100
        logger.info(f"✅ 验证通过率: {stats['validation_passed']}/{total_samples} ({validation_rate:.1f}%)")
        
        # 意图分布
        if stats["intent_distribution"]:
            logger.info("📋 意图类型分布:")
            for intent_type, count in stats["intent_distribution"].items():
                percentage = (count / total_samples) * 100
                logger.info(f"  {intent_type}: {count} ({percentage:.1f}%)")
        
        # G-Eval各维度分数
        if stats["g_eval_criteria_scores"]:
            logger.info("🎯 G-Eval各维度平均分:")
            for criterion, scores in stats["g_eval_criteria_scores"].items():
                if scores:
                    avg_score = sum(scores) / len(scores)
                    logger.info(f"  {criterion}: {avg_score:.2f}")
    
    def display_performance_metrics(self, total_samples: int, duration: float):
        """显示性能指标"""
        logger.info("⚡ 性能指标:")
        
        # 生成速度
        samples_per_minute = (total_samples / duration) * 60 if duration > 0 else 0
        logger.info(f"  生成速度: {samples_per_minute:.1f} 样本/分钟")
        
        # 平均每样本时间
        time_per_sample = duration / total_samples if total_samples > 0 else 0
        logger.info(f"  平均时间: {time_per_sample:.2f} 秒/样本")
        
        # 估算成本效益
        estimated_requests = total_samples * 3  # 生成+评估+验证
        logger.info(f"  估算API调用: {estimated_requests} 次")
    
    def display_sample_data(self, dataset: List[Dict], stats: Dict[str, Any]):
        """显示示例数据"""
        logger.info("📝 高质量样本示例:")
        
        # 寻找优秀质量的样本
        excellent_samples = [s for s in dataset if s.get('quality_rank') == 'excellent']
        if not excellent_samples:
            excellent_samples = [s for s in dataset if s.get('overall_score', 0) >= 4.0]
        
        if excellent_samples:
            sample = excellent_samples[0]
            logger.info(f"  指令: {sample.get('instruction', 'N/A')}")
            logger.info(f"  G-Eval分数: {sample.get('overall_score', 'N/A')}")
            logger.info(f"  一致性分数: {sample.get('consistency_score', 'N/A')}")
            
            # 显示G-Eval详细分数
            g_eval_scores = sample.get('g_eval_scores', {})
            if g_eval_scores:
                logger.info("  G-Eval详细分数:")
                for criterion, score in g_eval_scores.items():
                    logger.info(f"    {criterion}: {score}")
    
    def save_execution_summary(self, output_path: str, duration: float, args: argparse.Namespace, distiset: Any):
        """保存执行摘要"""
        try:
            total_samples = len(distiset['default']) if distiset and 'default' in distiset else 0
            
            summary = {
                "execution_info": {
                    "pipeline_type": "multi_model_evaluation",
                    "execution_time_seconds": duration,
                    "execution_time_formatted": f"{duration:.2f}s ({duration/60:.1f}m)",
                    "start_time": self.start_time.isoformat() if self.start_time else None,
                    "end_time": datetime.now().isoformat(),
                    "total_samples_generated": total_samples
                },
                "configuration": {
                    "config_file": args.config,
                    "output_path": output_path,
                    "test_mode": args.test_mode,
                    "samples_requested": args.samples,
                    "batch_size": args.batch_size,
                    "min_quality_threshold": args.min_quality,
                    "max_cost_limit": args.max_cost
                },
                "models_used": {
                    "generator": "Qwen (通义千问)",
                    "evaluator": "Doubao (豆包)",
                    "validator": "Qwen (通义千问)"
                },
                "performance_metrics": {
                    "samples_per_minute": (total_samples / duration) * 60 if duration > 0 else 0,
                    "seconds_per_sample": duration / total_samples if total_samples > 0 else 0,
                    "estimated_api_calls": total_samples * 3
                },
                "command_args": vars(args)
            }
            
            # 保存摘要
            summary_path = Path(output_path) / "execution_summary.json"
            summary_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(summary_path, 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2)
            
            logger.info(f"📋 执行摘要已保存: {summary_path}")
            
        except Exception as e:
            logger.warning(f"保存执行摘要失败: {e}")
    
    def print_final_recommendations(self, output_path: str):
        """打印最终建议"""
        print("\n" + "="*70)
        print("🎯 后续处理建议:")
        print("="*70)
        
        print("1. 📊 质量分析:")
        print(f"   python scripts/data_generation/multi_model_runner.py --analyze-quality {output_path}")
        
        print("\n2. 🔍 详细评估:")
        print("   # 查看G-Eval评估详情和豆包模型的评分分布")
        print("   # 分析通义千问和豆包的评估一致性")
        
        print("\n3. 📈 效果对比:")
        print("   # 与单模型生成的数据进行质量对比")
        print("   # 评估多模型互评的成本效益比")
        
        print("\n4. 🚀 模型训练:")
        print("   # 使用高质量数据进行LoRA微调")
        print("   # 验证微调模型的性能提升")
        
        print("\n5. 📝 质量优化:")
        print("   # 根据评估结果调整质量阈值")
        print("   # 优化G-Eval评估标准")
        
        print("\n6. 💰 成本优化:")
        print("   # 分析API调用成本分布")
        print("   # 优化批处理和缓存策略")
        
        print("="*70)
    
    async def main(self):
        """主函数"""
        parser = self.create_arg_parser()
        args = parser.parse_args()
        
        try:
            logger.info("🚀 启动多模型互评数据生成系统...")
            
            # 设置详细日志
            if args.verbose:
                logger.info("🔊 详细输出模式已启用")
            
            # 生成默认配置
            if args.generate_config:
                self.generate_default_config(args.generate_config)
                return
            
            # 分析数据质量
            if args.analyze_quality:
                self.analyze_data_quality(args.analyze_quality)
                return
            
            # 验证配置文件
            if not os.path.exists(args.config):
                logger.error(f"❌ 配置文件不存在: {args.config}")
                sys.exit(1)
            
            is_valid, errors = self.validate_multi_model_config(args.config)
            if not is_valid:
                logger.error("❌ 配置文件验证失败:")
                for error in errors:
                    logger.error(f"  • {error}")
                sys.exit(1)
            
            logger.info("✅ 配置文件验证通过")
            
            # 仅验证配置
            if args.validate_config:
                logger.info("✅ 配置文件验证完成")
                return
            
            # 检查模型连接
            if args.check_models:
                connectivity = await self.check_model_connectivity(args.config)
                
                print("\n🔍 模型连接状态检查:")
                print("-" * 40)
                
                for model_name, result in connectivity.items():
                    if model_name == "overall_status":
                        continue
                    
                    status = result["status"]
                    error = result.get("error")
                    response_time = result.get("response_time")
                    
                    status_icon = "✅" if status else "❌"
                    status_text = "正常" if status else "失败"
                    
                    print(f"{status_icon} {model_name}: {status_text}")
                    
                    if response_time:
                        print(f"   响应时间: {response_time:.2f}s")
                    
                    if error:
                        print(f"   错误信息: {error}")
                
                overall_status = connectivity.get("overall_status", False)
                if not overall_status:
                    logger.warning("⚠️ 部分模型连接失败，请检查配置和网络")
                    sys.exit(1)
                else:
                    logger.info("🎉 所有模型连接正常！")
                
                return
            
            # 成本估算
            if args.cost_estimate:
                samples = args.samples or self.config.get('enhanced_mode', {}).get('seed_samples_per_intent', 100)
                costs = self.estimate_api_costs(args.config, samples, args.cost_per_1k)
                
                print("\n💰 API成本估算:")
                print("-" * 40)
                print(f"📊 每种意图样本数: {costs.get('samples_per_intent', 0)}")
                print(f"📈 总意图样本数: {costs.get('total_intent_samples', 0)}")
                print(f"🔄 Qwen调用次数: {costs.get('qwen_requests', 0)}")
                print(f"🤖 Doubao调用次数: {costs.get('doubao_requests', 0)}")
                print(f"📋 总调用次数: {costs.get('total_requests', 0)}")
                print(f"🎯 预计总token数: {costs.get('total_tokens', 0):,}")
                print(f"💵 估算总成本: ${costs.get('total_cost', 0):.4f}")
                print(f"⏱️  预计执行时间: {costs.get('estimated_time_minutes', 0):.1f} 分钟")
                
                if args.max_cost and costs.get('total_cost', 0) > args.max_cost:
                    logger.warning(f"⚠️ 估算成本 ${costs.get('total_cost', 0):.4f} 超过预算限制 ${args.max_cost}")
                    
                print("\n注意: 这只是粗略估算，实际成本可能因模型响应长度而有差异")
                return
            
            # 根据参数修改配置
            config_path = self.modify_config_for_options(args.config, args)
            
            # 打印配置摘要
            self.print_pipeline_summary(config_path)
            
            # Dry run模式
            if args.dry_run:
                logger.info("🔍 Dry-run模式，验证Pipeline创建...")
                try:
                    pipeline = create_multi_model_pipeline(config_path)
                    logger.info("✅ Pipeline创建成功")
                    logger.info(f"📋 Pipeline包含 {len(pipeline.dag.nodes)} 个步骤")
                    
                    step_names = list(pipeline.dag.nodes)
                    for i, step_name in enumerate(step_names, 1):
                        logger.info(f"  {i}. {step_name}")
                    
                    logger.info("🔄 数据流向:")
                    logger.info("  意图生成 → 指令转换(Qwen) → G-Eval评估(Doubao) → 多模型验证 → 质量控制 → 筛选")
                    
                    return
                except Exception as e:
                    logger.error(f"❌ Pipeline创建失败: {e}")
                    if args.verbose:
                        traceback.print_exc()
                    sys.exit(1)
            
            # 确定输出路径
            output_path = args.output
            if not output_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                mode_suffix = "_test" if args.test_mode else ""
                output_path = f"data/processed/multi_model_output_{timestamp}{mode_suffix}"
            
            logger.info(f"📁 输出路径: {output_path}")
            
            # 执行Pipeline
            distiset = await self.run_pipeline_with_monitoring(config_path, output_path, args)
            
            # 打印最终建议
            self.print_final_recommendations(output_path)
            
            logger.info("🎉 多模型互评系统执行完成！")
            
        except KeyboardInterrupt:
            logger.warning("❌ 用户中断执行")
            sys.exit(1)
        except Exception as e:
            logger.error(f"❌ 系统执行失败: {e}")
            if args.verbose:
                traceback.print_exc()
            sys.exit(1)
        finally:
            # 清理临时文件
            self.cleanup_temp_files()


# 主程序入口
if __name__ == "__main__":
    runner = MultiModelRunner()
    
    try:
        asyncio.run(runner.main())
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)