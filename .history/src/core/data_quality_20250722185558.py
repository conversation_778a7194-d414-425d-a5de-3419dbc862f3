"""
数据质量控制器：去重、质量评估、数据验证
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Set, Tuple
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.feature_extraction.text import TfidfVectorizer
import json
import hashlib
from loguru import logger

from src.models.intent_models import TrainingSample, DataQualityMetrics

class DataQualityController:
    """数据质量控制器"""
    
    def __init__(self):
        self.sentence_model = None
        self.tfidf_vectorizer = None
        self.similarity_threshold = 0.85
        self.min_instruction_length = 3
        self.max_instruction_length = 200
    
    async def initialize(self):
        """初始化模型"""
        logger.info("正在初始化数据质量控制器...")
        
        try:
            # 加载句子嵌入模型
            self.sentence_model = SentenceTransformer('all-MiniLM-L6-v2')
            logger.info("句子嵌入模型加载完成")
            
            # 初始化TF-IDF向量化器
            self.tfidf_vectorizer = TfidfVectorizer(
                ngram_range=(1, 2),
                max_features=5000,
                stop_words=None
            )
            logger.info("TF-IDF向量化器初始化完成")
            
        except Exception as e:
            logger.error(f"数据质量控制器初始化失败: {e}")
            raise
    
    def deduplicate_samples(self, samples: List[TrainingSample]) -> List[TrainingSample]:
        """多层次去重处理"""
        logger.info(f"开始去重处理，原始样本数: {len(samples)}")
        
        # 1. 精确匹配去重
        unique_samples = self._exact_deduplication(samples)
        logger.info(f"精确匹配去重后样本数: {len(unique_samples)}")
        
        # 2. TF-IDF相似度去重
        unique_samples = self._tfidf_deduplication(unique_samples)
        logger.info(f"TF-IDF去重后样本数: {len(unique_samples)}")
        
        # 3. 语义相似度去重
        if self.sentence_model is not None:
            unique_samples = self._semantic_deduplication(unique_samples)
            logger.info(f"语义去重后样本数: {len(unique_samples)}")
        
        # 4. 意图结构去重
        unique_samples = self._intent_structure_deduplication(unique_samples)
        logger.info(f"意图结构去重后样本数: {len(unique_samples)}")
        
        logger.info(f"最终去重后样本数: {len(unique_samples)}")
        return unique_samples
    
    def _exact_deduplication(self, samples: List[TrainingSample]) -> List[TrainingSample]:
        """精确匹配去重"""
        seen_instructions = set()
        unique_samples = []
        
        for sample in samples:
            instruction_hash = hashlib.md5(sample.instruction.encode()).hexdigest()
            if instruction_hash not in seen_instructions:
                seen_instructions.add(instruction_hash)
                unique_samples.append(sample)
        
        return unique_samples
    
    def _tfidf_deduplication(self, samples: List[TrainingSample]) -> List[TrainingSample]:
        """基于TF-IDF的去重"""
        if len(samples) <= 1:
            return samples
        
        instructions = [sample.instruction for sample in samples]
        
        try:
            # 计算TF-IDF向量
            tfidf_matrix = self.tfidf_vectorizer.fit_transform(instructions)
            
            # 计算相似度矩阵
            similarity_matrix = cosine_similarity(tfidf_matrix)
            
            # 贪心去重
            to_keep = []
            kept_indices = set()
            
            for i, sample in enumerate(samples):
                if i in kept_indices:
                    continue
                
                should_keep = True
                for kept_idx in to_keep:
                    if similarity_matrix[i][kept_idx] > self.similarity_threshold:
                        should_keep = False
                        break
                
                if should_keep:
                    to_keep.append(i)
                    kept_indices.add(i)
            
            return [samples[i] for i in to_keep]
            
        except Exception as e:
            logger.warning(f"TF-IDF去重失败: {e}")
            return samples
    
    def _semantic_deduplication(self, samples: List[TrainingSample]) -> List[TrainingSample]:
        """基于语义相似度的去重"""
        if len(samples) <= 1:
            return samples
        
        try:
            instructions = [sample.instruction for sample in samples]
            embeddings = self.sentence_model.encode(instructions, batch_size=32)
            
            # 计算相似度矩阵
            similarity_matrix = cosine_similarity(embeddings)
            
            # 贪心去重
            to_keep = []
            kept_indices = set()
            
            for i, sample in enumerate(samples):
                if i in kept_indices:
                    continue
                
                should_keep = True
                for kept_idx in to_keep:
                    if similarity_matrix[i][kept_idx] > self.similarity_threshold:
                        should_keep = False
                        break
                
                if should_keep:
                    to_keep.append(i)
                    kept_indices.add(i)
            
            return [samples[i] for i in to_keep]
            
        except Exception as e:
            logger.warning(f"语义去重失败: {e}")
            return samples
    
    def _intent_structure_deduplication(self, samples: List[TrainingSample]) -> List[TrainingSample]:
        """基于意图结构的去重"""
        seen_structures = set()
        unique_samples = []
        
        for sample in samples:
            # 将意图结构序列化为字符串进行比较
            structure_key = self._serialize_intent_structure(sample.output)
            
            if structure_key not in seen_structures:
                seen_structures.add(structure_key)
                unique_samples.append(sample)
        
        return unique_samples
    
    def _serialize_intent_structure(self, intents: List) -> str:
        """序列化意图结构用于比较"""
        try:
            # 提取关键信息用于比较
            structure_data = []
            for intent in intents:
                if hasattr(intent, 'dict'):
                    intent_dict = intent.dict()
                else:
                    intent_dict = intent
                
                key_info = {
                    'intentType': intent_dict.get('intentType'),
                    'targetConceptName': intent_dict.get('targetConceptName'),
                    'props_keys': sorted(intent_dict.get('props', {}).keys())
                }
                structure_data.append(key_info)
            
            return json.dumps(structure_data, sort_keys=True)
        except Exception as e:
            logger.warning(f"序列化意图结构失败: {e}")
            return str(hash(str(intents)))
    
    def validate_samples(self, samples: List[TrainingSample]) -> Tuple[List[TrainingSample], List[str]]:
        """验证样本质量"""
        valid_samples = []
        validation_errors = []
        
        logger.info(f"开始验证样本质量，样本数: {len(samples)}")
        
        for i, sample in enumerate(samples):
            errors = []
            
            # 1. 指令长度检查
            if len(sample.instruction) < self.min_instruction_length:
                errors.append(f"指令过短: '{sample.instruction}'")
            elif len(sample.instruction) > self.max_instruction_length:
                errors.append(f"指令过长: {len(sample.instruction)} 字符")
            
            # 2. 指令内容检查
            if not sample.instruction.strip():
                errors.append("指令为空")
            elif sample.instruction.isdigit():
                errors.append("指令只包含数字")
            
            # 3. 意图结构检查
            if not sample.output and sample.metadata.get("intent_type") not in ["CLARIFICATION_NEEDED", "IRRELEVANT"]:
                errors.append("缺少有效意图")
            
            # 4. 字符检查
            if any(char in sample.instruction for char in ['<', '>', '{', '}', '[', ']']):
                errors.append("包含特殊字符")
            
            if errors:
                validation_errors.extend([f"样本 {i}: {error}" for error in errors])
            else:
                valid_samples.append(sample)
        
        logger.info(f"验证完成，有效样本数: {len(valid_samples)}, 错误数: {len(validation_errors)}")
        return valid_samples, validation_errors
    
    def assess_quality(self, samples: List[TrainingSample]) -> DataQualityMetrics:
        """评估数据质量"""
        logger.info(f"开始评估数据质量，样本数: {len(samples)}")
        
        if not samples:
            return DataQualityMetrics(
                total_samples=0,
                avg_instruction_length=0.0,
                intent_distribution_entropy=0.0,
                expression_diversity=0.0,
                duplicate_rate=0.0
            )
        
        # 1. 基础统计
        total_samples = len(samples)
        instruction_lengths = [len(s.instruction) for s in samples]
        avg_instruction_length = np.mean(instruction_lengths)
        
        # 2. 意图类型分布
        intent_distribution = self._calculate_intent_distribution(samples)
        intent_entropy = self._calculate_entropy(list(intent_distribution.values()))
        
        # 3. 表达多样性
        expression_diversity = 0.0
        if self.sentence_model is not None:
            expression_diversity = self._calculate_expression_diversity(samples)
        
        # 4. 重复率估算
        duplicate_rate = self._estimate_duplicate_rate(samples)
        
        metrics = DataQualityMetrics(
            total_samples=total_samples,
            avg_instruction_length=avg_instruction_length,
            intent_distribution_entropy=intent_entropy,
            expression_diversity=expression_diversity,
            duplicate_rate=duplicate_rate
        )
        
        logger.info(f"数据质量评估完成: {metrics}")
        return metrics
    
    def _calculate_intent_distribution(self, samples: List[TrainingSample]) -> Dict[str, int]:
        """计算意图类型分布"""
        distribution = {}
        
        for sample in samples:
            intent_type = sample.metadata.get("intent_type", "UNKNOWN")
            distribution[intent_type] = distribution.get(intent_type, 0) + 1
        
        return distribution
    
    def _calculate_entropy(self, values: List[int]) -> float:
        """计算分布熵"""
        if not values or sum(values) == 0:
            return 0.0
        
        total = sum(values)
        probs = [v / total for v in values if v > 0]
        entropy = -sum(p * np.log2(p) for p in probs)
        
        return entropy
    
    def _calculate_expression_diversity(self, samples: List[TrainingSample]) -> float:
        """计算表达多样性"""
        try:
            # 采样计算，避免内存问题
            sample_size = min(1000, len(samples))
            sampled = np.random.choice(samples, sample_size, replace=False)
            
            instructions = [s.instruction for s in sampled]
            embeddings = self.sentence_model.encode(instructions, batch_size=32)
            
            # 计算平均相似度
            similarity_matrix = cosine_similarity(embeddings)
            
            # 排除对角线元素
            mask = np.ones(similarity_matrix.shape, dtype=bool)
            np.fill_diagonal(mask, False)
            
            avg_similarity = np.mean(similarity_matrix[mask])
            diversity = 1 - avg_similarity
            
            return max(0.0, min(1.0, diversity))
            
        except Exception as e:
            logger.warning(f"计算表达多样性失败: {e}")
            return 0.0
    
    def _estimate_duplicate_rate(self, samples: List[TrainingSample]) -> float:
        """估算重复率"""
        if len(samples) <= 1:
            return 0.0
        
        # 基于指令的精确匹配
        instructions = [s.instruction for s in samples]
        unique_instructions = set(instructions)
        
        duplicate_rate = 1 - (len(unique_instructions) / len(instructions))
        return duplicate_rate
    
    def generate_quality_report(self, samples: List[TrainingSample], 
                              validation_errors: List[str] = None) -> Dict[str, Any]:
        """生成质量报告"""
        metrics = self.assess_quality(samples)
        
        # 意图分布统计
        intent_distribution = self._calculate_intent_distribution(samples)
        
        # 长度分布统计
        lengths = [len(s.instruction) for s in samples]
        length_stats = {
            "min": int(np.min(lengths)) if lengths else 0,
            "max": int(np.max(lengths)) if lengths else 0,
            "median": int(np.median(lengths)) if lengths else 0,
            "std": float(np.std(lengths)) if lengths else 0.0
        }
        
        report = {
            "quality_metrics": metrics.dict(),
            "intent_distribution": intent_distribution,
            "length_statistics": length_stats,
            "validation_errors": validation_errors or [],
            "recommendations": self._generate_recommendations(metrics, intent_distribution)
        }
        
        return report
    
    def _generate_recommendations(self, metrics: DataQualityMetrics, 
                                distribution: Dict[str, int]) -> List[str]:
        """生成质量改进建议"""
        recommendations = []
        
        # 样本数量建议
        if metrics.total_samples < 1000:
            recommendations.append("建议增加样本数量至1000以上")
        
        # 分布均匀性建议
        if metrics.intent_distribution_entropy < 2.0:
            recommendations.append("意图类型分布不均匀，建议平衡各类型样本数量")
        
        # 表达多样性建议
        if metrics.expression_diversity < 0.3:
            recommendations.append("表达多样性较低，建议增加更多样化的表达方式")
        
        # 重复率建议
        if metrics.duplicate_rate > 0.1:
            recommendations.append("重复率较高，建议进行更严格的去重处理")
        
        # 平均长度建议
        if metrics.avg_instruction_length < 10:
            recommendations.append("平均指令长度较短，建议增加更丰富的表达")
        elif metrics.avg_instruction_length > 100:
            recommendations.append("平均指令长度较长，建议简化表达")
        
        return recommendations