"""
加权采样器 - 支持基于配置的智能采样
"""

import random
import numpy as np
from typing import List, Dict, Any, Tuple, Optional
from loguru import logger
from collections import Counter

class WeightedSampler:
    """基于权重配置的采样器"""
    
    @staticmethod
    def weighted_choice(choices: List[Any], weights: List[float]) -> Any:
        """根据权重选择单个元素"""
        if not choices:
            return None
        
        if not weights or len(weights) != len(choices):
            logger.warning("权重列表长度与选择列表不匹配，使用均匀采样")
            return random.choice(choices)
        
        # 处理权重为0的情况
        total_weight = sum(weights)
        if total_weight <= 0:
            logger.warning("权重总和为0，使用均匀采样")
            return random.choice(choices)
        
        # 归一化权重
        normalized_weights = [w / total_weight for w in weights]
        
        try:
            return np.random.choice(choices, p=normalized_weights)
        except Exception as e:
            logger.warning(f"numpy采样失败: {e}，回退到手动采样")
            return WeightedSampler._manual_weighted_choice(choices, normalized_weights)
    
    @staticmethod
    def _manual_weighted_choice(choices: List[Any], normalized_weights: List[float]) -> Any:
        """手动实现的加权选择（numpy失败时的备选方案）"""
        rand_val = random.random()
        cumulative_weight = 0.0
        
        for choice, weight in zip(choices, normalized_weights):
            cumulative_weight += weight
            if rand_val <= cumulative_weight:
                return choice
        
        # 容错：返回最后一个选择
        return choices[-1]
    
    @staticmethod
    def weighted_choice_dict(weight_dict: Dict[str, float]) -> Optional[str]:
        """从权重字典中选择"""
        if not weight_dict:
            return None
        
        choices = list(weight_dict.keys())
        weights = list(weight_dict.values())
        return WeightedSampler.weighted_choice(choices, weights)
    
    @staticmethod
    def weighted_sample_multiple(choices: List[Any], weights: List[float], 
                                k: int, replacement: bool = True) -> List[Any]:
        """加权采样多个元素"""
        if not choices or k <= 0:
            return []
        
        if k == 1:
            return [WeightedSampler.weighted_choice(choices, weights)]
        
        if not replacement and k > len(choices):
            logger.warning(f"无替换采样数量({k})超过选择总数({len(choices)})，调整为{len(choices)}")
            k = len(choices)
        
        if not weights or len(weights) != len(choices):
            if replacement:
                return random.choices(choices, k=k)
            else:
                return random.sample(choices, k)
        
        samples = []
        remaining_choices = choices.copy()
        remaining_weights = weights.copy()
        
        for _ in range(k):
            if not remaining_choices:
                break
            
            selected = WeightedSampler.weighted_choice(remaining_choices, remaining_weights)
            samples.append(selected)
            
            if not replacement:
                # 移除已选择的元素
                index = remaining_choices.index(selected)
                remaining_choices.pop(index)
                remaining_weights.pop(index)
        
        return samples
    
    @staticmethod
    def sample_by_crud_weights(intent_types: List[str], crud_weights: Dict[str, float], 
                              total_count: int) -> Dict[str, int]:
        """根据CRUD权重分配各意图类型的样本数量"""
        if not intent_types or total_count <= 0:
            return {}
        
        # Intent到CRUD的映射
        intent_crud_mapping = {
            "ADD_COLUMN": "create",
            "DELETE_COLUMN": "delete", 
            "MODIFY_COLUMN": "update",
            "QUERY_COLUMN": "read",
            "ADD_RELATIONSHIP": "create",
            "DELETE_RELATIONSHIP": "delete",
            "MODIFY_RELATIONSHIP": "update",
            "ENABLE_SOFT_DELETE": "create",
            "DISABLE_SOFT_DELETE": "delete"
        }
        
        distribution = {}
        total_weight = 0.0
        
        # 计算总权重
        for intent_type in intent_types:
            crud_type = intent_crud_mapping.get(intent_type, "create")
            weight = crud_weights.get(crud_type, 25.0)
            total_weight += weight
        
        # 分配样本数量
        allocated_count = 0
        for intent_type in intent_types:
            crud_type = intent_crud_mapping.get(intent_type, "create")
            weight = crud_weights.get(crud_type, 25.0)
            
            if total_weight > 0:
                count = int(total_count * weight / total_weight)
            else:
                count = total_count // len(intent_types)
            
            distribution[intent_type] = count
            allocated_count += count
        
        # 处理因舍入导致的总数不匹配
        remaining = total_count - allocated_count
        if remaining > 0:
            # 将剩余数量分配给权重最高的意图类型
            sorted_intents = sorted(intent_types, 
                                  key=lambda x: crud_weights.get(intent_crud_mapping.get(x, "create"), 25.0), 
                                  reverse=True)
            for i in range(remaining):
                intent_type = sorted_intents[i % len(sorted_intents)]
                distribution[intent_type] += 1
        
        logger.info(f"CRUD权重分配结果: {distribution}")
        return distribution
    
    @staticmethod
    def sample_by_semantic_weights(semantic_weights: Dict[str, float], 
                                 total_count: int) -> Dict[str, int]:
        """根据语义策略权重分配样本数量"""
        if not semantic_weights or total_count <= 0:
            return {}
        
        distribution = {}
        total_weight = sum(semantic_weights.values())
        allocated_count = 0
        
        for strategy, weight in semantic_weights.items():
            if total_weight > 0:
                count = int(total_count * weight / total_weight)
            else:
                count = total_count // len(semantic_weights)
            
            distribution[strategy] = count
            allocated_count += count
        
        # 处理舍入误差
        remaining = total_count - allocated_count
        if remaining > 0:
            # 按权重顺序分配剩余数量
            sorted_strategies = sorted(semantic_weights.items(), key=lambda x: x[1], reverse=True)
            for i in range(remaining):
                strategy = sorted_strategies[i % len(sorted_strategies)][0]
                distribution[strategy] += 1
        
        logger.info(f"语义策略权重分配结果: {distribution}")
        return distribution
    
    @staticmethod
    def sample_by_expression_weights(expression_weights: Dict[str, float], 
                                   samples: List[Any]) -> List[Any]:
        """根据表达层权重对样本进行变换"""
        if not expression_weights or not samples:
            return samples
        
        enhanced_samples = []
        
        for sample in samples:
            # 原始样本总是保留
            enhanced_samples.append(sample)
            
            # 根据权重决定是否应用各种变换
            for layer, weight in expression_weights.items():
                if random.random() < weight / 100:  # 将权重转换为概率
                    # 这里需要具体的变换逻辑，当前只是示例
                    # 实际使用时会在data_generator中实现具体变换
                    enhanced_samples.append(sample)
        
        return enhanced_samples

class DistributionSampler:
    """分布式采样器 - 支持复杂的采样策略"""
    
    def __init__(self, random_seed: Optional[int] = None):
        """初始化采样器"""
        if random_seed is not None:
            random.seed(random_seed)
            np.random.seed(random_seed)
        
        self.sampling_history = []
    
    def stratified_sample(self, population: Dict[str, List[Any]], 
                         sample_sizes: Dict[str, int]) -> Dict[str, List[Any]]:
        """分层采样 - 确保每个层次都有代表性样本"""
        samples = {}
        
        for stratum, items in population.items():
            target_size = sample_sizes.get(stratum, 0)
            
            if target_size <= 0 or not items:
                samples[stratum] = []
                continue
            
            if target_size >= len(items):
                # 如果目标数量大于等于总数，返回全部
                samples[stratum] = items.copy()
            else:
                # 随机采样
                samples[stratum] = random.sample(items, target_size)
        
        self.sampling_history.append({
            "method": "stratified",
            "input_sizes": {k: len(v) for k, v in population.items()},
            "sample_sizes": sample_sizes,
            "output_sizes": {k: len(v) for k, v in samples.items()}
        })
        
        return samples
    
    def balanced_sample(self, items: List[Any], categories: List[str], 
                       target_per_category: int) -> Dict[str, List[Any]]:
        """均衡采样 - 确保每个类别的样本数量相等"""
        if not items or not categories:
            return {}
        
        # 将items按category分组
        categorized_items = {category: [] for category in categories}
        
        # 简单的分组策略（实际使用时需要根据具体需求调整）
        items_per_category = len(items) // len(categories)
        
        for i, item in enumerate(items):
            category = categories[i % len(categories)]
            categorized_items[category].append(item)
        
        # 对每个类别进行采样
        balanced_samples = {}
        for category, category_items in categorized_items.items():
            if len(category_items) >= target_per_category:
                balanced_samples[category] = random.sample(category_items, target_per_category)
            else:
                # 如果不够，进行有放回采样
                balanced_samples[category] = random.choices(category_items, k=target_per_category)
        
        return balanced_samples
    
    def hierarchical_sample(self, hierarchy: Dict[str, Dict[str, List[Any]]], 
                           weights: Dict[str, Dict[str, float]]) -> List[Any]:
        """层次化采样 - 支持多层级的加权采样"""
        samples = []
        
        for top_level, sub_items in hierarchy.items():
            top_weight = weights.get(top_level, {})
            
            for sub_level, items in sub_items.items():
                sub_weight = top_weight.get(sub_level, 1.0)
                
                if sub_weight > 0 and items:
                    # 根据权重决定采样数量
                    sample_count = max(1, int(sub_weight))
                    if sample_count <= len(items):
                        level_samples = random.sample(items, sample_count)
                    else:
                        level_samples = random.choices(items, k=sample_count)
                    
                    samples.extend(level_samples)
        
        return samples
    
    def get_sampling_statistics(self) -> Dict[str, Any]:
        """获取采样统计信息"""
        if not self.sampling_history:
            return {"total_samplings": 0}
        
        total_samplings = len(self.sampling_history)
        methods_used = [entry["method"] for entry in self.sampling_history]
        method_counts = Counter(methods_used)
        
        return {
            "total_samplings": total_samplings,
            "methods_used": dict(method_counts),
            "latest_sampling": self.sampling_history[-1] if self.sampling_history else None
        }

class AdaptiveSampler:
    """自适应采样器 - 根据历史结果调整采样策略"""
    
    def __init__(self):
        self.success_rates = {}  # 记录各种采样策略的成功率
        self.sample_quality_history = []
    
    def record_sample_quality(self, strategy: str, quality_score: float):
        """记录采样质量"""
        if strategy not in self.success_rates:
            self.success_rates[strategy] = []
        
        self.success_rates[strategy].append(quality_score)
        self.sample_quality_history.append({
            "strategy": strategy,
            "quality": quality_score,
            "timestamp": len(self.sample_quality_history)
        })
    
    def get_adaptive_weights(self, base_weights: Dict[str, float]) -> Dict[str, float]:
        """根据历史表现调整权重"""
        if not self.success_rates:
            return base_weights
        
        adaptive_weights = base_weights.copy()
        
        for strategy, scores in self.success_rates.items():
            if strategy in adaptive_weights and scores:
                avg_score = sum(scores) / len(scores)
                # 根据平均质量分数调整权重（简单的线性调整）
                adjustment_factor = avg_score  # 假设质量分数在0-1之间
                adaptive_weights[strategy] *= adjustment_factor
        
        return adaptive_weights
    
    def suggest_sampling_strategy(self, available_strategies: List[str]) -> str:
        """建议最优采样策略"""
        if not self.success_rates:
            return random.choice(available_strategies)
        
        strategy_scores = {}
        for strategy in available_strategies:
            scores = self.success_rates.get(strategy, [0.5])  # 默认中等分数
            strategy_scores[strategy] = sum(scores) / len(scores)
        
        # 返回平均质量最高的策略
        best_strategy = max(strategy_scores.items(), key=lambda x: x[1])[0]
        return best_strategy

# 工具函数
def validate_weights(weights: Dict[str, float]) -> bool:
    """验证权重配置的有效性"""
    if not weights:
        return False
    
    # 检查权重是否为非负数
    for key, weight in weights.items():
        if not isinstance(weight, (int, float)) or weight < 0:
            logger.error(f"无效权重: {key} = {weight}")
            return False
    
    # 检查权重总和是否为正数
    total_weight = sum(weights.values())
    if total_weight <= 0:
        logger.error(f"权重总和为非正数: {total_weight}")
        return False
    
    return True

def normalize_weights(weights: Dict[str, float]) -> Dict[str, float]:
    """归一化权重使其总和为1"""
    if not validate_weights(weights):
        logger.warning("权重验证失败，返回均匀权重")
        count = len(weights)
        return {key: 1.0/count for key in weights.keys()} if count > 0 else {}
    
    total = sum(weights.values())
    return {key: value / total for key, value in weights.items()}

def merge_weights(weights1: Dict[str, float], weights2: Dict[str, float], 
                 ratio: float = 0.5) -> Dict[str, float]:
    """合并两个权重配置"""
    all_keys = set(weights1.keys()) | set(weights2.keys())
    merged = {}
    
    for key in all_keys:
        w1 = weights1.get(key, 0.0)
        w2 = weights2.get(key, 0.0)
        merged[key] = w1 * ratio + w2 * (1 - ratio)
    
    return merged