"""
独立的增强采样管道模块
从enhanced_sampling_system.py中提取核心类，作为可复用的采样管道
专注于采样逻辑，便于集成到distilabel系统中
"""

import json
import yaml
import random
import asyncio
import hashlib
import numpy as np
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass, field
from pathlib import Path
from loguru import logger
import time
from collections import defaultdict

# 导入现有组件
from src.core.weighted_sampler import EnhancedSamplingAdapter, WeightedSampler
from src.models.intent_models import TrainingSample, Intent

# ==================== 数据结构定义 ====================

@dataclass
class IntentSample:
    """意图样本数据结构"""
    intent_json: Dict[str, Any]
    instruction: str
    sample_type: str  # base, paraphrase, adversarial, boundary, negative
    confidence: float = 1.0
    metadata: Dict[str, Any] = field(default_factory=dict)
    hash_id: str = field(default="")
    quality_score: float = 1.0
    
    def __post_init__(self):
        """生成样本哈希ID用于去重"""
        if not self.hash_id:
            content = f"{self.intent_json}_{self.instruction}"
            self.hash_id = hashlib.md5(content.encode()).hexdigest()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "intent_json": self.intent_json,
            "instruction": self.instruction,
            "sample_type": self.sample_type,
            "confidence": self.confidence,
            "metadata": self.metadata,
            "hash_id": self.hash_id,
            "quality_score": self.quality_score
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'IntentSample':
        """从字典创建对象"""
        return cls(
            intent_json=data.get("intent_json", {}),
            instruction=data.get("instruction", ""),
            sample_type=data.get("sample_type", "base"),
            confidence=data.get("confidence", 1.0),
            metadata=data.get("metadata", {}),
            hash_id=data.get("hash_id", ""),
            quality_score=data.get("quality_score", 1.0)
        )


@dataclass
class SamplingStatistics:
    """采样统计信息"""
    total_samples: int = 0
    samples_by_type: Dict[str, int] = field(default_factory=dict)
    samples_by_strategy: Dict[str, int] = field(default_factory=dict)
    quality_distribution: Dict[str, float] = field(default_factory=dict)
    generation_time: float = 0.0
    success_rate: float = 1.0
    
    def add_sample(self, sample: IntentSample, generation_time: float = 0.0):
        """添加样本统计"""
        self.total_samples += 1
        self.samples_by_type[sample.sample_type] = self.samples_by_type.get(sample.sample_type, 0) + 1
        
        strategy = sample.metadata.get("semantic_strategy", "unknown")
        self.samples_by_strategy[strategy] = self.samples_by_strategy.get(strategy, 0) + 1
        
        self.generation_time += generation_time
        
        # 更新质量分布
        quality_range = self._get_quality_range(sample.quality_score)
        self.quality_distribution[quality_range] = self.quality_distribution.get(quality_range, 0) + 1
    
    def _get_quality_range(self, score: float) -> str:
        """获取质量分数范围"""
        if score >= 0.8:
            return "high"
        elif score >= 0.6:
            return "medium"
        elif score >= 0.4:
            return "low"
        else:
            return "very_low"
    
    def get_summary(self) -> Dict[str, Any]:
        """获取统计摘要"""
        return {
            "total_samples": self.total_samples,
            "samples_by_type": dict(self.samples_by_type),
            "samples_by_strategy": dict(self.samples_by_strategy),
            "quality_distribution": dict(self.quality_distribution),
            "average_generation_time": self.generation_time / max(1, self.total_samples),
            "success_rate": self.success_rate
        }


# ==================== 核心生成器类 ====================

class IntentDrivenGenerator:
    """意图驱动的基础样本生成器"""
    
    def __init__(self, orm_config_path: str = "configs/orm_def.yaml"):
        self.orm_config_path = orm_config_path
        self.orm_config = self._load_orm_config()
        self.intent_templates = self._extract_intent_templates()
        self.weighted_sampler = WeightedSampler()
        
    def _load_orm_config(self) -> Dict[str, Any]:
        """加载ORM配置文件"""
        try:
            if Path(self.orm_config_path).exists():
                with open(self.orm_config_path, 'r', encoding='utf-8') as f:
                    return yaml.safe_load(f)
            else:
                logger.warning(f"ORM配置文件不存在: {self.orm_config_path}，使用默认配置")
                return self._get_default_orm_config()
        except Exception as e:
            logger.error(f"加载ORM配置失败: {e}")
            return self._get_default_orm_config()
    
    def _get_default_orm_config(self) -> Dict[str, Any]:
        """默认ORM配置"""
        return {
            "entities": ["客户", "订单", "产品", "用户", "供应商", "员工"],
            "field_types": ["VARCHAR(50)", "INT", "DECIMAL(10,2)", "DATETIME", "BOOLEAN", "TEXT"],
            "operations": ["ADD_COLUMN", "DELETE_COLUMN", "MODIFY_COLUMN", "ADD_RELATIONSHIP", "ENABLE_SOFT_DELETE"]
        }
    
    def _extract_intent_templates(self) -> List[Dict[str, Any]]:
        """从ORM配置中提取所有可能的意图结构"""
        templates = []
        entities = self.orm_config.get("entities", [])
        field_types = self.orm_config.get("field_types", [])
        operations = self.orm_config.get("operations", [])
        
        # 为每个操作类型生成模板组合
        for operation in operations:
            for entity in entities:
                for field_type in field_types:
                    template = {
                        "intentType": operation,
                        "targetConceptName": entity,
                        "props": {
                            "name": f"字段_{len(templates)}",
                            "stdSqlType": field_type
                        }
                    }
                    
                    # 根据操作类型添加特定属性
                    if operation == "ADD_COLUMN":
                        template["props"].update({
                            "nullable": True,
                            "comment": f"自动生成的{entity}字段"
                        })
                    elif operation == "ENABLE_SOFT_DELETE":
                        template["props"] = {
                            "deleteField": "is_deleted",
                            "deleteFieldType": "BOOLEAN"
                        }
                    
                    templates.append(template)
        
        logger.info(f"从ORM配置生成了 {len(templates)} 个基础意图模板")
        return templates
    
    def generate_base_samples(self, count: int = None) -> List[IntentSample]:
        """生成基础意图样本"""
        if count is None:
            count = min(200, len(self.intent_templates))
        
        samples = []
        available_templates = self.intent_templates.copy()
        
        for i in range(count):
            # 循环使用模板，确保多样性
            template_index = i % len(available_templates)
            template = available_templates[template_index].copy()
            
            # 生成个性化字段名
            entity = template["targetConceptName"]
            field_suggestions = self._get_field_suggestions(entity, template["intentType"])
            field_name = self.weighted_sampler.weighted_choice(
                field_suggestions, [1.0] * len(field_suggestions)
            )
            
            template["props"]["name"] = field_name
            
            # 生成基础指令
            instruction = self._generate_base_instruction(template)
            
            sample = IntentSample(
                intent_json=template,
                instruction=instruction,
                sample_type="base",
                confidence=1.0,
                metadata={
                    "template_id": template_index,
                    "generation_method": "intent_driven",
                    "entity": entity,
                    "field_name": field_name,
                    "operation": template["intentType"],
                    "index": i
                }
            )
            samples.append(sample)
        
        logger.info(f"生成了 {len(samples)} 个基础意图样本")
        return samples
    
    def _get_field_suggestions(self, entity: str, operation: str) -> List[str]:
        """根据实体和操作类型获取字段建议"""
        base_fields = {
            "客户": ["等级", "状态", "类型", "编号", "姓名", "电话", "地址", "信用等级"],
            "订单": ["状态", "金额", "数量", "创建时间", "完成时间", "备注", "优先级"],
            "产品": ["名称", "价格", "类别", "库存", "描述", "规格", "品牌"],
            "用户": ["用户名", "密码", "邮箱", "角色", "权限", "最后登录", "状态"],
            "供应商": ["名称", "联系人", "电话", "地址", "信用等级", "合作状态"],
            "员工": ["姓名", "部门", "职位", "工号", "入职时间", "工资", "状态"]
        }
        
        entity_fields = base_fields.get(entity, ["名称", "状态", "类型", "编号", "描述"])
        
        # 根据操作类型过滤字段
        if operation == "DELETE_COLUMN":
            # 删除操作，选择一些可选字段
            return [f for f in entity_fields if f not in ["编号", "名称"]]
        else:
            return entity_fields
    
    def _generate_base_instruction(self, intent_json: Dict[str, Any]) -> str:
        """生成基础指令"""
        intent_type = intent_json.get("intentType", "")
        entity = intent_json.get("targetConceptName", "")
        field_name = intent_json.get("props", {}).get("name", "字段")
        data_type = intent_json.get("props", {}).get("stdSqlType", "")
        
        templates = {
            "ADD_COLUMN": [
                f"给{entity}表添加一个{field_name}字段",
                f"为{entity}增加{field_name}属性",
                f"{entity}需要一个{field_name}字段",
                f"在{entity}表中加入{field_name}",
                f"添加{field_name}到{entity}表"
            ],
            "DELETE_COLUMN": [
                f"删除{entity}表的{field_name}字段",
                f"移除{entity}的{field_name}属性",
                f"去掉{entity}表中的{field_name}",
                f"把{entity}的{field_name}删了"
            ],
            "MODIFY_COLUMN": [
                f"修改{entity}表的{field_name}字段",
                f"更新{entity}的{field_name}属性",
                f"调整{entity}表{field_name}字段",
                f"变更{entity}的{field_name}字段类型"
            ],
            "ADD_RELATIONSHIP": [
                f"在{entity}和其他表之间添加关系",
                f"建立{entity}的关联关系",
                f"给{entity}增加外键关系"
            ],
            "ENABLE_SOFT_DELETE": [
                f"启用{entity}表的逻辑删除",
                f"{entity}表需要软删除功能",
                f"给{entity}添加逻辑删除",
                f"开启{entity}的软删除"
            ]
        }
        
        template_list = templates.get(intent_type, [f"对{entity}执行{intent_type}操作"])
        return self.weighted_sampler.weighted_choice(
            template_list, [1.0] * len(template_list)
        )


class ParaphraseGenerator:
    """多样化指令生成器"""
    
    def __init__(self, model_name: str = "Qwen/Qwen2.5-7B-Instruct"):
        self.model_name = model_name
        self.paraphrase_strategies = {
            "formal": "正式商务表达",
            "casual": "口语化日常表达",
            "technical": "技术专业表达",
            "brief": "简洁表达"
        }
        self.weighted_sampler = WeightedSampler()
    
    def generate_paraphrases(self, base_samples: List[IntentSample], 
                           variants_per_sample: int = 4) -> List[IntentSample]:
        """为基础样本生成多样化表达（同步版本）"""
        paraphrase_samples = []
        
        # 限制处理的基础样本数量，避免过多的改写
        limited_samples = base_samples[:min(50, len(base_samples))]
        
        for base_sample in limited_samples:
            for strategy_name in self.paraphrase_strategies.keys():
                for variant_id in range(variants_per_sample // len(self.paraphrase_strategies)):
                    try:
                        # 调用改写逻辑
                        paraphrased_instruction = self._apply_paraphrase_strategy(
                            base_sample.instruction, strategy_name
                        )
                        
                        paraphrase_sample = IntentSample(
                            intent_json=base_sample.intent_json.copy(),
                            instruction=paraphrased_instruction,
                            sample_type="paraphrase",
                            confidence=0.9,
                            metadata={
                                "base_sample_id": base_sample.hash_id,
                                "paraphrase_strategy": strategy_name,
                                "variant_id": variant_id,
                                "generation_method": "paraphrase"
                            }
                        )
                        paraphrase_samples.append(paraphrase_sample)
                        
                    except Exception as e:
                        logger.warning(f"改写样本生成失败: {e}")
                        continue
        
        logger.info(f"生成了 {len(paraphrase_samples)} 个改写样本")
        return paraphrase_samples
    
    def _apply_paraphrase_strategy(self, instruction: str, strategy: str) -> str:
        """应用改写策略"""
        if strategy == "formal":
            # 正式表达：添加敬语，使用正式词汇
            replacements = [
                ("给", "请为"),
                ("加", "添加"),
                ("删", "删除"),
                ("改", "修改")
            ]
            result = instruction
            for old, new in replacements:
                result = result.replace(old, new)
            if not result.startswith(("请", "烦请")):
                result = "请" + result
            return result
            
        elif strategy == "casual":
            # 口语化表达：使用简化词汇
            replacements = [
                ("添加", "加"),
                ("删除", "删"),
                ("修改", "改"),
                ("字段", "字段"),
                ("属性", "项"),
                ("请", "")
            ]
            result = instruction
            for old, new in replacements:
                result = result.replace(old, new)
            
            # 添加口语化前缀
            casual_prefixes = ["帮忙", "麻烦", "能不能", "可以"]
            prefix = self.weighted_sampler.weighted_choice(casual_prefixes, [1.0] * len(casual_prefixes))
            return f"{prefix}{result.strip()}"
            
        elif strategy == "technical":
            # 技术表达：使用专业术语
            replacements = [
                ("字段", "属性"),
                ("表", "实体"),
                ("添加", "配置"),
                ("删除", "移除"),
                ("修改", "更新")
            ]
            result = instruction
            for old, new in replacements:
                result = result.replace(old, new)
            return result
            
        else:  # brief
            # 简洁表达：去除修饰词
            result = instruction.replace("请", "").replace("一个", "").replace("需要", "")
            result = result.strip()
            
            # 进一步简化
            if "给" in result and "添加" in result:
                result = result.replace("给", "").replace("添加", "加")
            
            return result


class RobustnessGenerator:
    """鲁棒性与边界样本构建器"""
    
    def __init__(self):
        self.perturbation_strategies = [
            "entity_substitution",    # 实体替换
            "field_type_confusion",   # 字段类型混淆
            "partial_instruction",    # 部分指令
            "ambiguous_reference",    # 模糊引用
            "synonym_replacement"     # 同义词替换
        ]
        self.weighted_sampler = WeightedSampler()
    
    def generate_adversarial_samples(self, base_samples: List[IntentSample], 
                                   ratio: float = 0.3) -> List[IntentSample]:
        """生成对抗样本"""
        adversarial_samples = []
        sample_count = int(len(base_samples) * ratio)
        
        selected_samples = base_samples[:sample_count] if sample_count <= len(base_samples) else base_samples
        
        for sample in selected_samples:
            for strategy in self.perturbation_strategies:
                try:
                    perturbed_sample = self._apply_perturbation(sample, strategy)
                    if perturbed_sample:
                        adversarial_samples.append(perturbed_sample)
                except Exception as e:
                    logger.warning(f"对抗样本生成失败 {strategy}: {e}")
                    continue
        
        logger.info(f"生成了 {len(adversarial_samples)} 个对抗样本")
        return adversarial_samples
    
    def _apply_perturbation(self, sample: IntentSample, strategy: str) -> Optional[IntentSample]:
        """应用扰动策略"""
        if strategy == "entity_substitution":
            # 实体替换：用相似但不同的实体替换
            entity_replacements = {
                "客户": ["客人", "用户", "顾客"],
                "订单": ["订购", "单据", "需求"],
                "产品": ["商品", "货物", "物品"],
                "用户": ["客户", "账户", "成员"],
                "供应商": ["供货商", "厂商", "合作方"],
                "员工": ["职员", "工作人员", "人员"]
            }
            
            original_entity = sample.intent_json.get("targetConceptName", "")
            replacements = entity_replacements.get(original_entity, ["实体"])
            new_entity = self.weighted_sampler.weighted_choice(replacements, [1.0] * len(replacements))
            
            perturbed_instruction = sample.instruction.replace(original_entity, new_entity)
            perturbed_intent = sample.intent_json.copy()
            perturbed_intent["targetConceptName"] = new_entity
            
        elif strategy == "field_type_confusion":
            # 字段类型混淆：改变术语表达
            perturbed_instruction = sample.instruction.replace("字段", "属性").replace("类型", "格式")
            perturbed_intent = sample.intent_json.copy()
            
        elif strategy == "partial_instruction":
            # 部分指令：随机删除部分内容
            words = sample.instruction.split()
            if len(words) > 3:
                keep_count = random.randint(max(2, len(words)//2), len(words) - 1)
                kept_words = random.sample(words, keep_count)
                # 保持一定的语序
                kept_words.sort(key=lambda x: words.index(x))
                perturbed_instruction = " ".join(kept_words)
            else:
                perturbed_instruction = sample.instruction
            perturbed_intent = sample.intent_json.copy()
            
        elif strategy == "ambiguous_reference":
            # 模糊引用：使用代词和模糊表达
            replacements = [
                (r"客户表", "这个表"),
                (r"订单表", "那个表"),
                (r"产品", "那个"),
                (r"字段", "它"),
                (r"属性", "这个属性")
            ]
            perturbed_instruction = sample.instruction
            for pattern, replacement in replacements:
                if pattern.replace("表", "").replace("字段", "").replace("属性", "") in perturbed_instruction:
                    perturbed_instruction = perturbed_instruction.replace(pattern, replacement)
                    break
            perturbed_intent = sample.intent_json.copy()
            
        else:  # synonym_replacement
            # 同义词替换
            synonyms = {
                "添加": ["增加", "新增", "加入"],
                "删除": ["移除", "去掉", "取消"],
                "修改": ["更改", "变更", "调整"],
                "字段": ["列", "项", "属性"],
                "表": ["数据表", "实体"]
            }
            
            perturbed_instruction = sample.instruction
            for original, synonym_list in synonyms.items():
                if original in perturbed_instruction:
                    synonym = self.weighted_sampler.weighted_choice(synonym_list, [1.0] * len(synonym_list))
                    perturbed_instruction = perturbed_instruction.replace(original, synonym, 1)
                    break
            perturbed_intent = sample.intent_json.copy()
        
        return IntentSample(
            intent_json=perturbed_intent,
            instruction=perturbed_instruction,
            sample_type="adversarial",
            confidence=0.7,
            metadata={
                "base_sample_id": sample.hash_id,
                "perturbation_strategy": strategy,
                "generation_method": "adversarial"
            }
        )
    
    def generate_boundary_samples(self, base_samples: List[IntentSample], 
                                count: int = 50) -> List[IntentSample]:
        """生成边界样本（不完整或歧义的语句）"""
        boundary_samples = []
        
        # 预定义的不完整模式
        incomplete_patterns = [
            "加字段",
            "删除那个",
            "修改一下",
            "客户需要",
            "订单表",
            "给它添加",
            "要个字段",
            "删掉",
            "改改",
            "新增"
        ]
        
        # 基于现有样本生成模糊版本
        ambiguous_patterns = []
        for sample in base_samples[:20]:  # 使用前20个样本作为基础
            words = sample.instruction.split()
            if len(words) >= 3:
                # 提取关键词组合
                key_combo = " ".join(words[:2])
                ambiguous_patterns.append(key_combo)
        
        all_patterns = incomplete_patterns + ambiguous_patterns
        
        for i in range(count):
            pattern = all_patterns[i % len(all_patterns)]
            
            # 随机选择一个基础样本作为参考
            ref_sample = base_samples[i % len(base_samples)] if base_samples else None
            
            boundary_sample = IntentSample(
                intent_json={},  # 边界样本通常没有完整的意图
                instruction=pattern,
                sample_type="boundary",
                confidence=0.3,
                metadata={
                    "reference_sample_id": ref_sample.hash_id if ref_sample else "",
                    "boundary_type": "incomplete",
                    "generation_method": "boundary",
                    "pattern_source": "predefined" if i < len(incomplete_patterns) else "derived"
                }
            )
            boundary_samples.append(boundary_sample)
        
        logger.info(f"生成了 {len(boundary_samples)} 个边界样本")
        return boundary_samples
    
    def generate_negative_samples(self, count: int = 100) -> List[IntentSample]:
        """生成负样本（无效或无关指令）"""
        negative_categories = {
            "daily_life": [
                "今天天气怎么样",
                "晚上吃什么",
                "明天几点起床",
                "周末去哪里玩",
                "买点什么东西"
            ],
            "work_general": [
                "开个会议",
                "写个报告",
                "发送邮件",
                "打个电话",
                "整理文档"
            ],
            "system_operations": [
                "重启系统",
                "备份数据",
                "清理缓存",
                "更新软件",
                "检查网络"
            ],
            "business_unrelated": [
                "订机票",
                "预定酒店",
                "买火车票",
                "叫外卖",
                "看电影"
            ]
        }
        
        negative_samples = []
        all_patterns = []
        for category, patterns in negative_categories.items():
            all_patterns.extend([(pattern, category) for pattern in patterns])
        
        for i in range(count):
            pattern, category = all_patterns[i % len(all_patterns)]
            
            negative_sample = IntentSample(
                intent_json={},
                instruction=pattern,
                sample_type="negative",
                confidence=0.0,
                metadata={
                    "negative_type": category,
                    "generation_method": "negative"
                }
            )
            negative_samples.append(negative_sample)
        
        logger.info(f"生成了 {len(negative_samples)} 个负样本")
        return negative_samples


class QualityController:
    """数据质量控制器"""
    
    def __init__(self):
        self.seen_hashes = set()
        self.semantic_threshold = 0.85
        self.quality_metrics = {
            "length_check": True,
            "content_check": True,
            "format_check": True,
            "semantic_check": False  # 需要额外依赖，默认关闭
        }
    
    def enable_semantic_check(self, enabled: bool = True):
        """启用或禁用语义检查"""
        self.quality_metrics["semantic_check"] = enabled
        if enabled:
            try:
                # 延迟导入，避免依赖问题
                from sentence_transformers import SentenceTransformer
                self.sentence_transformer = SentenceTransformer('all-MiniLM-L6-v2')
                logger.info("语义检查已启用")
            except ImportError:
                logger.warning("sentence_transformers未安装，语义检查无法启用")
                self.quality_metrics["semantic_check"] = False
    
    def deduplicate_samples(self, samples: List[IntentSample]) -> List[IntentSample]:
        """多维度去重：哈希 + 语义相似性"""
        unique_samples = []
        
        # 第一层：哈希去重
        hash_filtered = []
        for sample in samples:
            if sample.hash_id not in self.seen_hashes:
                self.seen_hashes.add(sample.hash_id)
                hash_filtered.append(sample)
        
        logger.info(f"哈希去重后保留 {len(hash_filtered)}/{len(samples)} 个样本")
        
        # 第二层：语义去重（如果启用）
        if self.quality_metrics["semantic_check"] and len(hash_filtered) > 1:
            unique_samples = self._semantic_deduplication(hash_filtered)
        else:
            unique_samples = hash_filtered
        
        logger.info(f"去重后保留 {len(unique_samples)}/{len(samples)} 个样本")
        return unique_samples
    
    def _semantic_deduplication(self, samples: List[IntentSample]) -> List[IntentSample]:
        """基于语义相似性的去重"""
        if not hasattr(self, 'sentence_transformer'):
            return samples
        
        try:
            # 计算所有指令的嵌入
            instructions = [sample.instruction for sample in samples]
            embeddings = self.sentence_transformer.encode(instructions)
            
            # 计算相似性矩阵
            from sklearn.metrics.pairwise import cosine_similarity
            similarity_matrix = cosine_similarity(embeddings)
            
            # 标记需要移