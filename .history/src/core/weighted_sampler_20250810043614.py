"""
加权采样器：实现配置驱动的权重分配和采样策略
遵循KISS原则，提供简单高效的加权采样功能
"""

import random
import numpy as np
from typing import Dict, List, Any, Optional
from loguru import logger

class WeightedSampler:
    """基础加权采样器 - 实现简单高效的权重分配算法"""
    
    @staticmethod
    def weighted_choice(choices: List[str], weights: List[float]) -> str:
        """基于权重的随机选择
        
        Args:
            choices: 选择项列表
            weights: 对应的权重列表
            
        Returns:
            随机选中的项目
        """
        if not choices:
            return ""
            
        if not weights or len(choices) != len(weights):
            logger.warning("选择项和权重数量不匹配，使用均匀分布")
            return random.choice(choices)
        
        total_weight = sum(weights)
        if total_weight <= 0:
            logger.warning("权重总和为0或负数，使用均匀分布")
            return random.choice(choices)
        
        # 归一化权重并累积
        rand_val = random.random() * total_weight
        cumulative = 0
        
        for i, weight in enumerate(weights):
            cumulative += weight
            if rand_val <= cumulative:
                return choices[i]
        
        # 兜底返回最后一个
        return choices[-1]
    
    @staticmethod
    def weighted_choice_dict(weight_dict: Dict[str, float]) -> str:
        """基于字典权重的随机选择
        
        Args:
            weight_dict: 键为选择项，值为权重的字典
            
        Returns:
            随机选中的键
        """
        if not weight_dict:
            return ""
            
        choices = list(weight_dict.keys())
        weights = list(weight_dict.values())
        return WeightedSampler.weighted_choice(choices, weights)
    
    @staticmethod
    def sample_by_crud_weights(intent_types: List[str], 
                              crud_weights: Dict[str, float], 
                              total_count: int) -> Dict[str, int]:
        """基于CRUD权重分配样本数量
        
        Args:
            intent_types: 意图类型列表
            crud_weights: CRUD操作权重 {"create": 40, "read": 25, "update": 20, "delete": 15}
            total_count: 总样本数量
            
        Returns:
            每种意图类型分配的样本数量
        """
        if not intent_types:
            return {}
            
        if not crud_weights or total_count <= 0:
            # 均匀分配
            count_per_type = total_count // len(intent_types)
            return {intent_type: count_per_type for intent_type in intent_types}
        
        # 简化映射：根据意图类型映射到CRUD操作
        intent_to_crud_mapping = {
            "ADD_COLUMN": "create",
            "ADD_RELATIONSHIP": "create", 
            "ADD_ENTITY": "create",
            "DELETE_COLUMN": "delete",
            "DELETE_RELATIONSHIP": "delete",
            "DELETE_ENTITY": "delete",
            "MODIFY_COLUMN": "update",
            "UPDATE_ENTITY": "update",
            "QUERY_ENTITY": "read"
        }
        
        # 计算各意图类型的权重
        intent_weights = {}
        for intent_type in intent_types:
            crud_operation = intent_to_crud_mapping.get(intent_type, "create")
            intent_weights[intent_type] = crud_weights.get(crud_operation, 25.0)
        
        # 按权重分配样本数量
        total_weight = sum(intent_weights.values())
        result = {}
        allocated = 0
        
        for i, (intent_type, weight) in enumerate(intent_weights.items()):
            if i == len(intent_weights) - 1:  # 最后一个取剩余
                result[intent_type] = total_count - allocated
            else:
                count = int(total_count * weight / total_weight)
                result[intent_type] = count
                allocated += count
        
        logger.info(f"CRUD权重分配结果: {result}")
        return result
    
    def sample_by_semantic_weights(self, weights: Dict[str, float], 
                                 total_count: int) -> Dict[str, int]:
        """基于语义权重分配样本
        
        Args:
            weights: 语义策略权重字典
            total_count: 总样本数量
            
        Returns:
            各策略分配的样本数量
        """
        if not weights or total_count <= 0:
            return {}
        
        total_weight = sum(weights.values())
        if total_weight <= 0:
            return {key: 0 for key in weights.keys()}
        
        result = {}
        allocated = 0
        
        items = list(weights.items())
        for i, (key, weight) in enumerate(items):
            if i == len(items) - 1:  # 最后一个取剩余
                result[key] = total_count - allocated
            else:
                count = int(total_count * weight / total_weight)
                result[key] = count
                allocated += count
        
        return result

class DistributionSampler:
    """分布采样器 - 用于监控和优化采样分布"""
    
    def __init__(self):
        self.sampling_history = {}
        self.total_samples = 0
    
    def record_sample(self, category: str, count: int = 1):
        """记录采样情况
        
        Args:
            category: 样本类别
            count: 样本数量
        """
        self.sampling_history[category] = self.sampling_history.get(category, 0) + count
        self.total_samples += count
    
    def get_sampling_statistics(self) -> Dict[str, Any]:
        """获取采样统计信息
        
        Returns:
            包含总数、分布和熵值的统计信息
        """
        return {
            "total_samples": self.total_samples,
            "distribution": self.sampling_history.copy(),
            "entropy": self._calculate_entropy(),
            "categories_count": len(self.sampling_history)
        }
    
    def _calculate_entropy(self) -> float:
        """计算分布熵值
        
        Returns:
            分布熵值，越高表示分布越均匀
        """
        if not self.sampling_history or self.total_samples == 0:
            return 0.0
        
        entropy = 0.0
        for count in self.sampling_history.values():
            if count > 0:
                p = count / self.total_samples
                entropy -= p * np.log2(p)
        
        return float(entropy)
    
    def get_distribution_balance_score(self) -> float:
        """获取分布均衡分数
        
        Returns:
            0-1之间的分数，1表示完全均匀分布
        """
        if len(self.sampling_history) <= 1:
            return 1.0
        
        max_entropy = np.log2(len(self.sampling_history))
        current_entropy = self._calculate_entropy()
        
        return float(current_entropy / max_entropy) if max_entropy > 0 else 0.0

class AdaptiveSampler:
    """自适应采样器 - 基于历史表现动态调整采样策略"""
    
    def __init__(self, learning_rate: float = 0.1):
        self.quality_history = {}
        self.performance_weights = {}
        self.learning_rate = learning_rate
        self.min_samples_for_adaptation = 5
    
    def record_sample_quality(self, strategy: str, quality_score: float):
        """记录采样策略的质量分数
        
        Args:
            strategy: 策略名称
            quality_score: 质量分数 (0-1)
        """
        if strategy not in self.quality_history:
            self.quality_history[strategy] = []
        
        self.quality_history[strategy].append(quality_score)
        
        # 保持历史记录在合理范围内
        max_history = 50
        if len(self.quality_history[strategy]) > max_history:
            self.quality_history[strategy] = self.quality_history[strategy][-max_history:]
        
        # 更新性能权重
        self._update_performance_weights(strategy)
    
    def _update_performance_weights(self, strategy: str):
        """更新策略的性能权重
        
        Args:
            strategy: 策略名称
        """
        history = self.quality_history[strategy]
        if len(history) < self.min_samples_for_adaptation:
            # 样本不足，使用默认权重
            self.performance_weights[strategy] = 1.0
            return
        
        # 计算指数移动平均，给最近的样本更高权重
        weights = np.array([0.9 ** (len(history) - i - 1) for i in range(len(history))])
        weights = weights / weights.sum()
        
        weighted_average = np.average(history, weights=weights)
        self.performance_weights[strategy] = float(weighted_average)
    
    def get_adaptive_weights(self, base_weights: Dict[str, float]) -> Dict[str, float]:
        """获取自适应调整后的权重
        
        Args:
            base_weights: 基础权重
            
        Returns:
            调整后的权重
        """
        adaptive_weights = base_weights.copy()
        
        # 应用性能调整
        for strategy, base_weight in base_weights.items():
            performance_weight = self.performance_weights.get(strategy, 1.0)
            
            # 使用学习率控制调整幅度
            adjustment_factor = 1.0 + self.learning_rate * (performance_weight - 1.0)
            adaptive_weights[strategy] = base_weight * adjustment_factor
        
        # 归一化权重
        total_weight = sum(adaptive_weights.values())
        if total_weight > 0:
            for strategy in adaptive_weights:
                adaptive_weights[strategy] /= total_weight
        
        return adaptive_weights
    
    def suggest_sampling_strategy(self, available_strategies: List[str]) -> str:
        """建议最佳采样策略
        
        Args:
            available_strategies: 可用的策略列表
            
        Returns:
            推荐的策略名称
        """
        if not available_strategies:
            return ""
        
        # 基于历史性能选择最佳策略
        best_strategy = available_strategies[0]
        best_score = 0.0
        
        for strategy in available_strategies:
            if strategy in self.performance_weights:
                score = self.performance_weights[strategy]
                if score > best_score:
                    best_score = score
                    best_strategy = strategy
            else:
                # 未知策略给予中等评分
                if 0.5 > best_score:
                    best_score = 0.5
                    best_strategy = strategy
        
        return best_strategy
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告
        
        Returns:
            包含各策略性能统计的报告
        """
        report = {
            "strategies_count": len(self.quality_history),
            "total_samples": sum(len(history) for history in self.quality_history.values()),
            "performance_weights": self.performance_weights.copy(),
            "strategy_statistics": {}
        }
        
        for strategy, history in self.quality_history.items():
            report["strategy_statistics"][strategy] = {
                "sample_count": len(history),
                "average_quality": float(np.mean(history)) if history else 0.0,
                "quality_std": float(np.std(history)) if len(history) > 1 else 0.0,
                "recent_trend": self._calculate_trend(history)
            }
        
        return report
    
    def _calculate_trend(self, history: List[float]) -> str:
        """计算质量趋势
        
        Args:
            history: 历史质量分数
            
        Returns:
            趋势描述: "improving", "declining", "stable"
        """
        if len(history) < 10:
            return "insufficient_data"
        
        # 比较最近25%和之前25%的平均值
        recent_count = max(1, len(history) // 4)
        recent_avg = np.mean(history[-recent_count:])
        early_avg = np.mean(history[:recent_count])
        
        diff = recent_avg - early_avg
        
        if diff > 0.05:
            return "improving"
        elif diff < -0.05:
            return "declining" 
        else:
            return "stable"

# 工具函数
def create_balanced_distribution(categories: List[str], total_count: int) -> Dict[str, int]:
    """创建平衡的分布
    
    Args:
        categories: 类别列表
        total_count: 总数量
        
    Returns:
        均匀分布的数量字典
    """
    if not categories or total_count <= 0:
        return {}
    
    base_count = total_count // len(categories)
    remainder = total_count % len(categories)
    
    result = {}
    for i, category in enumerate(categories):
        result[category] = base_count + (1 if i < remainder else 0)
    
    return result

def validate_weights(weights: Dict[str, float]) -> bool:
    """验证权重配置的有效性
    
    Args:
        weights: 权重字典
        
    Returns:
        是否有效
    """
    if not weights:
        return False
    
    # 检查权重是否为正数
    for weight in weights.values():
        if weight < 0:
            logger.warning(f"发现负权重: {weight}")
            return False
    
    # 检查权重总和是否大于0
    total_weight = sum(weights.values())
    if total_weight <= 0:
        logger.warning(f"权重总和为0或负数: {total_weight}")
        return False
    
    return True


class EnhancedSamplingAdapter:
    """增强采样适配器 - 连接原有系统和新系统"""

    def __init__(self):
        self.weighted_sampler = WeightedSampler()
        self.distribution_sampler = DistributionSampler()
        self.adaptive_sampler = AdaptiveSampler()
        self.enabled_features = {
            "adaptive_sampling": True,
            "distribution_monitoring": True,
            "hard_example_mining": True
        }

    def set_feature_enabled(self, feature_name: str, enabled: bool):
        """启用或禁用特定功能"""
        if feature_name in self.enabled_features:
            self.enabled_features[feature_name] = enabled
            logger.info(f"功能 {feature_name} {'启用' if enabled else '禁用'}")

    def enhanced_semantic_sampling(self, semantic_weights: Dict[str, float],
                                 total_count: int,
                                 quality_feedback: Dict[str, float] = None) -> Dict[str, int]:
        """增强的语义采样"""
        # 应用自适应调整
        if self.enabled_features["adaptive_sampling"] and quality_feedback:
            for strategy, quality in quality_feedback.items():
                self.adaptive_sampler.record_sample_quality(strategy, quality)

            adjusted_weights = self.adaptive_sampler.get_adaptive_weights(semantic_weights)
        else:
            adjusted_weights = semantic_weights

        # 执行分配
        distribution = self.weighted_sampler.sample_by_semantic_weights(
            adjusted_weights, total_count
        )

        return distribution

    def enhanced_weighted_choice(self, choices: List[str], weights: List[float], context: str = None) -> str:
        """增强的加权选择"""
        return self.weighted_sampler.weighted_choice(choices, weights)