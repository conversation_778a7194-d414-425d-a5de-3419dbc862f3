"""
反向数据生成器：从意图结构生成自然语言表达
"""

import json
import random
import asyncio
from typing import List, Dict, Any, Tuple
from itertools import product, combinations
import re
from loguru import logger

from src.models.intent_models import TrainingSample, Intent, IntentType

class ReverseDataGenerator:
    """反向数据生成器：从意图结构生成自然语言表达"""
    
    def __init__(self, template_path: str = "data/templates/intent_templates.json", 
                 terms_path: str = "data/templates/business_terms.json"):
        self.template_path = template_path
        self.terms_path = terms_path
        self.templates = {}
        self.business_terms = {}
        self.load_templates()
        self.load_business_terms()
    
    def load_templates(self):
        """加载意图模板"""
        try:
            with open(self.template_path, 'r', encoding='utf-8') as f:
                self.templates = json.load(f)
            logger.info(f"加载了 {len(self.templates)} 个意图模板")
        except Exception as e:
            logger.error(f"模板加载失败: {e}")
            self.templates = {}
    
    def load_business_terms(self):
        """加载业务术语词典"""
        try:
            with open(self.terms_path, 'r', encoding='utf-8') as f:
                self.business_terms = json.load(f)
            logger.info("业务术语词典加载完成")
        except Exception as e:
            logger.error(f"业务术语加载失败: {e}")
            self.business_terms = {}
    
    def generate_single_intent_samples(self, intent_type: str, count: int = 100) -> List[TrainingSample]:
        """为单个意图类型生成样本"""
        if intent_type not in self.templates:
            raise ValueError(f"未找到意图类型: {intent_type}")
        
        template_config = self.templates[intent_type]
        samples = []
        
        logger.info(f"开始生成 {intent_type} 类型的样本，目标数量: {count}")
        
        # 生成基础样本
        base_samples = self._generate_base_samples(intent_type, template_config, count // 2)
        samples.extend(base_samples)
        
        # 生成变体样本
        variant_samples = self._generate_variant_samples(intent_type, template_config, count // 2)
        samples.extend(variant_samples)
        
        # 随机打乱
        random.shuffle(samples)
        
        logger.info(f"{intent_type} 类型生成了 {len(samples)} 个样本")
        return samples[:count]
    
    def _generate_base_samples(self, intent_type: str, template_config: Dict, count: int) -> List[TrainingSample]:
        """生成基础样本"""
        samples = []
        
        entities = template_config.get("entities", [])
        field_names = template_config.get("field_names", [])
        data_types = template_config.get("data_types", ["VARCHAR(50)"])
        expressions = template_config.get("expressions", [])
        
        for _ in range(count):
            try:
                # 随机选择组合
                entity = random.choice(entities)
                field_name = random.choice(field_names)
                data_type = random.choice(data_types)
                expression_template = random.choice(expressions)
                
                # 生成自然语言指令
                variables = {
                    "entity": entity,
                    "field_name": field_name,
                    "data_type": data_type,
                    "nullable": random.choice(["true", "false"]),
                    "default_value": self._generate_default_value(data_type),
                    "comment": f"{field_name}字段"
                }
                
                # 处理特殊类型的变量
                if intent_type == "MODIFY_COLUMN":
                    variables["new_field_name"] = random.choice(template_config.get("new_field_names", [field_name]))
                elif intent_type in ["ADD_RELATIONSHIP", "DELETE_RELATIONSHIP"]:
                    variables["entity1"] = entity
                    variables["entity2"] = random.choice([e for e in entities if e != entity])
                    variables["relationship_type"] = random.choice(template_config.get("relationship_types", ["one-to-many"]))
                    variables["foreign_key"] = f"{variables['entity2'].lower()}_id"
                
                instruction = self._fill_expression_template(expression_template, variables)
                
                # 生成对应的意图结构
                intent = self._generate_intent_from_template(template_config["template"], variables)
                
                sample = TrainingSample(
                    instruction=instruction,
                    output=[Intent(**intent)],
                    metadata={
                        "intent_type": intent_type,
                        "generation_method": "base_template",
                        "template": expression_template,
                        "variables": variables
                    }
                )
                samples.append(sample)
                
            except Exception as e:
                logger.warning(f"生成基础样本失败: {e}")
                continue
        
        return samples
    
    def _generate_variant_samples(self, intent_type: str, template_config: Dict, count: int) -> List[TrainingSample]:
        """生成变体样本（同义词替换、语序调整等）"""
        samples = []
        base_samples = self._generate_base_samples(intent_type, template_config, count)
        
        for base_sample in base_samples:
            try:
                # 同义词替换
                variant_instruction = self._apply_synonym_replacement(base_sample.instruction)
                
                # 语序调整
                if random.random() < 0.3:
                    variant_instruction = self._apply_word_order_change(variant_instruction)
                
                # 添加修饰词
                if random.random() < 0.2:
                    variant_instruction = self._add_modifiers(variant_instruction)
                
                variant_sample = TrainingSample(
                    instruction=variant_instruction,
                    output=base_sample.output,
                    metadata={
                        **base_sample.metadata,
                        "generation_method": "variant",
                        "original_instruction": base_sample.instruction
                    }
                )
                samples.append(variant_sample)
                
            except Exception as e:
                logger.warning(f"生成变体样本失败: {e}")
                continue
        
        return samples
    
    def _fill_expression_template(self, template: str, variables: Dict[str, str]) -> str:
        """填充表达式模板"""
        result = template
        for key, value in variables.items():
            placeholder = "{" + key + "}"
            if placeholder in result:
                result = result.replace(placeholder, str(value))
        return result
    
    def _generate_intent_from_template(self, template: Dict, variables: Dict) -> Dict:
        """从模板生成意图结构"""
        result = {}
        for key, value in template.items():
            if isinstance(value, str) and value.startswith("{") and value.endswith("}"):
                var_name = value[1:-1]
                result[key] = variables.get(var_name, value)
            elif isinstance(value, dict):
                result[key] = self._generate_intent_from_template(value, variables)
            else:
                result[key] = value
        return result
    
    def _generate_default_value(self, data_type: str) -> Any:
        """根据数据类型生成默认值"""
        if "INT" in data_type or "DECIMAL" in data_type:
            return random.choice([None, 0, 1, -1])
        elif "VARCHAR" in data_type or "TEXT" in data_type:
            return random.choice([None, "", "默认值"])
        elif "BOOLEAN" in data_type:
            return random.choice([None, True, False])
        elif "DATE" in data_type:
            return None
        else:
            return None
    
    def _apply_synonym_replacement(self, text: str) -> str:
        """应用同义词替换"""
        # 实体同义词替换
        if "entities" in self.business_terms:
            for standard, synonyms in self.business_terms["entities"].items():
                if standard in text:
                    synonym = random.choice(synonyms)
                    text = text.replace(standard, synonym)
                    break
        
        # 动作词同义词替换
        if "action_words" in self.business_terms:
            for standard, synonyms in self.business_terms["action_words"].items():
                if standard in text:
                    synonym = random.choice(synonyms)
                    text = text.replace(standard, synonym)
                    break
        
        return text
    
    def _apply_word_order_change(self, text: str) -> str:
        """应用语序调整"""
        # 简单的语序调整规则
        patterns = [
            (r"给(.+?)表添加(.+?)字段", r"添加\2字段到\1表"),
            (r"删除(.+?)表的(.+?)字段", r"把\1表的\2字段删除"),
            (r"(.+?)需要(.+?)字段", r"需要给\1添加\2字段")
        ]
        
        for pattern, replacement in patterns:
            if re.search(pattern, text):
                text = re.sub(pattern, replacement, text)
                break
        
        return text
    
    def _add_modifiers(self, text: str) -> str:
        """添加修饰词"""
        modifiers = ["请", "麻烦", "帮忙", "需要", "想要"]
        if random.random() < 0.5:
            modifier = random.choice(modifiers)
            text = modifier + text
        
        return text
    
    async def generate_training_dataset(self, samples_per_intent: int = 500) -> List[TrainingSample]:
        """生成完整的训练数据集"""
        all_samples = []
        
        for intent_type in self.templates.keys():
            logger.info(f"正在生成 {intent_type} 类型的样本...")
            try:
                samples = self.generate_single_intent_samples(intent_type, samples_per_intent)
                all_samples.extend(samples)
            except Exception as e:
                logger.error(f"生成 {intent_type} 类型样本失败: {e}")
                continue
        
        logger.info(f"总共生成了 {len(all_samples)} 个训练样本")
        return all_samples
    
    def generate_fuzzy_samples(self, count: int = 200) -> List[TrainingSample]:
        """生成模糊/不完整样本，用于澄清推荐训练"""
        fuzzy_samples = []
        
        for intent_type, config in self.templates.items():
            fuzzy_expressions = config.get("fuzzy_expressions", [])
            entities = config.get("entities", [])
            
            for _ in range(count // len(self.templates)):
                try:
                    entity = random.choice(entities)
                    fuzzy_expr = random.choice(fuzzy_expressions)
                    
                    instruction = fuzzy_expr.format(entity=entity)
                    
                    # 模糊样本不返回具体意图，而是需要澄清
                    sample = TrainingSample(
                        instruction=instruction,
                        output=[],  # 空意图列表表示需要澄清
                        metadata={
                            "intent_type": "CLARIFICATION_NEEDED",
                            "potential_intent": intent_type,
                            "generation_method": "fuzzy",
                            "entity": entity
                        }
                    )
                    fuzzy_samples.append(sample)
                    
                except Exception as e:
                    logger.warning(f"生成模糊样本失败: {e}")
                    continue
        
        logger.info(f"生成了 {len(fuzzy_samples)} 个模糊样本")
        return fuzzy_samples