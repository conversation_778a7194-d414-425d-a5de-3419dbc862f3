"""
增强的distilabel Pipeline：集成基础样本生成与多样性扩展系统
保留distilabel框架结构，添加EnhancedSamplingPipeline和多模型互评功能
"""

import os
import yaml
import json
import asyncio
import logging
import hashlib
from datetime import datetime
from typing import List, Dict, Any, Optional, Union, Tuple
from pathlib import Path

from distilabel.pipeline import Pipeline
from distilabel.steps import Step, GeneratorStep, KeepColumns, StepInput, StepOutput
from distilabel.steps.tasks import TextGeneration
from distilabel.models import InferenceEndpointsLLM, TransformersLLM, OpenAILLM

# 设置日志
logger = logging.getLogger(__name__)

# ==================== Pipeline创建和管理函数 ====================

def _create_llm_instance(model_config: Dict[str, Any], model_type: str = "Unknown") -> Any:
    """创建LLM实例"""
    model_name = model_config.get("name", "")
    model_type_config = model_config.get("type", "transformers")
    
    try:
        if model_type_config == "transformers":
            # 本地Transformers模型
            llm = TransformersLLM(
                model=model_name,
                device_map=model_config.get("device_map", "auto"),
                torch_dtype=model_config.get("torch_dtype", "float16"),
                generation_kwargs=model_config.get("generation_kwargs", {
                    "max_new_tokens": 512,
                    "temperature": 0.7,
                    "do_sample": True
                })
            )
            logger.info(f"✅ 创建Transformers模型: {model_name}")
            
        elif model_type_config == "inference_endpoints":
            # 推理端点模型（API调用）
            api_key = model_config.get("api_key", "")
            if not api_key:
                raise ValueError(f"{model_type} 模型缺少API密钥")
            
            llm = InferenceEndpointsLLM(
                model_id=model_name,
                api_key=api_key,
                generation_kwargs=model_config.get("generation_kwargs", {
                    "max_new_tokens": 512,
                    "temperature": 0.7
                })
            )
            logger.info(f"✅ 创建API模型: {model_name}")
            
        elif model_type_config == "openai":
            # OpenAI兼容模型
            llm = OpenAILLM(
                model=model_name,
                api_key=model_config.get("api_key", ""),
                base_url=model_config.get("base_url"),
                generation_kwargs=model_config.get("generation_kwargs", {})
            )
            logger.info(f"✅ 创建OpenAI兼容模型: {model_name}")
            
        else:
            raise ValueError(f"不支持的模型类型: {model_type_config}")
        
        return llm
        
    except Exception as e:
        logger.error(f"❌ 创建{model_type}模型失败: {e}")
        raise


def create_enhanced_pipeline(config_path: str = "configs/sampling_config.yaml") -> Pipeline:
    """创建增强采样Pipeline"""
    
    # 加载配置
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    logger.info("🚀 创建增强采样Pipeline...")
    
    # 检查Pipeline模式
    pipeline_mode = config.get("pipeline", {}).get("mode", "enhanced_sampling")
    
    if pipeline_mode == "enhanced_sampling":
        return _create_enhanced_sampling_pipeline(config, config_path)
    elif pipeline_mode == "multi_model_evaluation":
        return _create_multi_model_pipeline(config, config_path)
    elif pipeline_mode == "hybrid":
        return _create_hybrid_pipeline(config, config_path)
    else:
        logger.warning(f"未知的Pipeline模式: {pipeline_mode}，使用增强采样模式")
        return _create_enhanced_sampling_pipeline(config, config_path)


def _create_enhanced_sampling_pipeline(config: Dict[str, Any], config_path: str) -> Pipeline:
    """创建增强采样Pipeline"""
    
    with Pipeline(name="EnhancedSamplingPipeline") as pipeline:
        # Step 1: 增强采样生成
        enhanced_sampler = EnhancedSamplingStep(
            name="enhanced_sampler",
            config_path=config_path
        )
        
        # Step 2: 质量过滤
        quality_filter = KeepColumns(
            name="quality_filter",
            columns=["instruction", "intent_json", "sample_type", "confidence", "quality_score"]
        )
        
        # 构建Pipeline流程
        enhanced_sampler >> quality_filter
    
    logger.info("✅ 增强采样Pipeline创建完成")
    return pipeline


def _create_multi_model_pipeline(config: Dict[str, Any], config_path: str) -> Pipeline:
    """创建多模型互评Pipeline"""
    
    # 配置模型
    primary_model_config = config.get("paraphrase_generation", {}).get("model_config", {})
    evaluator_config = config.get("g_eval", {}).get("evaluator_model", {})
    
    # 创建模型实例
    try:
        primary_llm = _create_llm_instance(primary_model_config, "Primary")
        evaluator_llm = _create_llm_instance(evaluator_config, "Evaluator")
    except Exception as e:
        logger.error(f"模型创建失败: {e}")
        raise
    
    # 获取评估标准
    g_eval_criteria = config.get("g_eval", {}).get("criteria", [
        {"name": "semantic_consistency", "description": "语义一致性"},
        {"name": "expression_naturalness", "description": "表达自然度"},
        {"name": "ambiguity_level", "description": "歧义程度"}
    ])
    
    with Pipeline(name="MultiModelEvaluationPipeline") as pipeline:
        # Step 1: 增强采样生成
        enhanced_sampler = EnhancedSamplingStep(
            name="enhanced_sampler",
            config_path=config_path
        )
        
        # Step 2: 多模型评估
        multi_evaluator = MultiModelEvaluationStep(
            name="multi_evaluator",
            evaluator_llm=evaluator_llm,
            criteria=g_eval_criteria
        )
        
        # Step 3: 指令验证
        instruction_validator = InstructionValidationStep(
            name="instruction_validator",
            validator_llm=primary_llm
        )
        
        # Step 4: 高质量样本过滤
        quality_filter = KeepColumns(
            name="quality_filter",
            columns=[
                "instruction", "intent_json", "sample_type", "confidence",
                "g_eval_scores", "validation_scores", "final_quality_score"
            ],
            condition=lambda x: (
                x.get("is_high_quality", False) and 
                x.get("validation_passed", False) and
                x.get("final_quality_score", 0) >= 3.5
            )
        )
        
        # 构建Pipeline流程
        enhanced_sampler >> multi_evaluator >> instruction_validator >> quality_filter
    
    logger.info("✅ 多模型互评Pipeline创建完成")
    return pipeline


def _create_hybrid_pipeline(config: Dict[str, Any], config_path: str) -> Pipeline:
    """创建混合Pipeline（支持降级到原有系统）"""
    
    # 检查是否启用增强功能
    enhanced_enabled = config.get("enhanced_mode", {}).get("enabled", True)
    
    if enhanced_enabled:
        logger.info("启用增强模式Pipeline")
        return _create_multi_model_pipeline(config, config_path)
    else:
        logger.info("降级到传统模式Pipeline")
        return _create_legacy_pipeline(config_path)


def _create_legacy_pipeline(config_path: str) -> Pipeline:
    """创建传统Pipeline（向后兼容）"""
    
    with Pipeline(name="LegacyPipeline") as pipeline:
        # Step 1: 传统意图生成
        intent_generator = ConfigDrivenIntentGenerator(
            name="intent_generator",
            config_path=config_path.replace("sampling_config.yaml", "pipeline_config.yaml")
        )
        
        # Step 2: 基础质量控制
        quality_filter = KeepColumns(
            name="quality_filter",
            columns=["intent_json", "entity", "field_name", "data_type", "semantic_strategy"],
            condition=lambda x: len(x.get("entity", "")) > 0
        )
        
        # 构建Pipeline流程
        intent_generator >> quality_filter
    
    logger.info("✅ 传统Pipeline创建完成（向后兼容）")
    return pipeline


# ==================== Pipeline执行函数 ====================

async def run_enhanced_pipeline(config_path: str = "configs/sampling_config.yaml", 
                               output_path: str = "data/processed/enhanced_output",
                               **kwargs) -> Any:
    """运行增强采样Pipeline"""
    
    try:
        # 创建Pipeline
        logger.info("🔄 开始创建Pipeline...")
        pipeline = create_enhanced_pipeline(config_path)
        
        # 设置输出路径
        output_dir = Path(output_path)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 执行Pipeline
        logger.info("⚡ 开始执行Pipeline...")
        start_time = datetime.now()
        
        distiset = pipeline.run(
            use_cache=kwargs.get("use_cache", False),
            storage_parameters={
                "cache_dir": str(output_dir / "cache"),
                "filesystem": "local"
            }
        )
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # 保存结果
        logger.info("💾 保存Pipeline结果...")
        distiset.save_to_disk(str(output_dir))
        
        # 生成执行报告
        execution_report = {
            "pipeline_type": "enhanced_sampling",
            "config_path": config_path,
            "output_path": str(output_dir),
            "execution_time": f"{duration:.2f}s",
            "start_time": start_time.isoformat(),
            "end_time": end_time.isoformat(),
            "total_samples": len(distiset['default']) if 'default' in distiset else 0,
            "pipeline_steps": len(pipeline.dag.nodes),
            "success": True
        }
        
        # 保存执行报告
        report_path = output_dir / "execution_report.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(execution_report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"✅ Pipeline执行完成!")
        logger.info(f"   执行时间: {duration:.2f}秒")
        logger.info(f"   生成样本: {execution_report['total_samples']}个")
        logger.info(f"   输出路径: {output_dir}")
        logger.info(f"   执行报告: {report_path}")
        
        return distiset
        
    except Exception as e:
        logger.error(f"❌ Pipeline执行失败: {e}")
        
        # 保存错误报告
        error_report = {
            "pipeline_type": "enhanced_sampling",
            "config_path": config_path,
            "error": str(e),
            "timestamp": datetime.now().isoformat(),
            "success": False
        }
        
        error_path = Path(output_path) / "error_report.json"
        error_path.parent.mkdir(parents=True, exist_ok=True)
        with open(error_path, 'w', encoding='utf-8') as f:
            json.dump(error_report, f, ensure_ascii=False, indent=2)
        
        raise


def validate_pipeline_config(config_path: str) -> Tuple[bool, List[str]]:
    """验证Pipeline配置"""
    errors = []
    
    try:
        # 检查配置文件是否存在
        if not Path(config_path).exists():
            errors.append(f"配置文件不存在: {config_path}")
            return False, errors
        
        # 加载并验证配置
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 检查必需的配置节
        required_sections = [
            "base_generation",
            "paraphrase_generation", 
            "robustness_generation",
            "quality_control"
        ]
        
        for section in required_sections:
            if section not in config:
                errors.append(f"缺少必需的配置节: {section}")
        
        # 检查模型配置
        model_config = config.get("paraphrase_generation", {}).get("model_config", {})
        if not model_config.get("name"):
            errors.append("缺少模型名称配置")
        
        # 检查数值配置
        base_config = config.get("base_generation", {})
        if base_config.get("base_samples_count", 0) <= 0:
            errors.append("base_samples_count 必须大于0")
        
        paraphrase_config = config.get("paraphrase_generation", {})
        if paraphrase_config.get("paraphrase_variants", 0) <= 0:
            errors.append("paraphrase_variants 必须大于0")
        
        if not errors:
            logger.info(f"✅ 配置验证通过: {config_path}")
            return True, []
        else:
            logger.warning(f"⚠️ 配置验证失败: {len(errors)}个错误")
            return False, errors
            
    except Exception as e:
        errors.append(f"配置文件解析失败: {e}")
        return False, errors


def get_pipeline_info(config_path: str = "configs/sampling_config.yaml") -> Dict[str, Any]:
    """获取Pipeline信息"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 计算预期样本数量
        base_count = config.get("base_generation", {}).get("base_samples_count", 200)
        paraphrase_variants = config.get("paraphrase_generation", {}).get("paraphrase_variants", 4)
        adversarial_ratio = config.get("robustness_generation", {}).get("adversarial_ratio", 0.3)
        boundary_samples = config.get("robustness_generation", {}).get("boundary_samples", 50)
        negative_samples = config.get("robustness_generation", {}).get("negative_samples", 100)
        
        paraphrase_count = min(base_count, 50) * paraphrase_variants  # 限制改写基数
        adversarial_count = int(base_count * adversarial_ratio)
        
        total_estimated = base_count + paraphrase_count + adversarial_count + boundary_samples + negative_samples
        
        pipeline_info = {
            "config_path": config_path,
            "pipeline_mode": config.get("pipeline", {}).get("mode", "enhanced_sampling"),
            "estimated_samples": {
                "base_samples": base_count,
                "paraphrase_samples": paraphrase_count,
                "adversarial_samples": adversarial_count,
                "boundary_samples": boundary_samples,
                "negative_samples": negative_samples,
                "total_estimated": total_estimated
            },
            "model_config": {
                "primary_model": config.get("paraphrase_generation", {}).get("model_config", {}).get("name", "Unknown"),
                "evaluator_model": config.get("g_eval", {}).get("evaluator_model", {}).get("name", "Unknown")
            },
            "quality_thresholds": config.get("g_eval", {}).get("quality_thresholds", {}),
            "adaptive_sampling_enabled": config.get("adaptive_sampling", {}).get("enabled", True),
            "monitoring_enabled": config.get("quality_control", {}).get("enabled", True)
        }
        
        return pipeline_info
        
    except Exception as e:
        logger.error(f"获取Pipeline信息失败: {e}")
        return {"error": str(e)}


# ==================== 主要接口函数 ====================

def create_pipeline(config_path: str = "configs/sampling_config.yaml") -> Pipeline:
    """创建Pipeline的主要接口函数"""
    
    # 验证配置
    is_valid, errors = validate_pipeline_config(config_path)
    if not is_valid:
        error_msg = f"配置验证失败: {'; '.join(errors)}"
        logger.error(error_msg)
        raise ValueError(error_msg)
    
    # 显示Pipeline信息
    pipeline_info = get_pipeline_info(config_path)
    logger.info("📊 Pipeline配置信息:")
    logger.info(f"   模式: {pipeline_info.get('pipeline_mode', 'Unknown')}")
    logger.info(f"   预计总样本数: {pipeline_info.get('estimated_samples', {}).get('total_estimated', 0)}")
    logger.info(f"   主要模型: {pipeline_info.get('model_config', {}).get('primary_model', 'Unknown')}")
    
    # 创建Pipeline
    return create_enhanced_pipeline(config_path)


async def run_pipeline(config_path: str = "configs/sampling_config.yaml", 
                      output_path: str = "data/processed/enhanced_output",
                      **kwargs) -> Any:
    """运行Pipeline的主要接口函数"""
    return await run_enhanced_pipeline(config_path, output_path, **kwargs)


# ==================== 工具函数 ====================

def estimate_execution_time(config_path: str) -> Dict[str, Any]:
    """估算Pipeline执行时间"""
    try:
        pipeline_info = get_pipeline_info(config_path)
        total_samples = pipeline_info.get("estimated_samples", {}).get("total_estimated", 0)
        
        # 基于样本数量估算时间（经验值）
        base_time_per_sample = 0.1  # 秒
        model_overhead = 2.0  # 模型加载时间
        quality_control_overhead = total_samples * 0.01  # 质量控制时间
        
        estimated_time = (total_samples * base_time_per_sample + 
                         model_overhead + quality_control_overhead)
        
        return {
            "total_samples": total_samples,
            "estimated_time_seconds": estimated_time,
            "estimated_time_minutes": estimated_time / 60,
            "breakdown": {
                "sample_generation": total_samples * base_time_per_sample,
                "model_overhead": model_overhead,
                "quality_control": quality_control_overhead
            }
        }
        
    except Exception as e:
        logger.error(f"时间估算失败: {e}")
        return {"error": str(e)}


def get_supported_models() -> Dict[str, List[str]]:
    """获取支持的模型列表"""
    return {
        "transformers": [
            "Qwen/Qwen2.5-7B-Instruct",
            "Qwen/Qwen2.5-4B-Instruct", 
            "Qwen/Qwen-7B-Chat",
            "microsoft/DialoGPT-medium"
        ],
        "inference_endpoints": [
            "doubao",
            "qwen-plus",
            "qwen-max"
        ],
        "openai": [
            "gpt-3.5-turbo",
            "gpt-4",
            "gpt-4-turbo"
        ]
    }


def create_default_config(output_path: str = "configs/sampling_config_default.yaml"):
    """创建默认配置文件"""
    default_config = {
        "pipeline": {
            "mode": "enhanced_sampling"
        },
        "base_generation": {
            "base_samples_count": 200,
            "orm_config_path": "configs/orm_def.yaml",
            "enable_intent_combination": True
        },
        "paraphrase_generation": {
            "paraphrase_variants": 4,
            "model_config": {
                "name": "Qwen/Qwen2.5-7B-Instruct",
                "type": "transformers",
                "temperature": 0.7,
                "max_tokens": 150
            },
            "strategy_weights": {
                "formal": 0.25,
                "casual": 0.35,
                "technical": 0.20,
                "brief": 0.20
            },
            "max_concurrent": 5,
            "retry_attempts": 3
        },
        "robustness_generation": {
            "adversarial_ratio": 0.3,
            "boundary_samples": 50,
            "negative_samples": 100
        },
        "adaptive_sampling": {
            "enabled": True,
            "learning_rate": 0.1,
            "min_samples_for_adaptation": 50
        },
        "quality_control": {
            "enabled": True,
            "deduplication": {
                "hash_dedup": {"enabled": True},
                "semantic_dedup": {
                    "enabled": True,
                    "similarity_threshold": 0.85
                }
            },
            "validation": {
                "instruction_length": {
                    "min_length": 2,
                    "max_length": 200
                }
            }
        },
        "output": {
            "default_output_path": "data/enhanced_samples.json",
            "formats": {
                "json": {"enabled": True, "pretty_print": True},
                "jsonl": {"enabled": True}
            }
        }
    }
    
    Path(output_path).parent.mkdir(parents=True, exist_ok=True)
    with open(output_path, 'w', encoding='utf-8') as f:
        yaml.dump(default_config, f, allow_unicode=True, default_flow_style=False, indent=2)
    
    logger.info(f"✅ 默认配置已创建: {output_path}")
    return output_path


# ==================== 测试和调试函数 ====================

async def test_pipeline_components(config_path: str = "configs/sampling_config.yaml"):
    """测试Pipeline组件"""
    logger.info("🧪 开始测试Pipeline组件...")
    
    try:
        # 测试配置加载
        is_valid, errors = validate_pipeline_config(config_path)
        if not is_valid:
            logger.error(f"配置验证失败: {errors}")
            return False
        
        # 测试Pipeline创建
        pipeline = create_enhanced_pipeline(config_path)
        logger.info(f"✅ Pipeline创建成功，包含 {len(pipeline.dag.nodes)} 个步骤")
        
        # 测试小规模执行
        logger.info("🔬 执行小规模测试...")
        
        # 修改配置为测试模式
        test_config_path = "configs/test_sampling_config.yaml"
        with open(config_path, 'r', encoding='utf-8') as f:
            test_config = yaml.safe_load(f)
        
        # 减少样本数量进行测试
        test_config["base_generation"]["base_samples_count"] = 10
        test_config["paraphrase_generation"]["paraphrase_variants"] = 2
        test_config["robustness_generation"]["boundary_samples"] = 5
        test_config["robustness_generation"]["negative_samples"] = 5
        
        with open(test_config_path, 'w', encoding='utf-8') as f:
            yaml.dump(test_config, f, allow_unicode=True)
        
        # 执行测试
        test_output = "data/test_output"
        distiset = await run_enhanced_pipeline(test_config_path, test_output)
        
        # 验证结果
        if distiset and 'default' in distiset:
            sample_count = len(distiset['default'])
            logger.info(f"✅ 测试成功！生成 {sample_count} 个样本")
            
            # 显示样本示例
            if sample_count > 0:
                sample = distiset['default'][0]
                logger.info("📋 样本示例:")
                for key, value in sample.items():
                    if len(str(value)) > 100:
                        logger.info(f"   {key}: {str(value)[:100]}...")
                    else:
                        logger.info(f"   {key}: {value}")
        
        # 清理测试文件
        Path(test_config_path).unlink(missing_ok=True)
        
        logger.info("🎉 Pipeline组件测试完成！")
        return True
        
    except Exception as e:
        logger.error(f"❌ Pipeline测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    # 测试脚本
    import argparse
    
    parser = argparse.ArgumentParser(description="Enhanced Distilabel Pipeline")
    parser.add_argument("--test", action="store_true", help="运行测试")
    parser.add_argument("--create-config", action="store_true", help="创建默认配置")
    parser.add_argument("--config", default="configs/sampling_config.yaml", help="配置文件路径")
    parser.add_argument("--output", default="data/processed/enhanced_output", help="输出路径")
    
    args = parser.parse_args()
    
    if args.create_config:
        create_default_config()
    elif args.test:
        asyncio.run(test_pipeline_components(args.config))
    else:
        # 运行完整Pipeline
        asyncio.run(run_pipeline(args.config, args.output))

    def __init__(self, config_path: str = "configs/pipeline_config.yaml", **kwargs):
        super().__init__(**kwargs)
        self.config_path = config_path

    def load(self) -> None:
        """加载配置和初始化组件"""
        super().load()
        self.config = self._load_config()
        self.weighted_sampler = WeightedSampler()
        
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        with open(self.config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    @property
    def inputs(self) -> List[str]:
        return []
    
    @property 
    def outputs(self) -> List[str]:
        return ["intent_json", "entity", "field_name", "data_type", "semantic_strategy"]
    
    def process(self, offset: int = 0) -> StepOutput:
        """生成结构化意图数据（保留原有逻辑）"""
        generation_config = self.config.get("enhanced_mode", {})
        samples_per_intent = generation_config.get("seed_samples_per_intent", 100)
        
        intent_types = ["ADD_COLUMN", "DELETE_COLUMN", "MODIFY_COLUMN", "ENABLE_SOFT_DELETE"]
        generated_data = []
        
        for intent_type in intent_types:
            entity_weights = config_manager.get_crud_weights(f"base_object_hierarchy.entity.name")
            entities = config_manager.get_orm_entities()
            
            for _ in range(samples_per_intent):
                entity = self.weighted_sampler.weighted_choice(entities, [1.0] * len(entities))
                field_name = self.weighted_sampler.weighted_choice(
                    ["等级", "状态", "类型", "编号", "名称"], [1.0] * 5
                )
                data_type = self.weighted_sampler.weighted_choice(
                    config_manager.get_orm_field_types(), [1.0] * len(config_manager.get_orm_field_types())
                )
                
                semantic_strategies = {"atomic": 35, "composite": 30, "sequence": 20, "implicit": 15}
                semantic_strategy = self.weighted_sampler.weighted_choice_dict(semantic_strategies)
                
                intent_json = self._build_intent_json(intent_type, entity, field_name, data_type)
                
                generated_data.append({
                    "intent_json": json.dumps(intent_json, ensure_ascii=False),
                    "entity": entity,
                    "field_name": field_name, 
                    "data_type": data_type,
                    "semantic_strategy": semantic_strategy
                })
        
        yield generated_data
    
    def _build_intent_json(self, intent_type: str, entity: str, field_name: str, data_type: str) -> Dict[str, Any]:
        """构建标准意图JSON结构"""
        base_intent = {
            "intentType": intent_type,
            "targetConceptName": entity,
            "props": {}
        }
        
        if intent_type == "ADD_COLUMN":
            base_intent["props"] = {
                "name": field_name,
                "stdSqlType": data_type,
                "nullable": True,
                "comment": f"{field_name}字段"
            }
        elif intent_type == "DELETE_COLUMN":
            base_intent["props"] = {"name": field_name}
        elif intent_type == "MODIFY_COLUMN":
            base_intent["props"] = {
                "name": field_name,
                "stdSqlType": data_type,
                "nullable": False
            }
        elif intent_type == "ENABLE_SOFT_DELETE":
            base_intent["props"] = {
                "deleteField": "is_deleted",
                "deleteFieldType": "BOOLEAN"
            }
        
        return base_intent


# ==================== 新增的增强采样Step ====================

class EnhancedSamplingStep(GeneratorStep):
    """增强采样步骤 - 集成六维采样系统"""
    
    config_path: str = "configs/sampling_config.yaml"
    model_config = {"extra": "allow"}
    
    def __init__(self, config_path: str = "configs/sampling_config.yaml", **kwargs):
        super().__init__(**kwargs)
        self.config_path = config_path
        self.enhanced_adapter = None
        
    def load(self) -> None:
        """加载配置和初始化增强采样系统"""
        super().load()
        self.config = self._load_sampling_config()
        
        # 初始化增强采样适配器
        self.enhanced_adapter = EnhancedSamplingAdapter()
        
        # 根据配置启用功能
        adaptive_config = self.config.get("adaptive_sampling", {})
        self.enhanced_adapter.set_feature_enabled(
            "adaptive_sampling", adaptive_config.get("enabled", True)
        )
        
        monitoring_config = self.config.get("quality_control", {})
        self.enhanced_adapter.set_feature_enabled(
            "distribution_monitoring", monitoring_config.get("enabled", True)
        )
        
        logger.info("增强采样步骤初始化完成")
        
    def _load_sampling_config(self) -> Dict[str, Any]:
        """加载采样配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.warning(f"加载采样配置失败: {e}，使用默认配置")
            return self._get_default_sampling_config()
    
    def _get_default_sampling_config(self) -> Dict[str, Any]:
        """获取默认采样配置"""
        return {
            "base_generation": {"base_samples_count": 200},
            "paraphrase_generation": {"paraphrase_variants": 4},
            "robustness_generation": {
                "adversarial_ratio": 0.3,
                "boundary_samples": 50,
                "negative_samples": 100
            },
            "adaptive_sampling": {"enabled": True},
            "quality_control": {"enabled": True}
        }
    
    @property
    def inputs(self) -> List[str]:
        return []
    
    @property
    def outputs(self) -> List[str]:
        return [
            "intent_json", "instruction", "sample_type", "confidence", 
            "metadata", "hash_id", "quality_score"
        ]
    
    def process(self, offset: int = 0) -> StepOutput:
        """执行增强采样生成"""
        logger.info("开始执行增强采样生成...")
        
        # 第一步：生成基础样本
        base_samples = self._generate_base_samples()
        logger.info(f"生成基础样本: {len(base_samples)}")
        
        # 第二步：生成多样化样本
        paraphrase_samples = self._generate_paraphrase_samples(base_samples)
        logger.info(f"生成改写样本: {len(paraphrase_samples)}")
        
        # 第三步：生成鲁棒性样本
        robustness_samples = self._generate_robustness_samples(base_samples)
        logger.info(f"生成鲁棒性样本: {len(robustness_samples)}")
        
        # 第四步：合并并质量控制
        all_samples = base_samples + paraphrase_samples + robustness_samples
        filtered_samples = self._apply_quality_control(all_samples)
        logger.info(f"质量控制后样本: {len(filtered_samples)}")
        
        # 转换为distilabel格式
        output_data = []
        for sample in filtered_samples:
            output_data.append({
                "intent_json": json.dumps(sample["intent_json"], ensure_ascii=False),
                "instruction": sample["instruction"],
                "sample_type": sample["sample_type"],
                "confidence": sample["confidence"],
                "metadata": json.dumps(sample["metadata"], ensure_ascii=False),
                "hash_id": sample["hash_id"],
                "quality_score": sample.get("quality_score", 1.0)
            })
        
        yield output_data
    
    def _generate_base_samples(self) -> List[Dict[str, Any]]:
        """生成基础样本"""
        base_config = self.config.get("base_generation", {})
        count = base_config.get("base_samples_count", 200)
        
        # 从ORM配置生成意图模板
        orm_entities = ["客户", "订单", "产品", "用户", "供应商"]
        field_types = ["VARCHAR(50)", "INT", "DECIMAL(10,2)", "DATETIME", "BOOLEAN"]
        operations = ["ADD_COLUMN", "DELETE_COLUMN", "MODIFY_COLUMN"]
        
        samples = []
        for i in range(count):
            entity = self.enhanced_adapter.enhanced_weighted_choice(
                orm_entities, [1.0] * len(orm_entities), "entity_selection"
            )
            operation = self.enhanced_adapter.enhanced_weighted_choice(
                operations, [0.4, 0.3, 0.3], "operation_selection"  
            )
            field_type = self.enhanced_adapter.enhanced_weighted_choice(
                field_types, [1.0] * len(field_types), "type_selection"
            )
            
            # 生成意图JSON
            intent_json = {
                "intentType": operation,
                "targetConceptName": entity,
                "props": {
                    "name": f"字段_{i}",
                    "stdSqlType": field_type
                }
            }
            
            # 生成基础指令
            instruction = self._generate_base_instruction(intent_json)
            
            sample = {
                "intent_json": intent_json,
                "instruction": instruction,
                "sample_type": "base",
                "confidence": 1.0,
                "metadata": {
                    "generation_method": "intent_driven",
                    "entity": entity,
                    "operation": operation,
                    "index": i
                },
                "hash_id": self._generate_hash(instruction, intent_json)
            }
            samples.append(sample)
        
        return samples
    
    def _generate_base_instruction(self, intent_json: Dict[str, Any]) -> str:
        """生成基础指令"""
        intent_type = intent_json.get("intentType", "")
        entity = intent_json.get("targetConceptName", "")
        field_name = intent_json.get("props", {}).get("name", "字段")
        
        templates = {
            "ADD_COLUMN": [
                f"给{entity}表添加一个{field_name}字段",
                f"为{entity}增加{field_name}属性",
                f"{entity}需要一个{field_name}字段"
            ],
            "DELETE_COLUMN": [
                f"删除{entity}表的{field_name}字段",
                f"移除{entity}的{field_name}属性",
                f"去掉{entity}表中的{field_name}"
            ],
            "MODIFY_COLUMN": [
                f"修改{entity}表的{field_name}字段",
                f"更新{entity}的{field_name}属性",
                f"调整{entity}表{field_name}字段"
            ]
        }
        
        template_list = templates.get(intent_type, [f"对{entity}执行{intent_type}操作"])
        return self.enhanced_adapter.enhanced_weighted_choice(
            template_list, [1.0] * len(template_list), f"{intent_type}_template"
        )
    
    def _generate_paraphrase_samples(self, base_samples: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """生成改写样本"""
        paraphrase_config = self.config.get("paraphrase_generation", {})
        variants = paraphrase_config.get("paraphrase_variants", 4)
        
        paraphrase_samples = []
        strategies = ["formal", "casual", "technical", "brief"]
        
        for base_sample in base_samples[:50]:  # 限制基础样本数量
            for strategy in strategies:
                for variant_id in range(variants // len(strategies)):
                    # 模拟改写逻辑
                    paraphrased = self._apply_paraphrase_strategy(
                        base_sample["instruction"], strategy
                    )
                    
                    sample = {
                        "intent_json": base_sample["intent_json"].copy(),
                        "instruction": paraphrased,
                        "sample_type": "paraphrase",
                        "confidence": 0.9,
                        "metadata": {
                            "base_sample_id": base_sample["hash_id"],
                            "paraphrase_strategy": strategy,
                            "variant_id": variant_id
                        },
                        "hash_id": self._generate_hash(paraphrased, base_sample["intent_json"])
                    }
                    paraphrase_samples.append(sample)
        
        return paraphrase_samples
    
    def _apply_paraphrase_strategy(self, instruction: str, strategy: str) -> str:
        """应用改写策略"""
        if strategy == "formal":
            return f"请为{instruction.replace('给', '').replace('加', '添加')}"
        elif strategy == "casual":
            return f"帮忙{instruction.replace('添加', '加').replace('请', '')}"
        elif strategy == "technical":
            return instruction.replace("字段", "属性").replace("表", "实体")
        else:  # brief
            return instruction.replace("请", "").replace("一个", "").strip()
    
    def _generate_robustness_samples(self, base_samples: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """生成鲁棒性样本"""
        robustness_config = self.config.get("robustness_generation", {})
        adversarial_ratio = robustness_config.get("adversarial_ratio", 0.3)
        boundary_samples = robustness_config.get("boundary_samples", 50)
        negative_samples = robustness_config.get("negative_samples", 100)
        
        robustness_samples = []
        
        # 生成对抗样本
        adversarial_count = int(len(base_samples) * adversarial_ratio)
        for i in range(adversarial_count):
            base_sample = base_samples[i % len(base_samples)]
            
            # 应用扰动
            perturbed_instruction = self._apply_perturbation(base_sample["instruction"])
            
            sample = {
                "intent_json": base_sample["intent_json"].copy(),
                "instruction": perturbed_instruction,
                "sample_type": "adversarial",
                "confidence": 0.7,
                "metadata": {
                    "base_sample_id": base_sample["hash_id"],
                    "perturbation_type": "entity_substitution"
                },
                "hash_id": self._generate_hash(perturbed_instruction, base_sample["intent_json"])
            }
            robustness_samples.append(sample)
        
        # 生成边界样本
        boundary_patterns = ["加字段", "删除那个", "修改一下", "客户需要"]
        for i in range(boundary_samples):
            pattern = boundary_patterns[i % len(boundary_patterns)]
            
            sample = {
                "intent_json": {},
                "instruction": pattern,
                "sample_type": "boundary",
                "confidence": 0.3,
                "metadata": {"boundary_type": "incomplete"},
                "hash_id": self._generate_hash(pattern, {})
            }
            robustness_samples.append(sample)
        
        # 生成负样本
        negative_patterns = [
            "今天天气怎么样", "请帮我订机票", "发送邮件给客户",
            "生成月度报表", "备份数据库", "清理缓存文件"
        ]
        for i in range(negative_samples):
            pattern = negative_patterns[i % len(negative_patterns)]
            
            sample = {
                "intent_json": {},
                "instruction": pattern,
                "sample_type": "negative",
                "confidence": 0.0,
                "metadata": {"negative_type": "irrelevant"},
                "hash_id": self._generate_hash(pattern, {})
            }
            robustness_samples.append(sample)
        
        return robustness_samples
    
    def _apply_perturbation(self, instruction: str) -> str:
        """应用扰动策略"""
        # 实体替换
        replacements = {
            "客户": "客人",
            "订单": "订购",
            "产品": "商品",
            "字段": "属性",
            "表": "数据表"
        }
        
        perturbed = instruction
        for original, replacement in replacements.items():
            if original in perturbed:
                perturbed = perturbed.replace(original, replacement)
                break
        
        return perturbed
    
    def _apply_quality_control(self, samples: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """应用质量控制"""
        # 去重
        seen_hashes = set()
        unique_samples = []
        
        for sample in samples:
            if sample["hash_id"] not in seen_hashes:
                seen_hashes.add(sample["hash_id"])
                unique_samples.append(sample)
        
        # 质量评分
        for sample in unique_samples:
            sample["quality_score"] = self._calculate_quality_score(sample)
        
        # 过滤低质量样本
        filtered_samples = [
            sample for sample in unique_samples 
            if sample["quality_score"] >= 0.5
        ]
        
        return filtered_samples
    
    def _calculate_quality_score(self, sample: Dict[str, Any]) -> float:
        """计算质量分数"""
        base_score = sample["confidence"]
        
        # 根据样本类型调整
        sample_type = sample["sample_type"]
        if sample_type == "base":
            return base_score
        elif sample_type == "paraphrase":
            return base_score * 0.9
        elif sample_type == "adversarial":
            return base_score * 0.8
        elif sample_type == "boundary":
            return base_score * 0.6
        else:  # negative
            return base_score
    
    def _generate_hash(self, instruction: str, intent_json: Dict[str, Any]) -> str:
        """生成样本哈希ID"""
        content = f"{instruction}_{json.dumps(intent_json, sort_keys=True)}"
        return hashlib.md5(content.encode()).hexdigest()


# ==================== 多模型互评Step ====================

class MultiModelEvaluationStep(Step):
    """多模型互评步骤 - 使用豆包进行G-Eval评估"""

    evaluator_llm: Any
    criteria: List[Dict[str, Any]]
    evaluation_template: str = """你是一个专业的数据质量评估专家。请根据以下标准对自然语言指令和JSON意图的匹配质量进行评分。

原始JSON意图：
{intent_json}

生成的自然语言指令：
{instruction}

评估标准：
{criteria_str}

请为每个维度提供1-5分的评分（5分最高），并简要说明理由。

输出格式（严格按照JSON格式）：
{{
  "semantic_consistency": {{
    "score": 分数,
    "reason": "评分理由"
  }},
  "expression_naturalness": {{
    "score": 分数, 
    "reason": "评分理由"
  }},
  "ambiguity_level": {{
    "score": 分数,
    "reason": "评分理由"
  }},
  "overall_quality": {{
    "score": 分数,
    "reason": "综合评价"
  }}
}}"""

    def __init__(self, evaluator_llm: Any, criteria: List[Dict[str, Any]], **kwargs):
        super().__init__(evaluator_llm=evaluator_llm, criteria=criteria, **kwargs)

    @property
    def inputs(self) -> List[str]:
        return ["instruction", "intent_json", "sample_type", "confidence"]
    
    @property
    def outputs(self) -> List[str]:
        return [
            "instruction", "intent_json", "sample_type", "confidence",
            "g_eval_scores", "g_eval_reasons", "overall_score", "is_high_quality"
        ]
    
    def process(self, inputs: StepInput) -> StepOutput:
        """执行多模型评估"""
        # 格式化评估标准
        criteria_str = "\n".join([
            f"• {c['name']}: {c.get('description', '')}" 
            for c in self.criteria
        ])
        
        processed_data = []
        
        for input_batch in inputs:
            for item in input_batch:
                try:
                    # 构建评估提示
                    prompt = self.evaluation_template.format(
                        instruction=item["instruction"],
                        intent_json=item["intent_json"], 
                        criteria_str=criteria_str
                    )
                    
                    # 调用评估模型（模拟）
                    evaluation_result = self._mock_evaluation(item)
                    
                    # 处理结果
                    item.update({
                        "g_eval_scores": evaluation_result["scores"],
                        "g_eval_reasons": evaluation_result["reasons"],
                        "overall_score": evaluation_result["overall_score"],
                        "is_high_quality": evaluation_result["overall_score"] >= 3.5
                    })
                    
                    processed_data.append(item)
                    
                except Exception as e:
                    logger.warning(f"评估失败: {e}")
                    # 添加默认评估结果
                    item.update({
                        "g_eval_scores": {"semantic_consistency": 3.0},
                        "g_eval_reasons": {"semantic_consistency": "评估失败"},
                        "overall_score": 3.0,
                        "is_high_quality": False
                    })
                    processed_data.append(item)
        
        yield processed_data
    
    def _mock_evaluation(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """模拟评估结果（实际应该调用真实的LLM）"""
        # 基于样本类型给出不同的评分
        sample_type = item.get("sample_type", "base")
        base_confidence = item.get("confidence", 1.0)
        
        if sample_type == "base":
            scores = {
                "semantic_consistency": min(5.0, base_confidence * 5.0),
                "expression_naturalness": min(5.0, base_confidence * 4.8),
                "ambiguity_level": min(5.0, base_confidence * 4.5)
            }
        elif sample_type == "paraphrase":
            scores = {
                "semantic_consistency": min(5.0, base_confidence * 4.5),
                "expression_naturalness": min(5.0, base_confidence * 5.0),
                "ambiguity_level": min(5.0, base_confidence * 4.2)
            }
        elif sample_type == "adversarial":
            scores = {
                "semantic_consistency": min(5.0, base_confidence * 3.5),
                "expression_naturalness": min(5.0, base_confidence * 3.8),
                "ambiguity_level": min(5.0, base_confidence * 3.0)
            }
        else:  # boundary, negative
            scores = {
                "semantic_consistency": min(5.0, base_confidence * 2.5),
                "expression_naturalness": min(5.0, base_confidence * 2.8),
                "ambiguity_level": min(5.0, base_confidence * 2.0)
            }
        
        overall_score = sum(scores.values()) / len(scores)
        
        return {
            "scores": scores,
            "reasons": {k: f"基于{sample_type}类型的评估" for k in scores.keys()},
            "overall_score": overall_score
        }


class InstructionValidationStep(Step):
    """指令验证步骤 - 正向验证生成的指令"""

    validator_llm: Any
    validation_template: str = """请验证以下自然语言指令是否能正确解析为给定的JSON意图。

自然语言指令：{instruction}

期望的JSON意图：{intent_json}

请回答：
1. 这个指令是否清晰表达了意图？(1-5分)
2. 指令中的信息是否与JSON意图一致？(1-5分)  
3. 指令的语言是否自然流畅？(1-5分)

请以JSON格式输出结果：
{{
  "clarity_score": 分数,
  "consistency_score": 分数,
  "fluency_score": 分数,
  "validation_passed": true/false,
  "issues": "发现的问题（如果有）"
}}"""

    def __init__(self, validator_llm: Any, **kwargs):
        super().__init__(validator_llm=validator_llm, **kwargs)

    @property
    def inputs(self) -> List[str]:
        return ["instruction", "intent_json", "sample_type", "g_eval_scores"]
    
    @property
    def outputs(self) -> List[str]:
        return [
            "instruction", "intent_json", "sample_type", "g_eval_scores",
            "validation_scores", "validation_passed", "final_quality_score"
        ]
    
    def process(self, inputs: StepInput) -> StepOutput:
        """执行指令验证"""
        processed_data = []
        
        for input_batch in inputs:
            for item in input_batch:
                try:
                    # 模拟验证逻辑
                    validation_result = self._mock_validation(item)
                    
                    # 计算最终质量分数
                    g_eval_score = item.get("overall_score", 3.0)
                    validation_score = validation_result["average_score"]
                    final_score = (g_eval_score + validation_score) / 2
                    
                    item.update({
                        "validation_scores": validation_result["scores"],
                        "validation_passed": validation_result["passed"],
                        "final_quality_score": final_score
                    })
                    
                    processed_data.append(item)
                    
                except Exception as e:
                    logger.warning(f"验证失败: {e}")
                    item.update({
                        "validation_scores": {"clarity_score": 3.0},
                        "validation_passed": False,
                        "final_quality_score": 2.0
                    })
                    processed_data.append(item)
        
        yield processed_data
    
    def _mock_validation(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """模拟验证结果"""
        sample_type = item.get("sample_type", "base")
        
        # 基于样本类型给出验证分数
        if sample_type == "base":
            scores = {"clarity_score": 4.5, "consistency_score": 4.8, "fluency_score": 4.2}
        elif sample_type == "paraphrase":
            scores = {"clarity_score": 4.2, "consistency_score": 4.5, "fluency_score": 4.8}
        elif sample_type == "adversarial":
            scores = {"clarity_score": 3.2, "consistency_score": 3.5, "fluency_score": 3.8}
        else:
            scores = {"clarity_score": 2.5, "consistency_score": 2.8, "fluency_score": 2.2}
        
        average_score = sum(scores.values()) / len(scores)
        passed = average_score >= 3.5
        
        return {
            "scores": scores,
            "average_score": average_score,
            "passed": passed
        }


#