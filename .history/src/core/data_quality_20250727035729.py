"""
数据质量控制器：去重、质量评估、数据验证
"""

import json
import hashlib
from typing import List, Dict, Set, Tuple, Any, Optional
from loguru import logger

# 尝试导入可选依赖，如果失败则使用简化版本
try:
    import pandas as pd
    import numpy as np
    PANDAS_AVAILABLE = True
except ImportError:
    logger.warning("pandas/numpy 未安装，将使用简化版本")
    pd = None
    np = None
    PANDAS_AVAILABLE = False

try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    logger.warning("sentence_transformers 未安装，将使用简化的质量控制")
    SentenceTransformer = None
    SENTENCE_TRANSFORMERS_AVAILABLE = False

try:
    from sklearn.metrics.pairwise import cosine_similarity
    from sklearn.feature_extraction.text import TfidfVectorizer
    SKLEARN_AVAILABLE = True
except ImportError:
    logger.warning("scikit-learn 未安装，将使用简化的去重功能")
    cosine_similarity = None
    TfidfVectorizer = None
    SKLEARN_AVAILABLE = False

from src.models.intent_models import TrainingSample, DataQualityMetrics

class DataQualityController:
    """数据质量控制器"""
    
    def __init__(self):
        self.sentence_model = None
        self.tfidf_vectorizer = None
        self.similarity_threshold = 0.85
        self.min_instruction_length = 3
        self.max_instruction_length = 200
        
        # 标记是否使用高级功能
        self.use_advanced_dedup = SENTENCE_TRANSFORMERS_AVAILABLE and SKLEARN_AVAILABLE
        
    async def initialize(self):
        """初始化模型"""
        logger.info("正在初始化数据质量控制器...")
        
        try:
            if SENTENCE_TRANSFORMERS_AVAILABLE:
                # 使用更轻量的模型，避免下载问题
                try:
                    self.sentence_model = SentenceTransformer('all-MiniLM-L6-v2')
                    logger.info("句子嵌入模型加载完成")
                except Exception as e:
                    logger.warning(f"句子嵌入模型加载失败: {e}")
                    self.sentence_model = None
            
            if SKLEARN_AVAILABLE:
                # 初始化TF-IDF向量化器
                self.tfidf_vectorizer = TfidfVectorizer(
                    ngram_range=(1, 2),
                    max_features=5000,
                    stop_words=None
                )
                logger.info("TF-IDF向量化器初始化完成")
            
            logger.info(f"数据质量控制器初始化完成（高级功能: {self.use_advanced_dedup}）")
            
        except Exception as e:
            logger.warning(f"数据质量控制器初始化部分失败: {e}")
            # 降级为基础版本
            self.use_advanced_dedup = False
    
    def deduplicate_samples(self, samples: List[TrainingSample]) -> List[TrainingSample]:
        """多层次去重处理"""
        logger.info(f"开始去重处理，原始样本数: {len(samples)}")
        
        if not samples:
            return samples
        
        # 1. 精确匹配去重
        unique_samples = self._exact_deduplication(samples)
        logger.info(f"精确匹配去重后样本数: {len(unique_samples)}")
        
        if self.use_advanced_dedup:
            # 2. TF-IDF相似度去重
            if self.tfidf_vectorizer is not None:
                unique_samples = self._tfidf_deduplication(unique_samples)
                logger.info(f"TF-IDF去重后样本数: {len(unique_samples)}")
            
            # 3. 语义相似度去重
            if self.sentence_model is not None:
                unique_samples = self._semantic_deduplication(unique_samples)
                logger.info(f"语义去重后样本数: {len(unique_samples)}")
        else:
            logger.info("跳过高级去重功能（依赖库未安装）")
        
        # 4. 意图结构去重
        unique_samples = self._intent_structure_deduplication(unique_samples)
        logger.info(f"意图结构去重后样本数: {len(unique_samples)}")
        
        logger.info(f"最终去重后样本数: {len(unique_samples)}")
        return unique_samples
    
    def _exact_deduplication(self, samples: List[TrainingSample]) -> List[TrainingSample]:
        """精确匹配去重"""
        seen_instructions = set()
        unique_samples = []
        
        for sample in samples:
            instruction_hash = hashlib.md5(sample.instruction.encode()).hexdigest()
            if instruction_hash not in seen_instructions:
                seen_instructions.add(instruction_hash)
                unique_samples.append(sample)
        
        return unique_samples
    
    def _tfidf_deduplication(self, samples: List[TrainingSample]) -> List[TrainingSample]:
        """基于TF-IDF的去重"""
        if len(samples) <= 1 or not SKLEARN_AVAILABLE:
            return samples
        
        instructions = [sample.instruction for sample in samples]
        
        try:
            # 计算TF-IDF向量
            tfidf_matrix = self.tfidf_vectorizer.fit_transform(instructions)
            
            # 计算相似度矩阵
            similarity_matrix = cosine_similarity(tfidf_matrix)
            
            # 贪心去重
            to_keep = []
            kept_indices = set()
            
            for i, sample in enumerate(samples):
                if i in kept_indices:
                    continue
                
                should_keep = True
                for kept_idx in to_keep:
                    if similarity_matrix[i][kept_idx] > self.similarity_threshold:
                        should_keep = False
                        break
                
                if should_keep:
                    to_keep.append(i)
                    kept_indices.add(i)
            
            return [samples[i] for i in to_keep]
            
        except Exception as e:
            logger.warning(f"TF-IDF去重失败: {e}")
            return samples
    
    def _semantic_deduplication(self, samples: List[TrainingSample]) -> List[TrainingSample]:
        """基于语义相似度的去重"""
        if len(samples) <= 1 or not SENTENCE_TRANSFORMERS_AVAILABLE or self.sentence_model is None:
            return samples
        
        try:
            instructions = [sample.instruction for sample in samples]
            embeddings = self.sentence_model.encode(instructions, batch_size=32)
            
            # 计算相似度矩阵
            similarity_matrix = cosine_similarity(embeddings)
            
            # 贪心去重
            to_keep = []
            kept_indices = set()
            
            for i, sample in enumerate(samples):
                if i in kept_indices:
                    continue
                
                should_keep = True
                for kept_idx in to_keep:
                    if similarity_matrix[i][kept_idx] > self.similarity_threshold:
                        should_keep = False
                        break
                
                if should_keep:
                    to_keep.append(i)
                    kept_indices.add(i)
            
            return [samples[i] for i in to_keep]
            
        except Exception as e:
            logger.warning(f"语义去重失败: {e}")
            return samples
    
    def _intent_structure_deduplication(self, samples: List[TrainingSample]) -> List[TrainingSample]:
        """基于意图结构的去重"""
        seen_structures = set()
        unique_samples = []
        
        for sample in samples:
            # 将意图结构序列化为字符串进行比较
            structure_key = self._serialize_intent_structure(sample.output)
            
            if structure_key not in seen_structures:
                seen_structures.add(structure_key)
                unique_samples.append(sample)
        
        return unique_samples
    
    def _serialize_intent_structure(self, intents: List) -> str:
        """序列化意图结构用于比较"""
        try:
            # 提取关键信息用于比较
            structure_data = []
            for intent in intents:
                if hasattr(intent, 'dict'):
                    intent_dict = intent.dict()
                elif hasattr(intent, '__dict__'):
                    intent_dict = intent.__dict__
                else:
                    intent_dict = {}
                
                key_info = {
                    'intentType': intent_dict.get('intentType'),
                    'targetConceptName': intent_dict.get('targetConceptName'),
                    'props_keys': sorted(intent_dict.get('props', {}).keys()) if isinstance(intent_dict.get('props'), dict) else []
                }
                structure_data.append(key_info)
            
            return json.dumps(structure_data, sort_keys=True)
        except Exception as e:
            logger.warning(f"序列化意图结构失败: {e}")
            return str(hash(str(intents)))
    
    def validate_samples(self, samples: List[TrainingSample]) -> Tuple[List[TrainingSample], List[str]]:
        """验证样本质量"""
        valid_samples = []
        validation_errors = []
        
        logger.info(f"开始验证样本质量，样本数: {len(samples)}")
        
        for i, sample in enumerate(samples):
            errors = []
            
            # 1. 指令长度检查
            if len(sample.instruction) < self.min_instruction_length:
                errors.append(f"指令过短: '{sample.instruction}'")
            elif len(sample.instruction) > self.max_instruction_length:
                errors.append(f"指令过长: {len(sample.instruction)} 字符")
            
            # 2. 指令内容检查
            if not sample.instruction.strip():
                errors.append("指令为空")
            elif sample.instruction.isdigit():
                errors.append("指令只包含数字")
            
            # 3. 意图结构检查
            if not sample.output and sample.metadata.get("intent_type") not in ["CLARIFICATION_NEEDED", "IRRELEVANT"]:
                errors.append("缺少有效意图")
            
            # 4. 字符检查
            if any(char in sample.instruction for char in ['<', '>', '{', '}', '[', ']']):
                errors.append("包含特殊字符")
            
            if errors:
                validation_errors.extend([f"样本 {i}: {error}" for error in errors])
            else:
                valid_samples.append(sample)
        
        logger.info(f"验证完成，有效样本数: {len(valid_samples)}, 错误数: {len(validation_errors)}")
        return valid_samples, validation_errors
    
    def assess_quality(self, samples: List[TrainingSample]) -> DataQualityMetrics:
        """评估数据质量"""
        logger.info(f"开始评估数据质量，样本数: {len(samples)}")
        
        if not samples:
            return DataQualityMetrics(
                total_samples=0,
                avg_instruction_length=0.0,
                intent_distribution_entropy=0.0,
                expression_diversity=0.0,
                duplicate_rate=0.0
            )
        
        # 1. 基础统计
        total_samples = len(samples)
        instruction_lengths = [len(s.instruction) for s in samples]
        avg_instruction_length = sum(instruction_lengths) / len(instruction_lengths)
        
        # 2. 意图类型分布
        intent_distribution = self._calculate_intent_distribution(samples)
        intent_entropy = self._calculate_entropy(list(intent_distribution.values()))
        
        # 3. 表达多样性
        expression_diversity = 0.0
        if self.use_advanced_dedup and self.sentence_model is not None and SKLEARN_AVAILABLE:
            expression_diversity = self._calculate_expression_diversity(samples)
        else:
            # 简化版本：基于字符串长度变化
            expression_diversity = self._calculate_simple_diversity(samples)
        
        # 4. 重复率估算
        duplicate_rate = self._estimate_duplicate_rate(samples)
        
        metrics = DataQualityMetrics(
            total_samples=total_samples,
            avg_instruction_length=float(avg_instruction_length),
            intent_distribution_entropy=intent_entropy,
            expression_diversity=expression_diversity,
            duplicate_rate=duplicate_rate
        )
        
        logger.info(f"数据质量评估完成: {metrics}")
        return metrics
    
    def _calculate_intent_distribution(self, samples: List[TrainingSample]) -> Dict[str, int]:
        """计算意图类型分布"""
        distribution = {}
        
        for sample in samples:
            intent_type = sample.metadata.get("intent_type", "UNKNOWN")
            distribution[intent_type] = distribution.get(intent_type, 0) + 1
        
        return distribution
    
    def _calculate_entropy(self, values: List[int]) -> float:
        """计算分布熵"""
        if not values or sum(values) == 0:
            return 0.0
        
        total = sum(values)
        probs = [v / total for v in values if v > 0]
        
        if PANDAS_AVAILABLE and np is not None:
            entropy = -sum(p * np.log2(p) for p in probs)
        else:
            # 使用数学库的简化实现
            import math
            entropy = -sum(p * math.log2(p) for p in probs)
        
        return float(entropy)
    
    def _calculate_expression_diversity(self, samples: List[TrainingSample]) -> float:
        """计算表达多样性（高级版本）"""
        try:
            # 采样计算，避免内存问题
            sample_size = min(1000, len(samples))
            if len(samples) > sample_size:
                import random
                sampled = random.sample(samples, sample_size)
            else:
                sampled = samples
            
            instructions = [s.instruction for s in sampled]
            embeddings = self.sentence_model.encode(instructions, batch_size=32)
            
            # 计算平均相似度
            similarity_matrix = cosine_similarity(embeddings)
            
            # 排除对角线元素
            if PANDAS_AVAILABLE and np is not None:
                mask = np.ones(similarity_matrix.shape, dtype=bool)
                np.fill_diagonal(mask, False)
                avg_similarity = np.mean(similarity_matrix[mask])
            else:
                # 简化计算
                total_similarity = 0
                count = 0
                for i in range(len(similarity_matrix)):
                    for j in range(len(similarity_matrix)):
                        if i != j:
                            total_similarity += similarity_matrix[i][j]
                            count += 1
                avg_similarity = total_similarity / count if count > 0 else 0
            
            diversity = 1 - avg_similarity
            
            return max(0.0, min(1.0, float(diversity)))
            
        except Exception as e:
            logger.warning(f"计算表达多样性失败: {e}")
            return self._calculate_simple_diversity(samples)
    
    def _calculate_simple_diversity(self, samples: List[TrainingSample]) -> float:
        """计算表达多样性（简化版本）"""
        if not samples:
            return 0.0
        
        try:
            # 基于指令长度的变异系数
            lengths = [len(s.instruction) for s in samples]
            if len(set(lengths)) == 1:  # 所有长度相同
                return 0.0
            
            mean_length = sum(lengths) / len(lengths)
            
            # 计算标准差
            variance = sum((x - mean_length) ** 2 for x in lengths) / len(lengths)
            std_length = variance ** 0.5
            
            if mean_length == 0:
                return 0.0
            
            diversity = min(1.0, std_length / mean_length)
            return float(diversity)
            
        except Exception as e:
            logger.warning(f"计算简单多样性失败: {e}")
            return 0.0
    
    def _estimate_duplicate_rate(self, samples: List[TrainingSample]) -> float:
        """估算重复率"""
        if len(samples) <= 1:
            return 0.0
        
        # 基于指令的精确匹配
        instructions = [s.instruction for s in samples]
        unique_instructions = set(instructions)
        
        duplicate_rate = 1 - (len(unique_instructions) / len(instructions))
        return float(duplicate_rate)
    
    def generate_quality_report(self, samples: List[TrainingSample], 
                              validation_errors: Optional[List[str]] = None) -> Dict[str, Any]:
        """生成质量报告"""
        metrics = self.assess_quality(samples)
        
        # 意图分布统计
        intent_distribution = self._calculate_intent_distribution(samples)
        
        # 长度分布统计
        lengths = [len(s.instruction) for s in samples]
        length_stats = {
            "min": min(lengths) if lengths else 0,
            "max": max(lengths) if lengths else 0,
            "median": sorted(lengths)[len(lengths)//2] if lengths else 0,
            "avg": sum(lengths) / len(lengths) if lengths else 0.0
        }
        
        # 计算标准差
        if lengths and len(lengths) > 1:
            mean_length = length_stats["avg"]
            variance = sum((x - mean_length) ** 2 for x in lengths) / len(lengths)
            length_stats["std"] = variance ** 0.5
        else:
            length_stats["std"] = 0.0
        
        report = {
            "quality_metrics": metrics.dict(),
            "intent_distribution": intent_distribution,
            "length_statistics": length_stats,
            "validation_errors": validation_errors or [],
            "recommendations": self._generate_recommendations(metrics, intent_distribution),
            "advanced_features_available": self.use_advanced_dedup
        }
        
        return report
    
    def _generate_recommendations(self, metrics: DataQualityMetrics, 
                                distribution: Dict[str, int]) -> List[str]:
        """生成质量改进建议"""
        recommendations = []
        
        # 样本数量建议
        if metrics.total_samples < 1000:
            recommendations.append("建议增加样本数量至1000以上")
        
        # 分布均匀性建议
        if metrics.intent_distribution_entropy < 2.0:
            recommendations.append("意图类型分布不均匀，建议平衡各类型样本数量")
        
        # 表达多样性建议
        if metrics.expression_diversity < 0.3:
            recommendations.append("表达多样性较低，建议增加更多样化的表达方式")
        
        # 重复率建议
        if metrics.duplicate_rate > 0.1:
            recommendations.append("重复率较高，建议进行更严格的去重处理")
        
        # 平均长度建议
        if metrics.avg_instruction_length < 10:
            recommendations.append("平均指令长度较短，建议增加更丰富的表达")
        elif metrics.avg_instruction_length > 100:
            recommendations.append("平均指令长度较长，建议简化表达")
        
        # 高级功能建议
        if not self.use_advanced_dedup:
            recommendations.append("建议安装 sentence-transformers 和 scikit-learn 以启用高级去重功能")
        
        return recommendations