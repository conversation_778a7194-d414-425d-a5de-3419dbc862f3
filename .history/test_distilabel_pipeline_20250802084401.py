#!/usr/bin/env python3
"""
测试distilabel pipeline的基本功能
"""

import sys
sys.path.append('.')

def test_imports():
    """测试所有导入是否正常"""
    try:
        from src.core.distilabel_pipeline import (
            ConfigDrivenIntentGenerator,
            IntentToInstructionConverter,
            CustomGEvalStep,
            MultiModelInstructionValidator,
            InstructionValidator,
            CustomDataQualityStep,
            EnhancedDataQualityStep
        )
        print("✅ 所有类导入成功!")
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_class_instantiation():
    """测试类的实例化"""
    try:
        from src.core.distilabel_pipeline import ConfigDrivenIntentGenerator
        
        # 测试ConfigDrivenIntentGenerator实例化
        generator = ConfigDrivenIntentGenerator(
            config_path="configs/pipeline_config.yaml"
        )
        print("✅ ConfigDrivenIntentGenerator 实例化成功!")

        # 加载配置
        generator.load()
        print("✅ ConfigDrivenIntentGenerator 加载成功!")

        # 检查输入输出定义
        print(f"  输入: {generator.inputs}")
        print(f"  输出: {generator.outputs}")
        
        return True
    except Exception as e:
        print(f"❌ 类实例化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_distilabel_imports():
    """测试distilabel相关导入"""
    try:
        from distilabel.pipeline import Pipeline
        from distilabel.steps import Step, GeneratorStep
        from distilabel.steps.tasks import TextGeneration
        from distilabel.models import InferenceEndpointsLLM
        
        print("✅ Distilabel 核心组件导入成功!")
        print(f"  Pipeline: {Pipeline}")
        print(f"  Step: {Step}")
        print(f"  GeneratorStep: {GeneratorStep}")
        print(f"  TextGeneration: {TextGeneration}")
        print(f"  InferenceEndpointsLLM: {InferenceEndpointsLLM}")
        
        return True
    except Exception as e:
        print(f"❌ Distilabel导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 开始测试 distilabel pipeline...")
    print("=" * 50)
    
    # 测试导入
    print("\n1. 测试导入...")
    import_success = test_imports()
    
    # 测试distilabel导入
    print("\n2. 测试Distilabel导入...")
    distilabel_success = test_distilabel_imports()
    
    # 测试类实例化
    print("\n3. 测试类实例化...")
    instantiation_success = test_class_instantiation()
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    print(f"  导入测试: {'✅ 通过' if import_success else '❌ 失败'}")
    print(f"  Distilabel导入: {'✅ 通过' if distilabel_success else '❌ 失败'}")
    print(f"  实例化测试: {'✅ 通过' if instantiation_success else '❌ 失败'}")
    
    all_success = import_success and distilabel_success and instantiation_success
    print(f"\n🎯 总体结果: {'✅ 所有测试通过!' if all_success else '❌ 部分测试失败'}")
    
    return all_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
