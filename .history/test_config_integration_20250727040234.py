#!/usr/bin/env python3
"""
配置集成测试脚本 - 验证配置驱动的数据生成系统
"""

import sys
import asyncio
import os
from pathlib import Path
import json

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_step_1_config_files():
    """Step 1: 测试配置文件是否存在和可读"""
    print("🔍 Step 1: 检查配置文件...")
    
    config_files = {
        "ORM定义": "data/templates/orm_def.yaml",
        "ORM元数据": "data/templates/orm_metadata.yaml", 
        "ORM权重": "data/templates/orm_weights.yaml",
        "意图模板": "data/templates/intent_templates.json",
        "业务术语": "data/templates/business_terms.json"
    }
    
    all_exist = True
    for name, path in config_files.items():
        if os.path.exists(path):
            print(f"  {name}: {path}")
        else:
            print(f"  {name}: {path} - 文件不存在")
            all_exist = False
    
    return all_exist

def test_step_2_config_manager():
    """Step 2: 测试配置管理器"""
    print("\n🔧 Step 2: 测试配置管理器...")
    
    try:
        from src.utils.config import config_manager
        
        # 测试基本配置加载
        print("  测试配置加载...")
        
        # 测试ORM实体获取
        entities = config_manager.get_orm_entities()
        print(f"  实体列表 ({len(entities)}个): {entities[:3]}...")
        
        # 测试字段类型获取  
        field_types = config_manager.get_orm_field_types()
        print(f"  字段类型 ({len(field_types)}个): {field_types[:3]}...")
        
        # 测试权重获取
        crud_weights = config_manager.get_crud_weights("base_object_hierarchy.entity.column.name")
        print(f"  CRUD权重: {crud_weights}")
        
        # 测试目标数量
        target_count = config_manager.get_generation_target_count()
        print(f"  目标生成数量: {target_count}")
        
        # 测试配置验证
        validation = config_manager.validate_orm_configs()
        print(f"  配置验证结果: {validation}")
        
        # 测试配置摘要
        summary = config_manager.get_config_summary()
        print(f"  配置摘要: {summary}")
        
        return True
        
    except Exception as e:
        print(f"  配置管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_step_3_weighted_sampler():
    """Step 3: 测试加权采样器"""
    print("\n⚖️ Step 3: 测试加权采样器...")
    
    try:
        from src.core.weighted_sampler import WeightedSampler
        
        # 测试基本加权选择
        choices = ["ADD_COLUMN", "DELETE_COLUMN", "MODIFY_COLUMN"]
        weights = [40.0, 25.0, 35.0]
        selected = WeightedSampler.weighted_choice(choices, weights)
        print(f"  加权选择测试: {selected}")
        
        # 测试字典加权选择
        weight_dict = {"create": 40, "read": 25, "update": 20, "delete": 15}
        operation = WeightedSampler.weighted_choice_dict(weight_dict)
        print(f"  字典加权选择: {operation}")
        
        # 测试CRUD权重分配
        intent_types = ["ADD_COLUMN", "DELETE_COLUMN", "MODIFY_COLUMN"]
        distribution = WeightedSampler.sample_by_crud_weights(intent_types, weight_dict, 1000)
        print(f"  CRUD权重分配: {distribution}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 加权采样器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_step_4_data_generator():
    """Step 4: 测试配置驱动的数据生成器"""
    print("\n🎯 Step 4: 测试配置驱动数据生成器...")
    
    try:
        from src.core.data_generator import ReverseDataGenerator
        
        # 初始化生成器
        generator = ReverseDataGenerator()
        print(f"  ✅ 生成器初始化成功")
        print(f"    - 实体数量: {len(generator.entities)}")
        print(f"    - 字段类型数量: {len(generator.field_types)}")
        print(f"    - 模板数量: {len(generator.templates)}")
        
        # 测试单个意图类型生成
        print("  🔄 测试样本生成...")
        samples = generator.generate_single_intent_samples("ADD_COLUMN", 10)
        print(f"  ✅ 生成ADD_COLUMN样本: {len(samples)}个")
        
        # 显示样本示例
        if samples:
            sample = samples[0]
            print(f"    示例指令: {sample.instruction}")
            print(f"    意图类型: {sample.output[0].intentType if sample.output else 'None'}")
            print(f"    目标实体: {sample.output[0].targetConceptName if sample.output else 'None'}")
            print(f"    语义策略: {sample.metadata.get('semantic_strategy', 'Unknown')}")
        
        # 测试模糊样本生成
        fuzzy_samples = generator.generate_fuzzy_samples(5)
        print(f"  ✅ 生成模糊样本: {len(fuzzy_samples)}个")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 数据生成器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_step_5_full_pipeline():
    """Step 5: 测试完整Pipeline"""
    print("\n🚀 Step 5: 测试完整数据生成Pipeline...")
    
    try:
        from src.core.data_pipeline import DataGenerationPipeline
        
        # 初始化Pipeline
        pipeline = DataGenerationPipeline()
        await pipeline.initialize()
        print("  ✅ Pipeline初始化成功")
        
        # 获取Pipeline统计
        stats = pipeline.get_pipeline_statistics()
        print(f"  📊 Pipeline状态: {stats.get('config_status', {})}")
        
        # 运行小规模测试
        print("  🔄 运行小规模测试...")
        result = await pipeline.run_full_pipeline(
            samples_per_intent=20,  # 小规模测试
            adversarial_ratio=0.1,
            boundary_cases=5,
            fuzzy_samples=10,
            negative_samples=5,
            use_config_targets=True  # 使用配置驱动
        )
        
        print("  ✅ Pipeline测试完成")
        print(f"    - 最终样本数: {result['generation_statistics']['final_samples']}")
        print(f"    - 执行时间: {result['execution_time_seconds']:.2f}秒")
        print(f"    - 保存文件数: {len(result['saved_files'])}")
        
        # 显示质量指标
        quality_metrics = result['quality_report']['quality_metrics']
        print(f"    - 意图分布熵: {quality_metrics['intent_distribution_entropy']:.2f}")
        print(f"    - 表达多样性: {quality_metrics['expression_diversity']:.2f}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Pipeline测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_step_6_output_validation():
    """Step 6: 验证输出文件"""
    print("\n📁 Step 6: 验证输出文件...")
    
    try:
        output_dir = "data/processed"
        if not os.path.exists(output_dir):
            print(f"  ❌ 输出目录不存在: {output_dir}")
            return False
        
        # 查找最新的输出文件
        files = os.listdir(output_dir)
        json_files = [f for f in files if f.endswith('.json')]
        
        if not json_files:
            print(f"  ❌ 输出目录中没有JSON文件")
            return False
        
        print(f"  ✅ 找到 {len(json_files)} 个输出文件")
        
        # 验证训练样本文件
        training_files = [f for f in json_files if 'training_samples' in f]
        if training_files:
            latest_file = sorted(training_files)[-1]
            file_path = os.path.join(output_dir, latest_file)
            
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            print(f"  ✅ 验证训练文件: {latest_file}")
            print(f"    - 样本数量: {len(data)}")
            
            # 检查样本结构
            if data:
                sample = data[0]
                required_fields = ['instruction', 'output', 'metadata']
                missing_fields = [field for field in required_fields if field not in sample]
                
                if missing_fields:
                    print(f"    ❌ 缺少字段: {missing_fields}")
                    return False
                else:
                    print(f"    ✅ 样本结构正确")
                    print(f"    示例指令: {sample['instruction'][:50]}...")
        
        # 验证质量报告文件
        quality_files = [f for f in json_files if 'quality_report' in f]
        if quality_files:
            latest_quality = sorted(quality_files)[-1]
            print(f"  ✅ 找到质量报告: {latest_quality}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 输出验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def run_comprehensive_test():
    """运行完整的测试套件"""
    print("🧪 开始配置集成测试...")
    print("="*50)
    
    test_results = []
    
    # 逐步测试
    test_results.append(("配置文件检查", test_step_1_config_files()))
    test_results.append(("配置管理器", test_step_2_config_manager()))
    test_results.append(("加权采样器", test_step_3_weighted_sampler()))
    test_results.append(("数据生成器", test_step_4_data_generator()))
    test_results.append(("完整Pipeline", await test_step_5_full_pipeline()))
    test_results.append(("输出验证", test_step_6_output_validation()))
    
    # 输出测试结果
    print("\n" + "="*50)
    print("📊 测试结果汇总:")
    
    passed = 0
    failed = 0
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\n总计: {passed} 通过, {failed} 失败")
    
    if failed == 0:
        print("🎉 所有测试通过！配置驱动系统集成成功！")
        return True
    else:
        print("⚠️ 部分测试失败，请检查错误信息并修复问题。")
        return False

def print_next_steps():
    """打印后续步骤建议"""
    print("\n" + "="*50)
    print("📋 后续步骤建议:")
    print("1. 如果测试全部通过，可以运行大规模数据生成:")
    print("   python scripts/data_generation/generate_training_data.py --samples-per-intent 500")
    print("\n2. 检查生成的数据质量:")
    print("   python scripts/data_generation/assess_data_quality.py data/processed/training_samples_*.json")
    print("\n3. 调整配置参数优化生成效果:")
    print("   - 修改 data/templates/orm_weights.yaml 中的权重")
    print("   - 调整 configs/pipeline_config.yaml 中的生成参数")
    print("\n4. 集成到模型训练流程:")
    print("   - 使用生成的 train_set_*.json 进行模型微调")
    print("   - 使用 val_set_*.json 进行验证")

if __name__ == "__main__":
    try:
        success = asyncio.run(run_comprehensive_test())
        print_next_steps()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n❌ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试执行失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)