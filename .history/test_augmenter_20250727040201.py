"""
测试数据增强器
"""

import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.data_generator import ReverseDataGenerator
from src.core.data_augmenter import DataAugmenter

async def test_data_augmenter():
    """测试数据增强器功能"""
    print("🧪 开始测试数据增强器...")
    
    try:
        # 1. 先生成一些基础样本
        generator = ReverseDataGenerator()
        base_samples = generator.generate_single_intent_samples("ADD_COLUMN", 20)
        print(f"生成了 {len(base_samples)} 个基础样本")
        
        # 2. 初始化增强器
        augmenter = DataAugmenter()
        
        # 3. 测试对抗样本生成
        print("\n🎯 测试对抗样本生成...")
        adversarial_samples = augmenter.generate_adversarial_samples(base_samples, ratio=0.3)
        
        print(f"生成了 {len(adversarial_samples)} 个对抗样本")
        
        # 显示几个对抗样本
        for i, sample in enumerate(adversarial_samples[:3]):
            print(f"\n对抗样本 {i+1}:")
            print(f"  原始: {sample.metadata.get('original_instruction', 'N/A')}")
            print(f"  增强: {sample.instruction}")
            print(f"  类型: {sample.metadata.get('noise_type', 'N/A')}")
        
        # 4. 测试边界案例生成
        print("\n测试边界案例生成...")
        boundary_samples = augmenter.generate_boundary_cases(base_samples, 10)
        
        print(f"生成了 {len(boundary_samples)} 个边界案例")
        
        # 显示几个边界案例
        for i, sample in enumerate(boundary_samples[:3]):
            print(f"\n边界案例 {i+1}:")
            print(f"  原始: {sample.metadata.get('original_instruction', 'N/A')}")
            print(f"  边界: {sample.instruction}")
        
        # 5. 测试负样本生成
        print("\n测试负样本生成...")
        negative_samples = augmenter.generate_negative_samples(10)
        
        print(f"生成了 {len(negative_samples)} 个负样本")
        
        # 显示几个负样本
        for i, sample in enumerate(negative_samples[:3]):
            print(f"\n负样本 {i+1}:")
            print(f"  指令: {sample.instruction}")
            print(f"  意图数量: {len(sample.output)}")
            print(f"  类型: {sample.metadata.get('intent_type', 'N/A')}")
        
        print("\n🎉 数据增强器测试完成！")
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_data_augmenter())
    sys.exit(0 if success else 1)
