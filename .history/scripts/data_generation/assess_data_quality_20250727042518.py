#!/usr/bin/env python3
"""
数据质量评估脚本
"""

import asyncio
import sys
import json
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.core.data_quality import DataQualityController
from src.models.intent_models import TrainingSample, Intent
from src.utils.logger import logger

async def load_samples_from_file(file_path: str) -> list:
    """从文件加载样本"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        samples = []
        for item in data:
            # 重构Intent对象
            intents = []
            for intent_data in item.get("output", []):
                intent = Intent(**intent_data)
                intents.append(intent)
            
            sample = TrainingSample(
                instruction=item["instruction"],
                output=intents,
                metadata=item.get("metadata", {})
            )
            samples.append(sample)
        
        logger.info(f"从 {file_path} 加载了 {len(samples)} 个样本")
        return samples
        
    except Exception as e:
        logger.error(f"加载样本文件失败: {e}")
        return []

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="评估数据质量")
    parser.add_argument("file_path", help="样本文件路径")
    parser.add_argument("--output", "-o", help="输出报告文件路径")
    parser.add_argument("--validate", action="store_true", help="执行样本验证")
    parser.add_argument("--deduplicate", action="store_true", help="执行去重处理")
    
    args = parser.parse_args()
    
    try:
        # 加载样本
        samples = await load_samples_from_file(args.file_path)
        if not samples:
            logger.error("❌ 没有加载到有效样本")
            sys.exit(1)
        
        # 初始化质量控制器
        quality_controller = DataQualityController()
        await quality_controller.initialize()
        
        # 执行质量评估
        logger.info("📈 开始质量评估...")
        
        original_count = len(samples)
        validation_errors = []
        
        # 样本验证
        if args.validate:
            logger.info("🔍 执行样本验证...")
            samples, validation_errors = quality_controller.validate_samples(samples)
            logger.info(f"✅ 验证完成，有效样本: {len(samples)}/{original_count}")
        
        # 去重处理
        if args.deduplicate:
            logger.info("🔧 执行去重处理...")
            samples = quality_controller.deduplicate_samples(samples)
            logger.info(f"✅ 去重完成，最终样本: {len(samples)}")
        
        # 生成质量报告
        quality_report = quality_controller.generate_quality_report(samples, validation_errors)
        
        # 输出报告
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(quality_report, f, ensure_ascii=False, indent=2)
            logger.info(f"📝 质量报告已保存到: {args.output}")
        else:
            print("\n" + "="*50)
            print("📊 数据质量报告")
            print("="*50)
            
            metrics = quality_report["quality_metrics"]
            print(f"总样本数: {metrics['total_samples']}")
            print(f"平均指令长度: {metrics['avg_instruction_length']:.2f}")
            print(f"意图分布熵: {metrics['intent_distribution_entropy']:.2f}")
            print(f"表达多样性: {metrics['expression_diversity']:.2f}")
            print(f"重复率: {metrics['duplicate_rate']:.2f}")
            
            print("\n📋 意图分布:")
            for intent_type, count in quality_report["intent_distribution"].items():
                print(f"  {intent_type}: {count}")
            
            print("\n💡 改进建议:")
            for recommendation in quality_report["recommendations"]:
                print(f"  • {recommendation}")
            
            if validation_errors:
                print(f"\n验证错误数: {len(validation_errors)}")
                if len(validation_errors) <= 10:
                    for error in validation_errors:
                        print(f"  • {error}")
                else:
                    for error in validation_errors[:10]:
                        print(f"  • {error}")
                    print(f"  ... 还有 {len(validation_errors) - 10} 个错误")
        
        logger.info("质量评估完成")
        
    except Exception as e:
        logger.error(f"质量评估失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())