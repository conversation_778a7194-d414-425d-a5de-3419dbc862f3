#!/usr/bin/env python3
"""
测试数据生成系统
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.core.data_pipeline import DataGenerationPipeline
from src.utils.logger import logger

async def test_data_generation():
    """测试数据生成功能"""
    logger.info("🧪 开始测试数据生成系统...")
    
    try:
        # 初始化Pipeline
        pipeline = DataGenerationPipeline()
        await pipeline.initialize()
        
        # 运行小规模测试
        logger.info("🔬 运行小规模测试...")
        result = await pipeline.run_full_pipeline(
            samples_per_intent=50,  # 每种意图50个样本
            adversarial_ratio=0.1,
            boundary_cases=20,
            fuzzy_samples=30,
            negative_samples=20
        )
        
        # 检查结果
        stats = result.get("generation_statistics", {})
        final_samples = stats.get("final_samples", 0)
        
        logger.info(".  测试结果:")
        logger.info(f"  基础样本: {stats.get('base_samples', 0)}")
        logger.info(f"  模糊样本: {stats.get('fuzzy_samples', 0)}")
        logger.info(f"  对抗样本: {stats.get('adversarial_samples', 0)}")
        logger.info(f"  边界样本: {stats.get('boundary_samples', 0)}")
        logger.info(f"  负样本: {stats.get('negative_samples', 0)}")
        logger.info(f"  最终样本: {final_samples}")
        
        # 检查质量指标
        quality = result.get("quality_report", {}).get("quality_metrics", {})
        logger.info("📈 质量指标:")
        logger.info(f"  意图分布熵: {quality.get('intent_distribution_entropy', 0):.2f}")
        logger.info(f"  表达多样性: {quality.get('expression_diversity', 0):.2f}")
        logger.info(f"  重复率: {quality.get('duplicate_rate', 0):.2f}")
        
        # 验证最小要求
        if final_samples < 100:
            logger.warning("⚠️ 生成样本数量过少")
        else:
            logger.info("✅ 样本数量达标")
        
        if quality.get('intent_distribution_entropy', 0) < 1.5:
            logger.warning("⚠️ 意图分布不够均匀")
        else:
            logger.info("✅ 意图分布良好")
        
        if quality.get('expression_diversity', 0) < 0.2:
            logger.warning("⚠️ 表达多样性不足")
        else:
            logger.info("✅ 表达多样性良好")
        
        logger.info("🎉 数据生成系统测试完成！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_data_generation())
    sys.exit(0 if success else 1)