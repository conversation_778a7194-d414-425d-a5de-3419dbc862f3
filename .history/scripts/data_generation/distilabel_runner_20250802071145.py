#!/usr/bin/env python3

"""

import yaml
import json
import os
from typing import List, Dict, Any, Optional, Union
from pathlib import Path
from datetime import datetime

# 导入真正的distilabel组件
from distilabel.pipeline import Pipeline
from distilabel.steps import GeneratorStep

# 设置日志
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 简化的组件类（仅用于数据生成逻辑）
class WeightedSampler:
    """加权采样器"""
    def weighted_choice(self, choices, weights=None):
        import random
        if weights is None:
            weights = [1.0] * len(choices)
        return random.choices(choices, weights=weights)[0]
    
    def weighted_choice_dict(self, weight_dict):
        import random
        choices = list(weight_dict.keys())
        weights = list(weight_dict.values())
        return random.choices(choices, weights=weights)[0]

class ConfigManager:
    """配置管理器"""
    def get_orm_entities(self):
        return ["客户", "订单", "用户", "产品", "库存", "员工"]
    
    def get_orm_field_types(self):
        return ["INT", "VARCHAR", "TEXT", "DECIMAL", "BOOLEAN", "DATETIME"]

# 全局实例
config_manager = ConfigManager()


class IntentDataGenerator(GeneratorStep):
    """真正的distilabel GeneratorStep - 生成意图数据"""
    
    def __init__(self, config_path: str = "configs/pipeline_config.yaml", **kwargs):
        super().__init__(**kwargs)
        self.config_path = config_path
        self.config = self._load_config()
        self.weighted_sampler = WeightedSampler()
        
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                    logger.info(f"✅ 成功加载配置文件: {self.config_path}")
                    return config
            else:
                logger.warning(f"配置文件不存在: {self.config_path}，使用默认配置")
                return self._get_default_config()
        except Exception as e:
            logger.error(f"配置文件加载失败: {e}，使用默认配置")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "enhanced_mode": {
                "seed_samples_per_intent": 10,
                "evolution_generations": 1,
                "variants_per_generation": 2
            }
        }
    
    @property
    def inputs(self) -> List[str]:
        """定义输入字段（GeneratorStep通常没有输入）"""
        return []
    
    @property
    def outputs(self) -> List[str]:
        """定义输出字段"""
        return ["instruction", "intent_json", "entity", "field_name", "data_type", "intent_type"]
    
    def process(self, offset: int = 0) -> List[Dict[str, Any]]:
        """处理方法 - 生成数据"""
        generation_config = self.config.get("enhanced_mode", {})
        samples_per_intent = generation_config.get("seed_samples_per_intent", 10)
        
        logger.info(f"🎯 每种意图生成 {samples_per_intent} 个样本")
        
        intent_types = ["ADD_COLUMN", "DELETE_COLUMN", "MODIFY_COLUMN", "ENABLE_SOFT_DELETE"]
        generated_data = []
        
        for intent_type in intent_types:
            logger.info(f"🔄 生成 {intent_type} 类型的样本...")
            entities = config_manager.get_orm_entities()
            
            for i in range(samples_per_intent):
                # 生成基础数据
                entity = self.weighted_sampler.weighted_choice(entities)
                field_name = self.weighted_sampler.weighted_choice(
                    ["等级", "状态", "类型", "编号", "名称", "描述", "备注", "金额", "时间"]
                )
                data_type = self.weighted_sampler.weighted_choice(
                    config_manager.get_orm_field_types()
                )
                
                # 构建意图JSON
                intent_json = self._build_intent_json(intent_type, entity, field_name, data_type)
                
                # 生成对应的自然语言指令
                instruction = self._generate_instruction(intent_type, entity, field_name, data_type)
                
                generated_data.append({
                    "instruction": instruction,
                    "intent_json": json.dumps([intent_json], ensure_ascii=False),
                    "entity": entity,
                    "field_name": field_name,
                    "data_type": data_type,
                    "intent_type": intent_type
                })
        
        logger.info(f"✅ 数据生成完成，共 {len(generated_data)} 个样本")
        return generated_data
    
    def _build_intent_json(self, intent_type: str, entity: str, field_name: str, data_type: str) -> Dict[str, Any]:
        """构建标准意图JSON结构"""
        base_intent = {
            "intentType": intent_type,
            "targetConceptName": entity,
            "props": {}
        }
        
        if intent_type == "ADD_COLUMN":
            base_intent["props"] = {
                "name": field_name,
                "stdSqlType": data_type,
                "nullable": True,
                "comment": f"{field_name}字段"
            }
        elif intent_type == "DELETE_COLUMN":
            base_intent["props"] = {"name": field_name}
        elif intent_type == "MODIFY_COLUMN":
            base_intent["props"] = {
                "name": field_name,
                "stdSqlType": data_type,
                "nullable": False
            }
        elif intent_type == "ENABLE_SOFT_DELETE":
            base_intent["props"] = {
                "deleteField": "is_deleted",
                "deleteFieldType": "BOOLEAN"
            }
        
        return base_intent
    
    def _generate_instruction(self, intent_type: str, entity: str, field_name: str, data_type: str) -> str:
        """生成对应的自然语言指令"""
        templates = {
            "ADD_COLUMN": [
                f"给{entity}表添加一个{field_name}字段，类型为{data_type}",
                f"在{entity}表中新增{field_name}字段（{data_type}类型）",
                f"为{entity}表增加一个名为{field_name}的{data_type}字段",
                f"{entity}表需要添加{field_name}字段，数据类型是{data_type}",
                f"请在{entity}表添加{field_name}字段，使用{data_type}类型"
            ],
            "DELETE_COLUMN": [
                f"删除{entity}表的{field_name}字段",
                f"移除{entity}表中的{field_name}字段",
                f"从{entity}表中删掉{field_name}字段",
                f"{entity}表的{field_name}字段不需要了，请删除",
                f"去掉{entity}表的{field_name}字段"
            ],
            "MODIFY_COLUMN": [
                f"修改{entity}表的{field_name}字段类型为{data_type}",
                f"将{entity}表{field_name}字段改为{data_type}类型",
                f"更新{entity}表{field_name}字段的数据类型为{data_type}",
                f"{entity}表的{field_name}字段类型需要改成{data_type}"
            ],
            "ENABLE_SOFT_DELETE": [
                f"为{entity}表启用软删除功能",
                f"给{entity}表开启逻辑删除",
                f"{entity}表需要支持软删除",
                f"启用{entity}表的软删除机制"
            ]
        }
        
        return self.weighted_sampler.weighted_choice(templates[intent_type])


def create_pipeline(config_path: str = "configs/pipeline_config.yaml") -> Pipeline:
    """创建真正的distilabel Pipeline"""
    logger.info("🚀 创建真正的distilabel Pipeline...")
    
    try:
        # 创建Pipeline实例
        pipeline = Pipeline(name="IntentDataGeneration")
        
        # 添加数据生成步骤
        intent_generator = IntentDataGenerator(
            name="intent_data_generator",
            config_path=config_path
        )
        
        # 将步骤添加到pipeline
        pipeline.add_step(intent_generator)
        
        logger.info("✅ 真正的distilabel Pipeline创建成功")
        return pipeline
        
    except Exception as e:
        logger.error(f"❌ Pipeline创建失败: {e}")
        logger.error(f"错误详情: {type(e).__name__}: {str(e)}")
        raise


async def run_pipeline(config_path: str, output_path: str) -> Any:
    """运行真正的distilabel Pipeline"""
    logger.info("🔄 开始执行真正的distilabel Pipeline...")
    
    try:
        # 创建Pipeline
        pipeline = create_pipeline(config_path)
        
        # 确保输出目录存在
        output_dir = Path(output_path)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 执行Pipeline
        start_time = datetime.now()
        
        # 运行pipeline
        distiset = pipeline.run(
            use_cache=False,
            storage_parameters={
                "data_path": str(output_dir),
                "format": "jsonl"
            }
        )
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        logger.info(f"✅ 真正的distilabel Pipeline执行完成，用时: {duration:.2f}秒")
        
        # 保存额外的摘要信息
        if distiset and hasattr(distiset, '__len__'):
            summary_path = output_dir / "pipeline_summary.json"
            summary = {
                "pipeline_type": "distilabel_1.5.3",
                "execution_time_seconds": duration,
                "total_samples": len(distiset) if hasattr(distiset, '__len__') else "unknown",
                "config_file": config_path,
                "output_path": str(output_path),
                "timestamp": datetime.now().isoformat(),
                "distilabel_version": "1.5.3"
            }
            
            with open(summary_path, 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2)
            
            logger.info(f"📋 执行摘要已保存: {summary_path}")
        
        return distiset
        
    except Exception as e:
        logger.error(f"❌ 真正的distilabel Pipeline执行失败: {e}")
        logger.error(f"错误详情: {type(e).__name__}: {str(e)}")
        raise


# 向后兼容的函数
def create_multi_model_pipeline(config_path: str = "configs/distilabel_config.yaml") -> Pipeline:
    """创建多模型互评的distilabel Pipeline"""
    logger.info("📝 使用真正的distilabel 1.5.3 Pipeline")
    return create_pipeline(config_path)


async def run_multi_model_pipeline(config_path: str = "configs/distilabel_config.yaml",
                                  output_path: str = "data/processed/multi_model_output") -> Any:
    """运行多模型互评的数据生成Pipeline"""
    logger.info("📝 使用真正的distilabel 1.5.3 Pipeline")
    return await run_pipeline(config_path, output_path)