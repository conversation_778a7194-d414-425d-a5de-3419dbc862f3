# =============================================================================
# 修复后的 src/core/distilabel_pipeline.py
# =============================================================================
#!/usr/bin/env python3
"""
基于distilabel的配置驱动数据生成Pipeline
整合现有组件，支持反向生成和质量控制
"""

import yaml
import json
import os
import asyncio
from typing import List, Dict, Any, Optional, Union
from pathlib import Path
from datetime import datetime

# 基础导入
try:
    from distilabel import Pipeline, Step
    from distilabel.steps import GeneratorStep, TextGeneration, KeepColumns
    from distilabel.llms import InferenceEndpointsLLM, TransformersLLM, OpenAILLM
    from distilabel.steps.typing import StepInput, StepOutput
except ImportError as e:
    print(f"❌ Distilabel导入失败: {e}")
    print("请安装distilabel: pip install distilabel>=1.0.0")
    raise

# 导入现有组件（如果存在）
try:
    from src.core.weighted_sampler import WeightedSampler
except ImportError:
    print("⚠️ WeightedSampler未找到，使用简化版本")
    class WeightedSampler:
        def weighted_choice(self, choices, weights):
            import random
            return random.choice(choices)
        
        def weighted_choice_dict(self, weight_dict):
            import random
            choices = list(weight_dict.keys())
            weights = list(weight_dict.values())
            total = sum(weights)
            weights = [w/total for w in weights]
            return random.choices(choices, weights=weights)[0]

try:
    from src.core.data_quality import DataQualityController
except ImportError:
    print("⚠️ DataQualityController未找到，使用简化版本")
    class DataQualityController:
        def validate_samples(self, samples):
            return samples, []
        
        def assess_quality(self, samples):
            return type('QualityMetrics', (), {'expression_diversity': 0.8})()

try:
    from src.utils.config import config_manager
except ImportError:
    print("⚠️ config_manager未找到，使用简化版本")
    class ConfigManager:
        def get_crud_weights(self, key):
            return {"entity": 1.0}
        
        def get_orm_entities(self):
            return ["客户", "订单", "用户", "产品"]
        
        def get_orm_field_types(self):
            return ["INT", "VARCHAR", "TEXT", "DECIMAL", "BOOLEAN"]
    
    config_manager = ConfigManager()

try:
    from src.models.intent_models import TrainingSample, Intent
except ImportError:
    print("⚠️ Intent模型未找到，使用简化版本")
    class Intent:
        def __init__(self, intentType, targetConceptName, props=None):
            self.intentType = intentType
            self.targetConceptName = targetConceptName
            self.props = props or {}
        
        def dict(self):
            return {
                "intentType": self.intentType,
                "targetConceptName": self.targetConceptName,
                "props": self.props
            }
    
    class TrainingSample:
        def __init__(self, instruction, output, metadata=None):
            self.instruction = instruction
            self.output = output
            self.metadata = metadata or {}

# 日志设置
try:
    from src.utils.logger import logger
except ImportError:
    import logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)


class ConfigDrivenIntentGenerator(GeneratorStep):
    """配置驱动的意图生成步骤 - 复用现有WeightedSampler"""
    
    def __init__(self, config_path: str = "configs/pipeline_config.yaml", **kwargs):
        super().__init__(**kwargs)
        self.config_path = config_path
        self.config = self._load_config()
        self.weighted_sampler = WeightedSampler()
        
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            logger.warning(f"配置文件未找到: {self.config_path}，使用默认配置")
            return self._get_default_config()
        except Exception as e:
            logger.error(f"配置文件加载失败: {e}，使用默认配置")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "enhanced_mode": {
                "seed_samples_per_intent": 10,
                "evolution_generations": 1,
                "variants_per_generation": 2
            }
        }
    
    @property
    def inputs(self) -> List[str]:
        return []
    
    @property 
    def outputs(self) -> List[str]:
        return ["intent_json", "entity", "field_name", "data_type", "semantic_strategy"]
    
    def process(self, offset: int = 0) -> StepOutput:
        """生成结构化意图数据"""
        # 获取配置参数
        generation_config = self.config.get("enhanced_mode", {})
        samples_per_intent = generation_config.get("seed_samples_per_intent", 10)
        
        # 使用现有的WeightedSampler进行采样
        intent_types = ["ADD_COLUMN", "DELETE_COLUMN", "MODIFY_COLUMN", "ENABLE_SOFT_DELETE"]
        
        generated_data = []
        
        for intent_type in intent_types:
            # 基于权重配置采样
            entities = config_manager.get_orm_entities()
            
            for _ in range(samples_per_intent):
                # 使用加权采样选择参数
                entity = self.weighted_sampler.weighted_choice(entities, [1.0] * len(entities))
                field_name = self.weighted_sampler.weighted_choice(
                    ["等级", "状态", "类型", "编号", "名称"], [1.0] * 5
                )
                data_type = self.weighted_sampler.weighted_choice(
                    config_manager.get_orm_field_types(), [1.0] * len(config_manager.get_orm_field_types())
                )
                
                # 按语义策略权重分配
                semantic_strategies = {"atomic": 35, "composite": 30, "sequence": 20, "implicit": 15}
                semantic_strategy = self.weighted_sampler.weighted_choice_dict(semantic_strategies)
                
                # 构建意图JSON
                intent_json = self._build_intent_json(intent_type, entity, field_name, data_type)
                
                generated_data.append({
                    "intent_json": json.dumps(intent_json, ensure_ascii=False),
                    "entity": entity,
                    "field_name": field_name, 
                    "data_type": data_type,
                    "semantic_strategy": semantic_strategy
                })
        
        yield generated_data
    
    def _build_intent_json(self, intent_type: str, entity: str, field_name: str, data_type: str) -> Dict[str, Any]:
        """构建标准意图JSON结构"""
        base_intent = {
            "intentType": intent_type,
            "targetConceptName": entity,
            "props": {}
        }
        
        if intent_type == "ADD_COLUMN":
            base_intent["props"] = {
                "name": field_name,
                "stdSqlType": data_type,
                "nullable": True,
                "comment": f"{field_name}字段"
            }
        elif intent_type == "DELETE_COLUMN":
            base_intent["props"] = {"name": field_name}
        elif intent_type == "MODIFY_COLUMN":
            base_intent["props"] = {
                "name": field_name,
                "stdSqlType": data_type,
                "nullable": False
            }
        elif intent_type == "ENABLE_SOFT_DELETE":
            base_intent["props"] = {
                "deleteField": "is_deleted",
                "deleteFieldType": "BOOLEAN"
            }
        
        return base_intent


class MockLLM:
    """模拟LLM类，用于测试"""
    
    def __init__(self, name: str = "MockLLM"):
        self.name = name
    
    def generate(self, prompt: str, **kwargs) -> str:
        """生成模拟响应"""
        if "指令" in prompt or "instruction" in prompt.lower():
            return f"给{prompt[:10]}添加一个字段"
        else:
            return json.dumps({
                "intentType": "ADD_COLUMN",
                "targetConceptName": "客户",
                "props": {"name": "测试字段", "stdSqlType": "VARCHAR"}
            }, ensure_ascii=False)


def create_pipeline(config_path: str = "configs/pipeline_config.yaml") -> Pipeline:
    """创建简化的测试Pipeline"""
    
    logger.info("🚀 创建测试Pipeline...")
    
    try:
        # 创建Pipeline
        with Pipeline(name="TestIntentGeneration") as pipeline:
            # Step 1: 配置驱动的意图生成
            intent_generator = ConfigDrivenIntentGenerator(
                name="intent_generator",
                config_path=config_path
            )
            
            # 简化版本：直接输出意图数据
            logger.info("✅ 简化Pipeline创建成功")
            
        return pipeline
        
    except Exception as e:
        logger.error(f"❌ Pipeline创建失败: {e}")
        raise


async def run_pipeline(config_path: str, output_path: str) -> Any:
    """运行Pipeline"""
    
    logger.info("🔄 开始执行Pipeline...")
    
    try:
        # 创建Pipeline
        pipeline = create_pipeline(config_path)
        
        # 执行Pipeline
        start_time = datetime.now()
        
        distiset = pipeline.run(use_cache=False)
        
        # 确保输出目录存在
        output_dir = Path(output_path)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存结果
        if distiset:
            distiset.save_to_disk(output_path)
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        logger.info(f"✅ Pipeline执行完成，用时: {duration:.2f}秒")
        
        return distiset
        
    except Exception as e:
        logger.error(f"❌ Pipeline执行失败: {e}")
        raise


# 向后兼容的函数
def create_multi_model_pipeline(config_path: str = "configs/distilabel_config.yaml") -> Pipeline:
    """创建多模型互评的distilabel Pipeline（简化版本）"""
    logger.info("📝 注意：当前使用简化版本的Pipeline")
    return create_pipeline(config_path)


async def run_multi_model_pipeline(config_path: str = "configs/distilabel_config.yaml",
                                  output_path: str = "data/processed/multi_model_output") -> Any:
    """运行多模型互评的数据生成Pipeline（简化版本）"""
    logger.info("📝 注意：当前使用简化版本的Pipeline")
    return await run_pipeline(config_path, output_path)