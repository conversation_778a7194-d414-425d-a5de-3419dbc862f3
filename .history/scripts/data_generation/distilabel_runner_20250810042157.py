#!/usr/bin/env python3
"""
distilabel Pipeline启动脚本
支持配置驱动的数据生成和增强采样模式
遵循KISS、YAGNI和SOLID原则

功能特性:
- 基于distilabel的配置驱动数据生成
- 🚀 新增：增强采样模式支持
- 详细的进度监控和错误处理
- 灵活的配置管理和验证
- 统一的命令行接口
"""

import asyncio
import argparse
import sys
import os
import json
import yaml
import tempfile
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple
import traceback

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入distilabel相关模块
try:
    from src.core.distilabel_pipeline import (
        create_enhanced_pipeline as create_distilabel_pipeline,
        run_enhanced_pipeline as run_distilabel_pipeline
    )
    DISTILABEL_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ distilabel模块导入失败: {e}")
    DISTILABEL_AVAILABLE = False

# 🆕 导入增强采样系统
try:
    from src.core.enhanced_sampling_pipeline import (
        create_enhanced_pipeline,
        generate_enhanced_dataset,
        run_quick_sampling
    )
    ENHANCED_SAMPLING_AVAILABLE = True
    print("✅ 增强采样系统可用")
except ImportError as e:
    print(f"⚠️ 增强采样系统不可用: {e}")
    ENHANCED_SAMPLING_AVAILABLE = False

# 导入日志系统
try:
    from loguru import logger
except ImportError:
    import logging
    logger = logging.getLogger(__name__)
    logging.basicConfig(level=logging.INFO)


# ==================== 核心运行器类 ====================

class DistilabelRunner:
    """distilabel数据生成运行器 - 单一职责原则"""
    
    def __init__(self):
        self.start_time = None
        self.temp_config_path = None
        self.execution_stats = {
            "total_samples": 0,
            "execution_time": 0.0,
            "pipeline_mode": "",
            "success_rate": 1.0
        }
    
    def create_argument_parser(self) -> argparse.ArgumentParser:
        """创建命令行参数解析器"""
        parser = argparse.ArgumentParser(
            description="🔧 distilabel数据生成系统 + 增强采样",
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
🚀 使用示例:

基础distilabel模式:
  python scripts/data_generation/distilabel_runner.py
  python scripts/data_generation/distilabel_runner.py --test-mode
  python scripts/data_generation/distilabel_runner.py --dry-run

🆕 增强采样模式:
  # 启用增强采样
  python scripts/data_generation/distilabel_runner.py --enhanced-sampling
  
  # 增强采样测试
  python scripts/data_generation/distilabel_runner.py --enhanced-sampling --test-mode
  
  # 自定义配置
  python scripts/data_generation/distilabel_runner.py \\
    --enhanced-sampling \\
    --config configs/sampling_config.yaml \\
    --samples 100

高级用法:
  # 指定配置和输出
  python scripts/data_generation/distilabel_runner.py \\
    --config configs/my_config.yaml \\
    --output data/custom_output \\
    --verbose

  # 模型指定
  python scripts/data_generation/distilabel_runner.py \\
    --test-mode \\
    --samples 20 \\
    --model "Qwen/Qwen2.5-4B-Instruct"

  # 增强采样自定义配置
  python scripts/data_generation/distilabel_runner.py \\
    --enhanced-sampling \\
    --config configs/sampling_config.yaml \\
    --samples 50 \\
    --verbose

配置文件结构:
  - pipeline: 基础Pipeline配置
  - distilabel: distilabel特定设置
  - enhanced_mode: 增强生成模式配置 (新增)
  - quality_control: 质量控制参数
  - output: 输出格式和路径

模式对比:
  传统distilabel: 基于LLM的结构化数据生成
  增强采样: 六维采样系统（基础+改写+鲁棒性+边界+负样本+自适应）

注意事项:
  1. 确保配置文件格式正确（YAML）
  2. 检查模型是否可用（本地或API）
  3. 确保有足够的磁盘空间存储输出
  4. 大规模生成前先用测试模式验证
  5. 增强采样模式主要在本地运行，distilabel可能需要API
            """
        )
        
        # 基础参数
        parser.add_argument(
            "--config", "-c",
            type=str,
            default="configs/pipeline_config.yaml",
            help="配置文件路径 (默认: configs/pipeline_config.yaml，增强采样: configs/sampling_config.yaml)"
        )
        
        parser.add_argument(
            "--output", "-o", 
            type=str,
            default=None,
            help="输出目录路径 (默认: 自动生成)"
        )
        
        parser.add_argument(
            "--samples", "-s",
            type=int,
            default=None,
            help="每种意图的样本数量（覆盖配置文件设置）"
        )
        
        parser.add_argument(
            "--model", "-m",
            type=str,
            default=None,
            help="指定使用的模型（覆盖配置文件设置）"
        )
        
        # 🆕 增强采样选项
        parser.add_argument(
            "--enhanced-sampling",
            action="store_true",
            help="🚀 启用增强采样模式（六维采样系统）"
        )
        
        # 运行模式
        parser.add_argument(
            "--test-mode", "-t",
            action="store_true",
            help="🧪 测试模式，生成小量数据用于验证"
        )
        
        parser.add_argument(
            "--dry-run", "-d",
            action="store_true", 
            help="🔍 只验证配置，不执行实际生成"
        )
        
        parser.add_argument(
            "--verbose", "-v",
            action="store_true",
            help="🔊 详细输出模式"
        )
        
        # 质量控制
        parser.add_argument(
            "--min-quality",
            type=float,
            default=None,
            help="最低质量分数阈值"
        )
        
        parser.add_argument(
            "--max-samples",
            type=int,
            default=None,
            help="总样本数量上限"
        )
        
        # 输出格式
        parser.add_argument(
            "--format",
            choices=["json", "jsonl", "parquet"],
            default="json",
            help="输出格式 (默认: json)"
        )
        
        return parser
    
    def determine_config_path(self, args: argparse.Namespace) -> str:
        """根据模式自动确定配置路径 - 智能配置选择"""
        if args.config != "configs/pipeline_config.yaml":
            # 用户指定了配置文件，直接使用
            return args.config
        
        if args.enhanced_sampling:
            # 增强采样模式，使用采样配置
            enhanced_config = "configs/sampling_config.yaml"
            if Path(enhanced_config).exists():
                logger.info(f"🔄 自动切换到增强采样配置: {enhanced_config}")
                return enhanced_config
            else:
                logger.warning(f"⚠️ 增强采样配置不存在: {enhanced_config}，使用默认配置")
        
        return args.config
    
    def validate_config(self, config_path: str) -> Tuple[bool, List[str]]:
        """验证配置文件 - 支持多种配置类型"""
        errors = []
        
        try:
            if not Path(config_path).exists():
                errors.append(f"配置文件不存在: {config_path}")
                return False, errors
            
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 检查增强采样配置
            if "sampling_config" in str(config_path) or "enhanced" in str(config_path):
                return self._validate_enhanced_sampling_config(config, errors)
            
            # 检查distilabel配置
            required_sections = ['pipeline', 'output']
            for section in required_sections:
                if section not in config:
                    errors.append(f"缺少必需的配置节: {section}")
            
            # 检查pipeline配置
            pipeline_config = config.get('pipeline', {})
            if 'mode' not in pipeline_config:
                errors.append("pipeline配置中缺少mode字段")
            
            return len(errors) == 0, errors
            
        except yaml.YAMLError as e:
            errors.append(f"YAML格式错误: {e}")
            return False, errors
        except Exception as e:
            errors.append(f"配置文件读取失败: {e}")
            return False, errors
    
    def _validate_enhanced_sampling_config(self, config: Dict[str, Any], errors: List[str]) -> Tuple[bool, List[str]]:
        """验证增强采样配置"""
        required_sections = ["base_generation", "paraphrase_generation", "robustness_generation"]
        for section in required_sections:
            if section not in config:
                errors.append(f"增强采样配置缺少必需节: {section}")
        
        # 检查数值范围
        base_config = config.get("base_generation", {})
        if base_config.get("base_samples_count", 0) <= 0:
            errors.append("base_samples_count 必须大于0")
        
        return len(errors) == 0, errors
    
    def modify_config_for_test_mode(self, config_path: str, samples: int) -> str:
        """为测试模式修改配置 - YAGNI原则，只修改必要的"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 根据配置类型修改
            if "enhanced" in str(config_path) or "sampling" in str(config_path):
                # 增强采样配置
                if "base_generation" in config:
                    config["base_generation"]["base_samples_count"] = samples
                
                if "paraphrase_generation" in config:
                    config["paraphrase_generation"]["paraphrase_variants"] = min(2, config["paraphrase_generation"].get("paraphrase_variants", 4))
                
                if "robustness_generation" in config:
                    config["robustness_generation"]["boundary_samples"] = samples // 4
                    config["robustness_generation"]["negative_samples"] = samples // 2
            else:
                # distilabel配置
                if "distilabel" in config:
                    config["distilabel"]["num_samples"] = samples
                
                if "enhanced_mode" in config:
                    config["enhanced_mode"]["samples_per_intent"] = samples
            
            # 创建临时配置文件
            temp_fd, temp_path = tempfile.mkstemp(suffix='.yaml', prefix='test_config_')
            self.temp_config_path = temp_path
            
            with os.fdopen(temp_fd, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
            
            logger.info(f"📝 测试配置已创建: {temp_path}")
            return temp_path
            
        except Exception as e:
            logger.error(f"修改测试配置失败: {e}")
            return config_path
    
    def print_pipeline_summary(self, config_path: str):
        """打印Pipeline摘要 - 保持简洁"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            print("\n" + "="*50)
            print("📋 Pipeline配置摘要")
            print("="*50)
            
            # 检测模式
            if "enhanced" in str(config_path) or "sampling" in str(config_path):
                print("🚀 模式: 增强采样系统")
                base_config = config.get('base_generation', {})
                print(f"基础样本: {base_config.get('base_samples_count', '未设置')}")
                
                paraphrase_config = config.get('paraphrase_generation', {})
                print(f"改写变体: {paraphrase_config.get('paraphrase_variants', '未设置')}")
                
                robustness_config = config.get('robustness_generation', {})
                print(f"对抗比例: {robustness_config.get('adversarial_ratio', '未设置')}")
                print(f"边界样本: {robustness_config.get('boundary_samples', '未设置')}")
                print(f"负样本: {robustness_config.get('negative_samples', '未设置')}")
                
            else:
                print("🔧 模式: distilabel Pipeline")
                pipeline_config = config.get('pipeline', {})
                print(f"Pipeline模式: {pipeline_config.get('mode', '未设置')}")
                
                distilabel_config = config.get('distilabel', {})
                if distilabel_config:
                    print(f"模型: {distilabel_config.get('primary_model', {}).get('name', '未设置')}")
                    print(f"样本数: {distilabel_config.get('num_samples', '未设置')}")
                    print(f"批次大小: {distilabel_config.get('batch_size', '未设置')}")
                
                enhanced_config = config.get('enhanced_mode', {})
                if enhanced_config:
                    print(f"增强模式: 启用")
                    print(f"演化代数: {enhanced_config.get('evolution_generations', '未设置')}")
            
            # 输出配置
            output_config = config.get('output', {})
            print(f"输出目录: {output_config.get('output_dir', '未设置')}")
            print(f"保存格式: {output_config.get('save_format', '未设置')}")
            
            print("="*50)
            
        except Exception as e:
            logger.warning(f"无法读取配置摘要: {e}")
    
    def run_enhanced_sampling_mode(self, config_path: str, output_path: str, args: argparse.Namespace) -> bool:
        """运行增强采样模式"""
        if not ENHANCED_SAMPLING_AVAILABLE:
            logger.error("❌ 增强采样系统不可用，请检查依赖安装")
            return False
        
        try:
            logger.info("🚀 启动增强采样模式...")
            
            # 确定样本数量
            sample_count = args.samples or 100
            if args.test_mode:
                sample_count = min(20, sample_count)
                logger.info(f"🧪 测试模式：限制样本数量为 {sample_count}")
            
            # 运行增强采样
            logger.info(f"📊 开始生成 {sample_count} 个基础样本...")
            self.execution_stats["pipeline_mode"] = "enhanced_sampling"
            
            start_time = datetime.now()
            
            samples = run_quick_sampling(
                base_samples_count=sample_count,
                enable_paraphrase=True,
                enable_robustness=True,
                output_path=output_path
            )
            
            end_time = datetime.now()
            self.execution_stats["execution_time"] = (end_time - start_time).total_seconds()
            self.execution_stats["total_samples"] = len(samples)
            
            logger.info(f"✅ 增强采样完成！生成了 {len(samples)} 个样本")
            
            # 打印样本类型分布
            if samples:
                sample_types = {}
                for sample in samples:
                    if hasattr(sample, 'metadata') and sample.metadata:
                        sample_type = sample.metadata.get("sample_type", "unknown")
                        sample_types[sample_type] = sample_types.get(sample_type, 0) + 1
                
                if sample_types:
                    logger.info("📈 样本类型分布:")
                    for sample_type, count in sample_types.items():
                        logger.info(f"   {sample_type}: {count}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 增强采样失败: {e}")
            if args.verbose:
                traceback.print_exc()
            return False
    
    def run_distilabel_mode(self, config_path: str, output_path: str, args: argparse.Namespace) -> Any:
        """运行distilabel模式"""
        if not DISTILABEL_AVAILABLE:
            logger.error("❌ distilabel系统不可用，请安装相关依赖")
            return None
        
        try:
            logger.info("🔧 启动distilabel Pipeline...")
            self.execution_stats["pipeline_mode"] = "distilabel"
            
            start_time = datetime.now()
            
            # 创建pipeline
            pipeline = create_distilabel_pipeline(config_path)
            
            # 执行pipeline
            distiset = pipeline.run()
            
            end_time = datetime.now()
            self.execution_stats["execution_time"] = (end_time - start_time).total_seconds()
            
            if distiset:
                total_samples = len(distiset['default']) if 'default' in distiset else 0
                self.execution_stats["total_samples"] = total_samples
                logger.info(f"✅ distilabel Pipeline完成！生成了 {total_samples} 个样本")
            
            return distiset
            
        except Exception as e:
            logger.error(f"❌ distilabel Pipeline失败: {e}")
            if args.verbose:
                traceback.print_exc()
            return None
    
    def save_execution_summary(self, output_path: str, args: argparse.Namespace):
        """保存执行摘要"""
        try:
            summary = {
                "execution_info": {
                    "pipeline_mode": self.execution_stats["pipeline_mode"],
                    "execution_time_seconds": self.execution_stats["execution_time"],
                    "execution_time_formatted": f"{self.execution_stats['execution_time']:.2f}s",
                    "start_time": self.start_time.isoformat() if self.start_time else None,
                    "end_time": datetime.now().isoformat(),
                    "total_samples_generated": self.execution_stats["total_samples"]
                },
                "configuration": {
                    "config_file": args.config,
                    "output_path": output_path,
                    "test_mode": args.test_mode,
                    "enhanced_sampling": args.enhanced_sampling,
                    "samples_requested": args.samples,
                    "model_specified": args.model,
                    "format": args.format
                },
                "performance_metrics": {
                    "samples_per_second": self.execution_stats["total_samples"] / max(1, self.execution_stats["execution_time"]),
                    "success_rate": self.execution_stats["success_rate"]
                },
                "command_args": vars(args)
            }
            
            # 保存摘要
            summary_path = Path(output_path) / "execution_summary.json"
            summary_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(summary_path, 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2)
            
            logger.info(f"📋 执行摘要已保存: {summary_path}")
            
        except Exception as e:
            logger.warning(f"保存执行摘要失败: {e}")
    
    def print_final_recommendations(self, output_path: str, args: argparse.Namespace):
        """打印最终建议"""
        print("\n" + "="*50)
        print("🎯 后续处理建议:")
        print("="*50)
        
        if args.enhanced_sampling:
            print("📊 增强采样数据分析:")
            print(f"   # 检查生成的数据质量和多样性")
            print(f"   # 验证六维采样的效果")
            
            print("\n🔍 样本质量验证:")
            print("   # 检查对抗样本和边界样本的有效性")
            print("   # 分析负样本的识别准确性")
            
            print("\n🚀 模型训练:")
            print("   # 使用增强数据进行LoRA微调")
            print("   # 验证训练效果提升")
        else:
            print("1. 📊 检查生成的数据质量:")
            print(f"   # 分析distilabel生成的数据分布")
            
            print("\n2. 🔄 转换为训练格式:")
            print(f"   # 将distilabel输出转换为标准训练格式")
            
            print("\n3. 🚀 进行模型微调:")
            print("   # 使用生成的数据进行LoRA微调")
            
            print("\n4. 📈 质量评估:")
            print("   # 评估生成数据的多样性和准确性")
        
        print(f"\n📁 输出位置: {output_path}")
        print("="*50)
    
    def cleanup_temp_files(self):
        """清理临时文件"""
        if self.temp_config_path and os.path.exists(self.temp_config_path):
            try:
                os.unlink(self.temp_config_path)
                logger.info(f"🧹 清理临时配置文件")
            except Exception as e:
                logger.warning(f"清理临时文件失败: {e}")
    
    async def main(self):
        """主函数 - 协调所有组件"""
        parser = self.create_argument_parser()
        args = parser.parse_args()
        
        self.start_time = datetime.now()
        
        try:
            logger.info("🚀 启动distilabel数据生成系统...")
            
            # 设置日志级别
            if args.verbose:
                logger.info("🔊 详细输出模式已启用")
            
            # 自动确定配置路径
            config_path = self.determine_config_path(args)
            
            # 验证配置文件
            if not Path(config_path).exists():
                logger.error(f"❌ 配置文件不存在: {config_path}")
                if args.enhanced_sampling:
                    logger.info("💡 提示: 可以生成默认增强采样配置")
                sys.exit(1)
            
            is_valid, errors = self.validate_config(config_path)
            if not is_valid:
                logger.error("❌ 配置文件验证失败:")
                for error in errors:
                    logger.error(f"  • {error}")
                sys.exit(1)
            
            logger.info("✅ 配置文件验证通过")
            
            # 处理测试模式或自定义样本数
            if args.test_mode or args.samples:
                samples = args.samples or 10
                if args.test_mode:
                    logger.info(f"🧪 测试模式启用，样本数量: {samples}")
                else:
                    logger.info(f"📊 指定样本数量: {samples}")
                config_path = self.modify_config_for_test_mode(config_path, samples)
            
            # 打印配置摘要
            self.print_pipeline_summary(config_path)
            
            # Dry run模式
            if args.dry_run:
                logger.info("🔍 Dry-run模式，只验证配置")
                
                if args.enhanced_sampling:
                    if ENHANCED_SAMPLING_AVAILABLE:
                        try:
                            pipeline = create_enhanced_pipeline(config_path)
                            logger.info("✅ 增强采样Pipeline创建成功")
                        except Exception as e:
                            logger.error(f"❌ 增强采样Pipeline创建失败: {e}")
                            sys.exit(1)
                    else:
                        logger.error("❌ 增强采样系统不可用")
                        sys.exit(1)
                else:
                    if DISTILABEL_AVAILABLE:
                        try:
                            pipeline = create_distilabel_pipeline(config_path)
                            logger.info("✅ distilabel Pipeline创建成功")
                            logger.info(f"Pipeline包含 {len(pipeline.dag.nodes)} 个步骤")
                            
                            # 打印步骤信息
                            for i, step_name in enumerate(pipeline.dag.nodes, 1):
                                logger.info(f"  步骤 {i}: {step_name}")
                        except Exception as e:
                            logger.error(f"❌ distilabel Pipeline创建失败: {e}")
                            sys.exit(1)
                    else:
                        logger.error("❌ distilabel系统不可用")
                        sys.exit(1)
                
                return
            
            # 确定输出路径
            output_path = args.output
            if not output_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                mode_prefix = "enhanced" if args.enhanced_sampling else "distilabel"
                test_suffix = "_test" if args.test_mode else ""
                output_path = f"data/processed/{mode_prefix}_output_{timestamp}{test_suffix}"
            
            logger.info(f"📁 输出路径: {output_path}")
            
            # 执行Pipeline
            logger.info("🔄 开始执行Pipeline...")
            
            success = False
            if args.enhanced_sampling:
                # 增强采样模式
                success = self.run_enhanced_sampling_mode(config_path, output_path, args)
            else:
                # distilabel模式
                distiset = self.run_distilabel_mode(config_path, output_path, args)
                success = distiset is not None
                
                # 显示结果统计
                if success and distiset:
                    if args.verbose and self.execution_stats["total_samples"] > 0:
                        logger.info("📝 生成样本示例:")
                        sample_data = distiset['default'][0] if distiset['default'] else {}
                        for key, value in list(sample_data.items())[:3]:
                            logger.info(f"  {key}: {str(value)[:100]}...")
            
            if success:
                # 保存执行摘要
                self.save_execution_summary(output_path, args)
                
                # 显示执行统计
                logger.info("🎉 Pipeline执行完成！")
                logger.info(f"⏱️  执行时间: {self.execution_stats['execution_time']:.2f} 秒")
                logger.info(f"📊 生成样本数: {self.execution_stats['total_samples']}")
                
                # 打印后续建议
                self.print_final_recommendations(output_path, args)
            else:
                logger.error("❌ Pipeline执行失败")
                sys.exit(1)
            
        except KeyboardInterrupt:
            logger.warning("❌ 用户中断执行")
            sys.exit(1)
        except Exception as e:
            logger.error(f"❌ 执行失败: {e}")
            if args.verbose:
                traceback.print_exc()
            sys.exit(1)
        finally:
            # 清理临时文件
            self.cleanup_temp_files()


# ==================== 便捷函数 ====================

def print_help_and_examples():
    """打印帮助和示例"""
    print("""
🔧 distilabel数据生成系统 - 使用指南

基本命令:
  python scripts/data_generation/distilabel_runner.py                    # 使用默认配置
  python scripts/data_generation/distilabel_runner.py --test-mode       # 测试模式
  python scripts/data_generation/distilabel_runner.py --dry-run         # 验证配置

🆕 增强采样模式:
  python scripts/data_generation/distilabel_runner.py --enhanced-sampling
  python scripts/data_generation/distilabel_runner.py --enhanced-sampling --test-mode

高级用法:
  # 指定配置和输出
  python scripts/data_generation/distilabel_runner.py \\
    --config configs/my_config.yaml \\
    --output data/custom_output \\
    --verbose

  # 小规模生成用于调试
  python scripts/data_generation/distilabel_runner.py \\
    --test-mode \\
    --samples 20 \\
    --model "Qwen/Qwen2.5-4B-Instruct"

  # 增强采样自定义配置
  python scripts/data_generation/distilabel_runner.py \\
    --enhanced-sampling \\
    --config configs/sampling_config.yaml \\
    --samples 50 \\
    --verbose

配置文件结构:
  - pipeline: 基础Pipeline配置
  - distilabel: distilabel特定设置
  - enhanced_mode: 增强生成模式配置
  - quality_control: 质量控制参数
  - output: 输出格式和路径

模式对比:
  传统distilabel: 基于LLM的结构化数据生成
  增强采样: 六维采样系统（基础+改写+鲁棒性+边界+负样本+自适应）

注意事项:
  1. 确保配置文件格式正确（YAML）
  2. 检查模型是否可用（本地或API）
  3. 确保有足够的磁盘空间存储输出
  4. 大规模生成前先用测试模式验证
  5. 增强采样模式主要在本地运行，distilabel可能需要API
    """)


def quick_test():
    """快速测试函数"""
    print("🧪 运行快速测试...")
    
    try:
        # 测试增强采样
        if ENHANCED_SAMPLING_AVAILABLE:
            samples = run_quick_sampling(base_samples_count=3)
            print(f"✅ 增强采样测试通过: {len(samples)} 个样本")
        else:
            print("⚠️ 增强采样不可用")
        
        # 测试distilabel（如果可用）
        if DISTILABEL_AVAILABLE:
            print("✅ distilabel系统可用")
        else:
            print("⚠️ distilabel系统不可用")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")


def compare_modes():
    """对比不同模式的特点"""
    print("""
📊 模式对比分析:

🔧 distilabel模式:
  优势:
  - 基于LLM的高质量生成
  - 支持多种数据格式
  - 灵活的Pipeline配置
  - 内置质量评估

  适用场景:
  - 需要高质量、语义丰富的数据
  - 有充足的API预算
  - 对数据质量要求极高

🚀 增强采样模式:
  优势:
  - 六维采样保证数据多样性
  - 主要在本地运行，成本低
  - 专门针对意图识别优化
  - 包含对抗和边界样本

  适用场景:
  - 需要大量训练数据
  - 成本控制要求严格
  - 专注于意图识别任务
  - 需要鲁棒性增强

💡 选择建议:
  - 质量优先 → distilabel模式
  - 成本优先 → 增强采样模式
  - 混合使用 → 两种模式结合
    """)


def show_system_status():
    """显示系统状态"""
    print("🔍 系统组件状态检查:")
    print("="*40)
    
    # 检查增强采样系统
    status_enhanced = "✅ 可用" if ENHANCED_SAMPLING_AVAILABLE else "❌ 不可用"
    print(f"增强采样系统: {status_enhanced}")
    
    # 检查distilabel系统
    status_distilabel = "✅ 可用" if DISTILABEL_AVAILABLE else "❌ 不可用"
    print(f"distilabel系统: {status_distilabel}")
    
    # 检查配置文件
    configs_to_check = [
        "configs/pipeline_config.yaml",
        "configs/sampling_config.yaml",
        "configs/distilabel_config.yaml"
    ]
    
    print("\n📄 配置文件状态:")
    for config_path in configs_to_check:
        exists = Path(config_path).exists()
        status = "✅ 存在" if exists else "❌ 缺失"
        print(f"  {config_path}: {status}")
    
    # 系统建议
    print("\n💡 建议:")
    if not ENHANCED_SAMPLING_AVAILABLE and not DISTILABEL_AVAILABLE:
        print("  ⚠️ 两个系统都不可用，请检查依赖安装")
    elif not ENHANCED_SAMPLING_AVAILABLE:
        print("  📦 可以安装增强采样系统以获得更多功能")
    elif not DISTILABEL_AVAILABLE:
        print("  📦 可以安装distilabel系统以获得LLM生成功能")
    else:
        print("  🎉 所有系统正常，可以使用全部功能")
    
    print("="*40)


def generate_sample_config():
    """生成示例配置文件"""
    print("📝 生成示例配置文件...")
    
    # 增强采样配置示例
    enhanced_config = {
        "base_generation": {
            "base_samples_count": 200,
            "orm_config_path": "configs/orm_def.yaml"
        },
        "paraphrase_generation": {
            "paraphrase_variants": 4,
            "model_config": {
                "name": "Qwen/Qwen2.5-7B-Instruct"
            }
        },
        "robustness_generation": {
            "adversarial_ratio": 0.3,
            "boundary_samples": 50,
            "negative_samples": 100
        },
        "quality_control": {
            "enabled": True,
            "min_quality_score": 0.5
        }
    }
    
    # distilabel配置示例
    distilabel_config = {
        "pipeline": {
            "mode": "text_generation",
            "steps": ["generator", "processor"]
        },
        "distilabel": {
            "primary_model": {
                "name": "Qwen/Qwen2.5-7B-Instruct",
                "type": "transformers"
            },
            "num_samples": 100,
            "batch_size": 16
        },
        "output": {
            "output_dir": "./data/processed",
            "save_format": ["json"]
        }
    }
    
    # 保存示例配置
    try:
        enhanced_path = "configs/sampling_config_example.yaml"
        distilabel_path = "configs/distilabel_config_example.yaml"
        
        Path("configs").mkdir(exist_ok=True)
        
        with open(enhanced_path, 'w', encoding='utf-8') as f:
            yaml.dump(enhanced_config, f, default_flow_style=False, allow_unicode=True)
        
        with open(distilabel_path, 'w', encoding='utf-8') as f:
            yaml.dump(distilabel_config, f, default_flow_style=False, allow_unicode=True)
        
        print(f"✅ 增强采样配置示例: {enhanced_path}")
        print(f"✅ distilabel配置示例: {distilabel_path}")
        print("\n💡 请根据实际需求修改配置后使用")
        
    except Exception as e:
        print(f"❌ 生成配置文件失败: {e}")


# ==================== 主程序入口 ====================

if __name__ == "__main__":
    # 检查命令行参数
    if len(sys.argv) == 1:
        print_help_and_examples()
        sys.exit(0)
    
    # 特殊命令处理
    if len(sys.argv) == 2:
        if sys.argv[1] in ["--help", "-h"]:
            print_help_and_examples()
            sys.exit(0)
        elif sys.argv[1] == "--quick-test":
            quick_test()
            sys.exit(0)
        elif sys.argv[1] == "--compare-modes":
            compare_modes()
            sys.exit(0)
        elif sys.argv[1] == "--status":
            show_system_status()
            sys.exit(0)
        elif sys.argv[1] == "--generate-config":
            generate_sample_config()
            sys.exit(0)
    
    # 启动主程序
    runner = DistilabelRunner()
    
    try:
        asyncio.run(runner.main())
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)