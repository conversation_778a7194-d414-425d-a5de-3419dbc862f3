#!/usr/bin/env python3
"""
distilabel Pipeline启动脚本
简化的命令行界面，支持配置驱动的数据生成
""

import asyncio
import argparse
import sys
from pathlib import Path
from datetime import datetime
import json

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.core.distilabel_pipeline import create_pipeline, run_pipeline
from src.utils.logger import logger


def create_arg_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="基于distilabel的配置驱动数据生成系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 使用默认配置运行
  python scripts/distilabel_runner.py
  
  # 指定配置文件
  python scripts/distilabel_runner.py --config configs/distilabel_config.yaml
  
  # 小规模测试运行
  python scripts/distilabel_runner.py --test-mode --samples 50
  
  # 指定输出目录
  python scripts/distilabel_runner.py --output data/processed/my_dataset
        """
    )
    
    parser.add_argument(
        "--config", "-c",
        type=str,
        default="configs/pipeline_config.yaml",
        help="配置文件路径"
    )
    
    parser.add_argument(
        "--output", "-o", 
        type=str,
        default=None,
        help="输出目录路径"
    )
    
    parser.add_argument(
        "--test-mode", "-t",
        action="store_true",
        help="测试模式，生成小量数据用于验证"
    )
    
    parser.add_argument(
        "--samples", "-s",
        type=int,
        default=None,
        help="每种意图的样本数量（覆盖配置文件设置）"
    )
    
    parser.add_argument(
        "--model", "-m",
        type=str,
        default=None,
        help="指定使用的模型（覆盖配置文件设置）"
    )
    
    parser.add_argument(
        "--dry-run", "-d",
        action="store_true", 
        help="只验证配置，不执行实际生成"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="详细输出模式"
    )
    
    return parser


def validate_config(config_path: str) -> bool:
    """验证配置文件"""
    try:
        import yaml
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        required_sections = ['pipeline', 'enhanced_mode', 'output']
        missing_sections = [section for section in required_sections if section not in config]
        
        if missing_sections:
            logger.error(f"配置文件缺少必需节: {missing_sections}")
            return False
        
        logger.info("配置文件验证通过")
        return True
        
    except Exception as e:
        logger.error(f"配置文件验证失败: {e}")
        return False


def modify_config_for_test_mode(config_path: str, samples_per_intent: int = 10) -> str:
    """为测试模式修改配置"""
    import yaml
    import tempfile
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 修改为测试模式参数
        config.setdefault('enhanced_mode', {})['seed_samples_per_intent'] = samples_per_intent
        config.setdefault('enhanced_mode', {})['evolution_generations'] = 1
        config.setdefault('enhanced_mode', {})['variants_per_generation'] = 2
        
        # 创建临时配置文件
        temp_config = tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False, encoding='utf-8')
        yaml.dump(config, temp_config, default_flow_style=False, allow_unicode=True)
        temp_config.flush()
        
        logger.info(f"测试模式配置已保存到: {temp_config.name}")
        return temp_config.name
        
    except Exception as e:
        logger.error(f"修改测试配置失败: {e}")
        return config_path


def print_pipeline_summary(config_path: str):
    """打印Pipeline配置摘要"""
    import yaml
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        print("\n" + "="*50)
        print("📊 Pipeline配置摘要")
        print("="*50)
        
        # 基础配置
        pipeline_config = config.get('pipeline', {})
        print(f"Pipeline模式: {pipeline_config.get('mode', 'unknown')}")
        
        # 模型配置
        if 'distilabel' in config and 'model' in config['distilabel']:
            model_config = config['distilabel']['model']
            print(f"使用模型: {model_config.get('name', 'unknown')}")
            print(f"模型类型: {model_config.get('type', 'unknown')}")
        
        # 生成配置
        enhanced_config = config.get('enhanced_mode', {})
        print(f"每种意图样本数: {enhanced_config.get('seed_samples_per_intent', 'unknown')}")
        print(f"演化代数: {enhanced_config.get('evolution_generations', 'unknown')}")
        
        # 输出配置
        output_config = config.get('output', {})
        print(f"输出目录: {output_config.get('output_dir', 'unknown')}")
        print(f"保存格式: {output_config.get('save_format', 'unknown')}")
        
        print("="*50)
        
    except Exception as e:
        logger.warning(f"无法读取配置摘要: {e}")


async def main():
    """主函数"""
    parser = create_arg_parser()
    args = parser.parse_args()
    
    # 设置日志级别
    if args.verbose:
        logger.info("启用详细输出模式")
    
    logger.info("🚀 启动distilabel数据生成Pipeline...")
    
    try:
        # 验证配置文件
        if not Path(args.config).exists():
            logger.error(f"配置文件不存在: {args.config}")
            sys.exit(1)
        
        if not validate_config(args.config):
            logger.error("配置文件验证失败")
            sys.exit(1)
        
        # 处理测试模式
        config_path = args.config
        if args.test_mode:
            samples = args.samples or 10
            logger.info(f"测试模式启用，每种意图生成 {samples} 个样本")
            config_path = modify_config_for_test_mode(args.config, samples)
        elif args.samples:
            logger.info(f"指定样本数量: {args.samples}")
            config_path = modify_config_for_test_mode(args.config, args.samples)
        
        # 打印配置摘要
        print_pipeline_summary(config_path)
        
        # Dry run模式
        if args.dry_run:
            logger.info("Dry-run模式，只验证配置")
            try:
                pipeline = create_pipeline(config_path)
                logger.info("✅ Pipeline创建成功，配置验证通过")
                logger.info(f"Pipeline包含 {len(pipeline.dag.nodes)} 个步骤")
                
                # 打印步骤信息
                for i, step_name in enumerate(pipeline.dag.nodes, 1):
                    logger.info(f"  步骤 {i}: {step_name}")
                
                return
            except Exception as e:
                logger.error(f"❌ Pipeline创建失败: {e}")
                sys.exit(1)
        
        # 确定输出路径
        output_path = args.output
        if not output_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"data/processed/distilabel_output_{timestamp}"
        
        logger.info(f"📁 输出路径: {output_path}")
        
        # 执行Pipeline
        logger.info("🔄 开始执行Pipeline...")
        start_time = datetime.now()
        
        try:
            distiset = await run_pipeline(config_path, output_path)
            
            # 执行完成统计
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            logger.info("🎉 Pipeline执行完成！")
            logger.info(f"⏱️  执行时间: {duration:.2f} 秒")
            
            # 显示结果统计
            if distiset:
                total_samples = len(distiset['default']) if 'default' in distiset else 0
                logger.info(f"📊 生成样本数: {total_samples}")
                
                # 保存执行摘要
                summary = {
                    "execution_time": f"{duration:.2f}s",
                    "total_samples": total_samples,
                    "config_file": args.config,
                    "output_path": output_path,
                    "timestamp": datetime.now().isoformat(),
                    "test_mode": args.test_mode,
                    "command_args": vars(args)
                }
                
                summary_path = Path(output_path) / "execution_summary.json"
                summary_path.parent.mkdir(parents=True, exist_ok=True)
                
                with open(summary_path, 'w', encoding='utf-8') as f:
                    json.dump(summary, f, ensure_ascii=False, indent=2)
                
                logger.info(f"📋 执行摘要已保存: {summary_path}")
                
                # 显示示例数据
                if args.verbose and total_samples > 0:
                    logger.info("📝 生成样本示例:")
                    sample_data = distiset['default'][0] if distiset['default'] else {}
                    for key, value in list(sample_data.items())[:3]:
                        logger.info(f"  {key}: {str(value)[:100]}...")
            
        except Exception as e:
            logger.error(f"❌ Pipeline执行失败: {e}")
            import traceback
            if args.verbose:
                traceback.print_exc()
            sys.exit(1)
        
        # 后续处理建议
        print("\n" + "="*50)
        print("🎯 后续处理建议:")
        print("="*50)
        print("1. 检查生成的数据质量:")
        print(f"   python scripts/data_generation/assess_data_quality.py {output_path}")
        
        print("\n2. 转换为训练格式:")
        print(f"   # 将distilabel输出转换为标准训练格式")
        
        print("\n3. 进行模型微调:")
        print("   # 使用生成的数据进行LoRA微调")
        
        print("\n4. 质量评估:")
        print("   # 评估生成数据的多样性和准确性")
        print("="*50)
        
    except KeyboardInterrupt:
        logger.warning("❌ 用户中断执行")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ 执行失败: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


def print_help_and_examples():
    """打印帮助和示例"""
    print("""
🔧 distilabel数据生成系统 - 使用指南

基本命令:
  python scripts/distilabel_runner.py                    # 使用默认配置
  python scripts/distilabel_runner.py --test-mode       # 测试模式
  python scripts/distilabel_runner.py --dry-run         # 验证配置

高级用法:
  # 指定配置和输出
  python scripts/distilabel_runner.py \\
    --config configs/my_config.yaml \\
    --output data/custom_output \\
    --verbose

  # 小规模生成用于调试
  python scripts/distilabel_runner.py \\
    --test-mode \\
    --samples 20 \\
    --model "Qwen/Qwen2.5-4B-Instruct"

配置文件结构:
  - pipeline: 基础Pipeline配置
  - distilabel: distilabel特定设置
  - enhanced_mode: 增强生成模式配置
  - quality_control: 质量控制参数
  - output: 输出格式和路径

注意事项:
  1. 确保配置文件格式正确（YAML）
  2. 检查模型是否可用（本地或API）
  3. 确保有足够的磁盘空间存储输出
  4. 大规模生成前先用测试模式验证
    """)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except Exception as e:
        print(f"启动失败: {e}")
        sys.exit(1)