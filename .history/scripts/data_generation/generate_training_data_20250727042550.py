#!/usr/bin/env python3
"""
训练数据生成脚本
"""

import asyncio
import sys
import os
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.core.data_pipeline import DataGenerationPipeline
from src.utils.logger import logger

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="生成训练数据")
    parser.add_argument("--samples-per-intent", type=int, default=500, 
                       help="每种意图类型的样本数量")
    parser.add_argument("--adversarial-ratio", type=float, default=0.1,
                       help="对抗样本比例")
    parser.add_argument("--boundary-cases", type=int, default=100,
                       help="边界案例数量")
    parser.add_argument("--fuzzy-samples", type=int, default=200,
                       help="模糊样本数量")
    parser.add_argument("--negative-samples", type=int, default=100,
                       help="负样本数量")
    parser.add_argument("--incremental", nargs='+', 
                       help="增量生成指定意图类型")
    
    args = parser.parse_args()
    
    try:
        # 初始化Pipeline
        pipeline = DataGenerationPipeline()
        await pipeline.initialize()
        
        if args.incremental:
            # 增量生成
            logger.info(f"🎯 增量生成模式，意图类型: {args.incremental}")
            result = await pipeline.run_incremental_generation(
                intent_types=args.incremental,
                samples_per_intent=args.samples_per_intent
            )
        else:
            # 完整生成
            logger.info("🚀 完整生成模式")
            result = await pipeline.run_full_pipeline(
                samples_per_intent=args.samples_per_intent,
                adversarial_ratio=args.adversarial_ratio,
                boundary_cases=args.boundary_cases,
                fuzzy_samples=args.fuzzy_samples,
                negative_samples=args.negative_samples
            )
        
        logger.info("数据生成完成！")
        logger.info(f"生成统计: {result.get('generation_statistics', {})}")
        logger.info(f"保存文件: {result.get('saved_files', [])}")
        
    except Exception as e:
        logger.error(f"数据生成失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())