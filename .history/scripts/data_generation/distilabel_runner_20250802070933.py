#!/usr/bin/env python3
"""
修复后的distilabel Pipeline - 兼容distilabel 1.5.3
基于实际API结构: Pipeline在distilabel.pipeline中
"""

import yaml
import json
import os
import asyncio
from typing import List, Dict, Any, Optional, Union
from pathlib import Path
from datetime import datetime

# 设置日志
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 正确的distilabel 1.5.3导入方式
try:
    from distilabel.pipeline import Pipeline
    logger.info("✅ 成功导入distilabel.pipeline.Pipeline")
    DISTILABEL_AVAILABLE = True
except ImportError as e:
    logger.warning(f"⚠️ distilabel.pipeline导入失败: {e}")
    DISTILABEL_AVAILABLE = False
    
    # 创建简化的Pipeline类
    class Pipeline:
        def __init__(self, name=None):
            self.name = name
            self.steps = []
        
        def run(self, **kwargs):
            return None

# 尝试导入distilabel的其他组件
try:
    from distilabel.steps import GeneratorStep, TextGeneration
    logger.info("✅ 成功导入distilabel.steps")
except ImportError:
    logger.warning("⚠️ distilabel.steps导入失败，使用简化版本")
    
    class GeneratorStep:
        def __init__(self, **kwargs):
            pass
    
    class TextGeneration:
        def __init__(self, **kwargs):
            pass

# 简化的组件类
class WeightedSampler:
    """简化的加权采样器"""
    def weighted_choice(self, choices, weights=None):
        import random
        if weights is None:
            weights = [1.0] * len(choices)
        return random.choices(choices, weights=weights)[0]
    
    def weighted_choice_dict(self, weight_dict):
        import random
        choices = list(weight_dict.keys())
        weights = list(weight_dict.values())
        return random.choices(choices, weights=weights)[0]

class ConfigManager:
    """简化的配置管理器"""
    def get_orm_entities(self):
        return ["客户", "订单", "用户", "产品", "库存", "员工"]
    
    def get_orm_field_types(self):
        return ["INT", "VARCHAR", "TEXT", "DECIMAL", "BOOLEAN", "DATETIME"]

# 全局实例
config_manager = ConfigManager()

class SimpleDataGenerator:
    """完全独立的数据生成器"""
    
    def __init__(self, config_path: str = "configs/pipeline_config.yaml"):
        self.config_path = config_path
        self.config = self._load_config()
        self.weighted_sampler = WeightedSampler()
        
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                    logger.info(f"✅ 成功加载配置文件: {self.config_path}")
                    return config
            else:
                logger.warning(f"配置文件不存在: {self.config_path}，使用默认配置")
                return self._get_default_config()
        except Exception as e:
            logger.error(f"配置文件加载失败: {e}，使用默认配置")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "enhanced_mode": {
                "seed_samples_per_intent": 10,
                "evolution_generations": 1,
                "variants_per_generation": 2
            }
        }
    
    def generate_data(self) -> List[Dict[str, Any]]:
        """生成结构化意图数据"""
        generation_config = self.config.get("enhanced_mode", {})
        samples_per_intent = generation_config.get("seed_samples_per_intent", 10)
        
        logger.info(f"🎯 每种意图生成 {samples_per_intent} 个样本")
        
        intent_types = ["ADD_COLUMN", "DELETE_COLUMN", "MODIFY_COLUMN", "ENABLE_SOFT_DELETE"]
        generated_data = []
        
        for intent_type in intent_types:
            logger.info(f"🔄 生成 {intent_type} 类型的样本...")
            entities = config_manager.get_orm_entities()
            
            for i in range(samples_per_intent):
                # 生成基础数据
                entity = self.weighted_sampler.weighted_choice(entities)
                field_name = self.weighted_sampler.weighted_choice(
                    ["等级", "状态", "类型", "编号", "名称", "描述", "备注", "金额", "时间"]
                )
                data_type = self.weighted_sampler.weighted_choice(
                    config_manager.get_orm_field_types()
                )
                
                # 构建意图JSON
                intent_json = self._build_intent_json(intent_type, entity, field_name, data_type)
                
                # 生成对应的自然语言指令
                instruction = self._generate_instruction(intent_type, entity, field_name, data_type)
                
                generated_data.append({
                    "instruction": instruction,
                    "intent_json": json.dumps([intent_json], ensure_ascii=False),
                    "entity": entity,
                    "field_name": field_name,
                    "data_type": data_type,
                    "intent_type": intent_type
                })
        
        logger.info(f"✅ 数据生成完成，共 {len(generated_data)} 个样本")
        return generated_data
    
    def _build_intent_json(self, intent_type: str, entity: str, field_name: str, data_type: str) -> Dict[str, Any]:
        """构建标准意图JSON结构"""
        base_intent = {
            "intentType": intent_type,
            "targetConceptName": entity,
            "props": {}
        }
        
        if intent_type == "ADD_COLUMN":
            base_intent["props"] = {
                "name": field_name,
                "stdSqlType": data_type,
                "nullable": True,
                "comment": f"{field_name}字段"
            }
        elif intent_type == "DELETE_COLUMN":
            base_intent["props"] = {"name": field_name}
        elif intent_type == "MODIFY_COLUMN":
            base_intent["props"] = {
                "name": field_name,
                "stdSqlType": data_type,
                "nullable": False
            }
        elif intent_type == "ENABLE_SOFT_DELETE":
            base_intent["props"] = {
                "deleteField": "is_deleted",
                "deleteFieldType": "BOOLEAN"
            }
        
        return base_intent
    
    def _generate_instruction(self, intent_type: str, entity: str, field_name: str, data_type: str) -> str:
        """生成对应的自然语言指令"""
        templates = {
            "ADD_COLUMN": [
                f"给{entity}表添加一个{field_name}字段，类型为{data_type}",
                f"在{entity}表中新增{field_name}字段（{data_type}类型）",
                f"为{entity}表增加一个名为{field_name}的{data_type}字段",
                f"{entity}表需要添加{field_name}字段，数据类型是{data_type}",
                f"请在{entity}表添加{field_name}字段，使用{data_type}类型"
            ],
            "DELETE_COLUMN": [
                f"删除{entity}表的{field_name}字段",
                f"移除{entity}表中的{field_name}字段",
                f"从{entity}表中删掉{field_name}字段",
                f"{entity}表的{field_name}字段不需要了，请删除",
                f"去掉{entity}表的{field_name}字段"
            ],
            "MODIFY_COLUMN": [
                f"修改{entity}表的{field_name}字段类型为{data_type}",
                f"将{entity}表{field_name}字段改为{data_type}类型",
                f"更新{entity}表{field_name}字段的数据类型为{data_type}",
                f"{entity}表的{field_name}字段类型需要改成{data_type}"
            ],
            "ENABLE_SOFT_DELETE": [
                f"为{entity}表启用软删除功能",
                f"给{entity}表开启逻辑删除",
                f"{entity}表需要支持软删除",
                f"启用{entity}表的软删除机制"
            ]
        }
        
        return self.weighted_sampler.weighted_choice(templates[intent_type])


class MockDistiset:
    """模拟distilabel的数据集结构"""
    
    def __init__(self, data: List[Dict[str, Any]]):
        self.data = {"default": data}
    
    def save_to_disk(self, path: str):
        """保存数据到磁盘"""
        output_path = Path(path)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 保存为JSONL格式
        jsonl_path = output_path / "default.jsonl"
        with open(jsonl_path, 'w', encoding='utf-8') as f:
            for item in self.data["default"]:
                f.write(json.dumps(item, ensure_ascii=False) + '\n')
        
        # 保存摘要信息
        summary_path = output_path / "dataset_summary.json"
        summary = {
            "total_samples": len(self.data["default"]),
            "intent_types": list(set(item["intent_type"] for item in self.data["default"])),
            "entities": list(set(item["entity"] for item in self.data["default"])),
            "generated_at": datetime.now().isoformat(),
            "distilabel_version": "1.5.3_compatible"
        }
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        
        logger.info(f"📁 数据已保存到: {jsonl_path}")
        logger.info(f"📋 摘要已保存到: {summary_path}")
    
    def __contains__(self, key):
        return key in self.data
    
    def __getitem__(self, key):
        return self.data[key]
    
    def __len__(self):
        return len(self.data.get("default", []))


def create_pipeline(config_path: str = "configs/pipeline_config.yaml") -> Any:
    """创建数据生成Pipeline"""
    logger.info("🚀 创建数据生成Pipeline...")
    
    try:
        # 创建数据生成器
        generator = SimpleDataGenerator(config_path)
        
        # 模拟Pipeline对象
        class SimplePipeline:
            def __init__(self, generator):
                self.generator = generator
                self.data = None
            
            def run(self, use_cache=False, **kwargs):
                """运行数据生成"""
                logger.info("🔄 开始生成数据...")
                self.data = self.generator.generate_data()
                logger.info(f"✅ 生成完成，共{len(self.data)}个样本")
                
                return MockDistiset(self.data)
        
        pipeline = SimplePipeline(generator)
        logger.info("✅ Pipeline创建成功")
        return pipeline
        
    except Exception as e:
        logger.error(f"❌ Pipeline创建失败: {e}")
        raise


async def run_pipeline(config_path: str, output_path: str) -> Any:
    """运行Pipeline"""
    logger.info("🔄 开始执行Pipeline...")
    
    try:
        # 创建Pipeline
        pipeline = create_pipeline(config_path)
        
        # 执行Pipeline
        start_time = datetime.now()
        distiset = pipeline.run(use_cache=False)
        
        # 保存结果
        distiset.save_to_disk(output_path)
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        logger.info(f"✅ Pipeline执行完成，用时: {duration:.2f}秒")
        return distiset
        
    except Exception as e:
        logger.error(f"❌ Pipeline执行失败: {e}")
        raise


# 向后兼容的函数
def create_multi_model_pipeline(config_path: str = "configs/distilabel_config.yaml") -> Any:
    """创建多模型互评的distilabel Pipeline（简化版本）"""
    logger.info("📝 当前使用简化版本的Pipeline，兼容distilabel 1.5.3")
    return create_pipeline(config_path)


async def run_multi_model_pipeline(config_path: str = "configs/distilabel_config.yaml",
                                  output_path: str = "data/processed/multi_model_output") -> Any:
    """运行多模型互评的数据生成Pipeline（简化版本）"""
    logger.info("📝 当前使用简化版本的Pipeline，兼容distilabel 1.5.3")
    return await run_pipeline(config_path, output_path)