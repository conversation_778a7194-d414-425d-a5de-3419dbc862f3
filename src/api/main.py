"""
自然语言意图解析系统 - API主入口
"""

import sys
import os
from pathlib import Path
from contextlib import asynccontextmanager

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from typing import List, Dict, Any
import uvicorn
from datetime import datetime
import traceback

# 导入本地模块
from src.models.intent_models import ParseRequest, ParseResponse, Intent
from src.utils.config import config_manager
from src.utils.logger import logger

@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动事件
    logger.info("自然语言意图解析API启动中...")
    logger.info(f"配置信息：{config_manager.get('app.name')} v{config_manager.get('app.version')}")
    logger.info("API服务启动完成")
    yield
    # 关闭事件
    logger.info("API服务正在关闭...")

# 创建FastAPI应用
app = FastAPI(
    title="自然语言意图解析API",
    description="将自然语言指令转换为结构化意图JSON",
    version=config_manager.get("app.version", "1.0.0"),
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mock数据，用于测试
MOCK_RESPONSES = {
    "给客户表添加一个等级字段": {
        "intents": [
            {
                "intentType": "ADD_COLUMN",
                "targetConceptName": "客户",
                "props": {
                    "name": "等级",
                    "stdSqlType": "VARCHAR(50)"
                },
                "confidence": 0.95
            }
        ],
        "confidence": 0.95,
        "need_clarification": False,
        "clarification_options": []
    },
    "删除订单表的备注字段": {
        "intents": [
            {
                "intentType": "DELETE_COLUMN",
                "targetConceptName": "订单",
                "props": {
                    "name": "备注"
                },
                "confidence": 0.88
            }
        ],
        "confidence": 0.88,
        "need_clarification": False,
        "clarification_options": []
    },
    "加字段": {
        "intents": [],
        "confidence": 0.2,
        "need_clarification": True,
        "clarification_options": [
            "您是想在哪个表中添加字段？",
            "请指定要添加的字段名称",
            "请选择字段类型：文本/数字/日期"
        ]
    }
}

@app.get("/")
async def root():
    """根路径，返回API信息"""
    return {
        "message": "自然语言意图解析API",
        "version": config_manager.get("app.version", "1.0.0"),
        "status": "running",
        "timestamp": datetime.now().isoformat(),
        "docs": "/docs",
        "health": "/health"
    }

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {
        "status": "healthy",
        "service": "nlp-intent-parsing",
        "timestamp": datetime.now().isoformat(),
        "version": config_manager.get("app.version", "1.0.0"),
        "uptime": "正在运行"
    }

@app.post("/parse", response_model=ParseResponse)
async def parse_intent(request: ParseRequest):
    """解析自然语言意图"""
    start_time = datetime.now()
    
    try:
        logger.info(f"收到解析请求: {request.text}")
        
        # 输入验证
        if not request.text or len(request.text.strip()) == 0:
            raise HTTPException(status_code=400, detail="输入文本不能为空")
        
        if len(request.text) > 500:
            raise HTTPException(status_code=400, detail="输入文本过长，请控制在500字符以内")
        
        # 查找匹配的Mock响应
        text = request.text.strip()
        mock_response = None
        
        # 精确匹配
        if text in MOCK_RESPONSES:
            mock_response = MOCK_RESPONSES[text]
            logger.info(f"精确匹配到Mock响应")
        else:
            # 模糊匹配逻辑
            if any(keyword in text for keyword in ["添加", "加", "增加"]) and "字段" in text:
                mock_response = MOCK_RESPONSES["给客户表添加一个等级字段"]
                logger.info("匹配到添加字段意图")
            elif any(keyword in text for keyword in ["删除", "移除"]) and "字段" in text:
                mock_response = MOCK_RESPONSES["删除订单表的备注字段"]
                logger.info("匹配到删除字段意图")
            elif len(text) < 10:  # 短文本认为是模糊输入
                mock_response = MOCK_RESPONSES["加字段"]
                logger.info("检测到模糊输入，返回澄清选项")
        
        # 如果没有匹配，返回通用的澄清响应
        if mock_response is None:
            mock_response = {
                "intents": [],
                "confidence": 0.1,
                "need_clarification": True,
                "clarification_options": [
                    "抱歉，我没有理解您的意图，请提供更详细的描述",
                    "您可以尝试这样描述：'给[表名]添加[字段名]字段'",
                    "或者：'删除[表名]的[字段名]字段'"
                ]
            }
            logger.warning("未匹配到任何意图，返回通用澄清")
        
        # 计算处理时间
        processing_time = (datetime.now() - start_time).total_seconds() * 1000
        
        # 构建响应
        response = ParseResponse(
            intents=[Intent(**intent) for intent in mock_response["intents"]],
            confidence=mock_response["confidence"],
            need_clarification=mock_response["need_clarification"],
            clarification_options=mock_response["clarification_options"],
            processing_time_ms=round(processing_time, 2)
        )
        
        logger.info(f"解析完成，置信度: {response.confidence}, 处理时间: {processing_time:.2f}ms")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"解析请求处理失败: {str(e)}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"服务内部错误: {str(e)}")

@app.get("/parse/examples")
async def get_examples():
    """获取示例输入，用于API测试"""
    return {
        "examples": [
            {
                "input": "给客户表添加一个等级字段",
                "description": "标准的添加字段指令",
                "expected_intent": "ADD_COLUMN"
            },
            {
                "input": "删除订单表的备注字段", 
                "description": "标准的删除字段指令",
                "expected_intent": "DELETE_COLUMN"
            },
            {
                "input": "加字段",
                "description": "模糊指令，会触发澄清推荐",
                "expected_intent": "CLARIFICATION_NEEDED"
            }
        ],
        "supported_intents": [
            "ADD_COLUMN - 添加字段",
            "DELETE_COLUMN - 删除字段", 
            "MODIFY_COLUMN - 修改字段",
            "ADD_RELATIONSHIP - 添加关系",
            "DELETE_RELATIONSHIP - 删除关系",
            "ENABLE_SOFT_DELETE - 启用软删除"
        ]
    }

if __name__ == "__main__":
    # 启动服务
    uvicorn.run(
        "src.api.main:app",
        host="127.0.0.1",
        port=8000,
        reload=True,
        log_level="info"
    )
