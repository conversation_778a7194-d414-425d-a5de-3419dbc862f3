"""
基础样本生成与多样性扩展系统
采用意图驱动的反向生成 + 多样化扩展 + 自适应采样策略
遵循KISS和YAGNI原则，构建高质量训练数据生成管道
"""

import json
import yaml
import random
import numpy as np
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass, field
from pathlib import Path
from loguru import logger
import hashlib
from sklearn.metrics.pairwise import cosine_similarity
from sentence_transformers import SentenceTransformer
import asyncio

@dataclass
class IntentSample:
    """意图样本数据结构"""
    intent_json: Dict[str, Any]
    instruction: str
    sample_type: str  # base, paraphrase, adversarial, boundary, negative
    confidence: float = 1.0
    metadata: Dict[str, Any] = field(default_factory=dict)
    hash_id: str = field(default="")
    
    def __post_init__(self):
        """生成样本哈希ID用于去重"""
        if not self.hash_id:
            content = f"{self.intent_json}_{self.instruction}"
            self.hash_id = hashlib.md5(content.encode()).hexdigest()


class IntentDrivenGenerator:
    """意图驱动的基础样本生成器"""
    
    def __init__(self, orm_config_path: str = "configs/orm_def.yaml"):
        self.orm_config = self._load_orm_config(orm_config_path)
        self.intent_templates = self._extract_intent_templates()
        
    def _load_orm_config(self, config_path: str) -> Dict[str, Any]:
        """加载ORM配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"加载ORM配置失败: {e}")
            return self._get_default_orm_config()
    
    def _get_default_orm_config(self) -> Dict[str, Any]:
        """默认ORM配置"""
        return {
            "entities": ["客户", "订单", "产品", "用户", "供应商"],
            "field_types": ["VARCHAR(50)", "INT", "DECIMAL(10,2)", "DATETIME", "BOOLEAN"],
            "operations": ["ADD_COLUMN", "DELETE_COLUMN", "MODIFY_COLUMN", "ADD_RELATIONSHIP"]
        }
    
    def _extract_intent_templates(self) -> List[Dict[str, Any]]:
        """从ORM配置中提取所有可能的意图结构"""
        templates = []
        entities = self.orm_config.get("entities", [])
        field_types = self.orm_config.get("field_types", [])
        operations = self.orm_config.get("operations", [])
        
        # 为每个操作类型生成所有可能的组合
        for operation in operations:
            for entity in entities:
                for field_type in field_types:
                    template = {
                        "intentType": operation,
                        "targetConceptName": entity,
                        "props": {
                            "name": f"field_{len(templates)}",
                            "stdSqlType": field_type
                        }
                    }
                    templates.append(template)
        
        logger.info(f"从ORM配置生成了 {len(templates)} 个基础意图模板")
        return templates
    
    def generate_base_samples(self, count: int = None) -> List[IntentSample]:
        """生成基础意图样本"""
        if count is None:
            count = len(self.intent_templates)
        
        samples = []
        selected_templates = random.sample(self.intent_templates, min(count, len(self.intent_templates)))
        
        for i, template in enumerate(selected_templates):
            # 生成基础指令（规范表达）
            instruction = self._generate_base_instruction(template)
            
            sample = IntentSample(
                intent_json=template,
                instruction=instruction,
                sample_type="base",
                metadata={"template_id": i, "generation_method": "intent_driven"}
            )
            samples.append(sample)
        
        logger.info(f"生成了 {len(samples)} 个基础意图样本")
        return samples


class ParaphraseGenerator:
    """多样化指令生成器 - 使用Paraphrasing模型"""
    
    def __init__(self, model_name: str = "Qwen/Qwen2.5-7B-Instruct"):
        self.model_name = model_name
        self.paraphrase_strategies = {
            "formal": "请将以下指令改写为正式的商务表达",
            "casual": "请将以下指令改写为口语化的日常表达", 
            "technical": "请将以下指令改写为技术性的专业表达",
            "brief": "请将以下指令改写为简洁的表达"
        }
    
    async def generate_paraphrases(self, base_samples: List[IntentSample], 
                                 variants_per_sample: int = 4) -> List[IntentSample]:
        """为基础样本生成多样化表达"""
        paraphrase_samples = []
        
        for base_sample in base_samples:
            for strategy_name, strategy_prompt in self.paraphrase_strategies.items():
                for variant_id in range(variants_per_sample // len(self.paraphrase_strategies)):
                    try:
                        # 模拟LLM调用生成改写指令
                        paraphrased_instruction = await self._call_paraphrase_model(
                            base_sample.instruction, strategy_prompt
                        )
                        
                        paraphrase_sample = IntentSample(
                            intent_json=base_sample.intent_json.copy(),
                            instruction=paraphrased_instruction,
                            sample_type="paraphrase",
                            metadata={
                                "base_sample_id": base_sample.hash_id,
                                "paraphrase_strategy": strategy_name,
                                "variant_id": variant_id
                            }
                        )
                        paraphrase_samples.append(paraphrase_sample)
                        
                    except Exception as e:
                        logger.warning(f"改写样本生成失败: {e}")
                        continue
        
        logger.info(f"生成了 {len(paraphrase_samples)} 个改写样本")
        return paraphrase_samples
    
    async def _call_paraphrase_model(self, instruction: str, strategy_prompt: str) -> str:
        """调用改写模型（模拟异步LLM调用）"""
        # 这里模拟LLM调用，实际应该调用真实的模型API
        await asyncio.sleep(0.1)  # 模拟网络延迟
        
        # 简单的改写逻辑（实际应该使用LLM）
        variations = {
            "formal": f"请为{instruction.replace('给', '为').replace('加', '添加')}",
            "casual": f"帮忙{instruction.replace('添加', '加').replace('请', '')}",
            "technical": f"配置{instruction.replace('给', '对').replace('字段', '属性')}",
            "brief": instruction.replace("请", "").replace("一个", "").strip()
        }
        
        for key, variation in variations.items():
            if key in strategy_prompt.lower():
                return variation
        
        return instruction


class RobustnessGenerator:
    """鲁棒性与边界样本构建器"""
    
    def __init__(self):
        self.perturbation_strategies = [
            "entity_substitution",    # 实体替换
            "field_type_confusion",   # 字段类型混淆
            "partial_instruction",    # 部分指令
            "ambiguous_reference"     # 模糊引用
        ]
    
    def generate_adversarial_samples(self, base_samples: List[IntentSample], 
                                   ratio: float = 0.3) -> List[IntentSample]:
        """生成对抗样本"""
        adversarial_samples = []
        sample_count = int(len(base_samples) * ratio)
        
        selected_samples = random.sample(base_samples, min(sample_count, len(base_samples)))
        
        for sample in selected_samples:
            for strategy in self.perturbation_strategies:
                try:
                    perturbed_sample = self._apply_perturbation(sample, strategy)
                    if perturbed_sample:
                        adversarial_samples.append(perturbed_sample)
                except Exception as e:
                    logger.warning(f"对抗样本生成失败 {strategy}: {e}")
                    continue
        
        logger.info(f"生成了 {len(adversarial_samples)} 个对抗样本")
        return adversarial_samples
    
    def _apply_perturbation(self, sample: IntentSample, strategy: str) -> Optional[IntentSample]:
        """应用扰动策略"""
        if strategy == "entity_substitution":
            # 实体替换：用不存在的实体替换
            fake_entities = ["订购", "商品", "客人", "供货商"]
            original_entity = sample.intent_json.get("targetConceptName", "")
            new_entity = random.choice(fake_entities)
            
            perturbed_instruction = sample.instruction.replace(original_entity, new_entity)
            perturbed_intent = sample.intent_json.copy()
            perturbed_intent["targetConceptName"] = new_entity
            
        elif strategy == "field_type_confusion":
            # 字段类型混淆
            perturbed_instruction = sample.instruction.replace("字段", "属性").replace("类型", "格式")
            perturbed_intent = sample.intent_json.copy()
            
        elif strategy == "partial_instruction":
            # 部分指令：随机删除部分内容
            words = sample.instruction.split()
            if len(words) > 3:
                keep_count = random.randint(2, len(words) - 1)
                perturbed_instruction = " ".join(random.sample(words, keep_count))
            else:
                perturbed_instruction = sample.instruction
            perturbed_intent = sample.intent_json.copy()
            
        else:  # ambiguous_reference
            # 模糊引用：使用代词和模糊表达
            perturbed_instruction = sample.instruction.replace("客户表", "这个表").replace("订单", "那个")
            perturbed_intent = sample.intent_json.copy()
        
        return IntentSample(
            intent_json=perturbed_intent,
            instruction=perturbed_instruction,
            sample_type="adversarial",
            confidence=0.7,  # 对抗样本置信度较低
            metadata={
                "base_sample_id": sample.hash_id,
                "perturbation_strategy": strategy
            }
        )
    
    def generate_boundary_samples(self, base_samples: List[IntentSample], 
                                count: int = 50) -> List[IntentSample]:
        """生成边界样本（不完整或歧义的语句）"""
        boundary_samples = []
        
        incomplete_patterns = [
            "加字段",
            "删除那个",
            "修改一下",
            "客户需要",
            "订单表",
            "给它添加"
        ]
        
        for i in range(count):
            pattern = random.choice(incomplete_patterns)
            
            # 随机选择一个基础样本作为参考
            ref_sample = random.choice(base_samples)
            
            boundary_sample = IntentSample(
                intent_json={},  # 边界样本通常没有完整的意图
                instruction=pattern,
                sample_type="boundary",
                confidence=0.3,
                metadata={
                    "reference_sample_id": ref_sample.hash_id,
                    "boundary_type": "incomplete"
                }
            )
            boundary_samples.append(boundary_sample)
        
        logger.info(f"生成了 {len(boundary_samples)} 个边界样本")
        return boundary_samples
    
    def generate_negative_samples(self, count: int = 100) -> List[IntentSample]:
        """生成负样本（无效或无关指令）"""
        negative_patterns = [
            "今天天气怎么样",
            "请帮我订机票",
            "数据库连接失败",
            "系统重启中",
            "用户权限不足",
            "文件上传完成",
            "发送邮件给客户",
            "生成月度报表",
            "备份数据库",
            "清理缓存文件"
        ]
        
        negative_samples = []
        for i in range(count):
            pattern = random.choice(negative_patterns)
            
            negative_sample = IntentSample(
                intent_json={},
                instruction=pattern,
                sample_type="negative",
                confidence=0.0,
                metadata={"negative_type": "irrelevant"}
            )
            negative_samples.append(negative_sample)
        
        logger.info(f"生成了 {len(negative_samples)} 个负样本")
        return negative_samples


class AdaptiveSampler:
    """动态与自适应采样器"""
    
    def __init__(self, learning_rate: float = 0.1):
        self.error_rates = {}  # 记录各类型样本的错误率
        self.learning_rate = learning_rate
        self.difficulty_scores = {}  # 样本难度分数
        
    def record_error_rate(self, sample_type: str, error_rate: float):
        """记录样本类型的错误率"""
        self.error_rates[sample_type] = error_rate
        logger.info(f"更新 {sample_type} 错误率: {error_rate:.3f}")
    
    def get_adaptive_weights(self, base_weights: Dict[str, float]) -> Dict[str, float]:
        """基于错误率获取自适应权重"""
        adaptive_weights = base_weights.copy()
        
        for sample_type, base_weight in base_weights.items():
            error_rate = self.error_rates.get(sample_type, 0.5)
            
            # 错误率越高，权重越大
            adjustment = 1.0 + self.learning_rate * error_rate
            adaptive_weights[sample_type] = base_weight * adjustment
        
        # 归一化权重
        total_weight = sum(adaptive_weights.values())
        if total_weight > 0:
            for key in adaptive_weights:
                adaptive_weights[key] /= total_weight
        
        return adaptive_weights
    
    def mine_hard_examples(self, samples: List[IntentSample], 
                          confidence_threshold: float = 0.5) -> List[IntentSample]:
        """挖掘难例样本"""
        hard_examples = [
            sample for sample in samples 
            if sample.confidence < confidence_threshold
        ]
        
        # 按置信度排序，置信度越低越困难
        hard_examples.sort(key=lambda x: x.confidence)
        
        logger.info(f"挖掘到 {len(hard_examples)} 个难例样本")
        return hard_examples


class QualityController:
    """数据质量控制器"""
    
    def __init__(self):
        self.sentence_transformer = None  # 延迟加载
        self.seen_hashes = set()
        self.semantic_threshold = 0.85
    
    def _get_sentence_transformer(self):
        """延迟加载句子嵌入模型"""
        if self.sentence_transformer is None:
            try:
                self.sentence_transformer = SentenceTransformer('all-MiniLM-L6-v2')
            except Exception as e:
                logger.warning(f"无法加载句子嵌入模型: {e}")
                self.sentence_transformer = None
        return self.sentence_transformer
    
    def deduplicate_samples(self, samples: List[IntentSample]) -> List[IntentSample]:
        """多维度去重：哈希 + 语义相似性"""
        unique_samples = []
        
        # 第一层：哈希去重
        hash_filtered = []
        for sample in samples:
            if sample.hash_id not in self.seen_hashes:
                self.seen_hashes.add(sample.hash_id)
                hash_filtered.append(sample)
        
        logger.info(f"哈希去重后保留 {len(hash_filtered)}/{len(samples)} 个样本")
        
        # 第二层：语义去重
        if len(hash_filtered) > 1:
            unique_samples = self._semantic_deduplication(hash_filtered)
        else:
            unique_samples = hash_filtered
        
        logger.info(f"语义去重后保留 {len(unique_samples)}/{len(hash_filtered)} 个样本")
        return unique_samples
    
    def _semantic_deduplication(self, samples: List[IntentSample]) -> List[IntentSample]:
        """基于语义相似性的去重"""
        model = self._get_sentence_transformer()
        if model is None:
            return samples  # 如果模型加载失败，跳过语义去重
        
        # 计算所有指令的嵌入
        instructions = [sample.instruction for sample in samples]
        try:
            embeddings = model.encode(instructions)
            similarity_matrix = cosine_similarity(embeddings)
            
            # 标记需要移除的样本
            to_remove = set()
            for i in range(len(samples)):
                if i in to_remove:
                    continue
                for j in range(i + 1, len(samples)):
                    if j in to_remove:
                        continue
                    if similarity_matrix[i][j] > self.semantic_threshold:
                        # 保留置信度更高的样本
                        if samples[i].confidence >= samples[j].confidence:
                            to_remove.add(j)
                        else:
                            to_remove.add(i)
                            break
            
            # 返回未被标记移除的样本
            return [sample for i, sample in enumerate(samples) if i not in to_remove]
            
        except Exception as e:
            logger.warning(f"语义去重失败: {e}")
            return samples
    
    def validate_samples(self, samples: List[IntentSample]) -> Tuple[List[IntentSample], List[str]]:
        """验证样本质量"""
        valid_samples = []
        errors = []
        
        for sample in samples:
            try:
                # 基本验证
                if not sample.instruction.strip():
                    errors.append(f"空指令: {sample.hash_id}")
                    continue
                
                if sample.sample_type in ["base", "paraphrase"] and not sample.intent_json:
                    errors.append(f"缺少意图JSON: {sample.hash_id}")
                    continue
                
                # 长度验证
                if len(sample.instruction) < 2 or len(sample.instruction) > 200:
                    errors.append(f"指令长度异常: {sample.hash_id}")
                    continue
                
                valid_samples.append(sample)
                
            except Exception as e:
                errors.append(f"验证异常 {sample.hash_id}: {e}")
                
        logger.info(f"样本验证完成: {len(valid_samples)} 有效, {len(errors)} 错误")
        return valid_samples, errors


class EnhancedSamplingPipeline:
    """增强采样管道 - 整合所有组件"""
    
    def __init__(self, config_path: str = "configs/sampling_config.yaml"):
        self.config = self._load_config(config_path)
        
        # 初始化所有组件
        self.intent_generator = IntentDrivenGenerator()
        self.paraphrase_generator = ParaphraseGenerator()
        self.robustness_generator = RobustnessGenerator()
        self.adaptive_sampler = AdaptiveSampler()
        self.quality_controller = QualityController()
        
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        default_config = {
            "base_samples_count": 200,
            "paraphrase_variants": 4,
            "adversarial_ratio": 0.3,
            "boundary_samples": 50,
            "negative_samples": 100
        }
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                return {**default_config, **config}
        except Exception as e:
            logger.warning(f"配置文件加载失败，使用默认配置: {e}")
            return default_config
    
    async def run_complete_pipeline(self) -> List[IntentSample]:
        """运行完整的采样管道"""
        logger.info("🚀 启动增强采样管道")
        
        # 步骤1: 生成基础样本
        logger.info("📊 步骤1: 意图驱动的基础样本生成")
        base_samples = self.intent_generator.generate_base_samples(
            self.config["base_samples_count"]
        )
        
        # 步骤2: 生成多样化指令
        logger.info("🎭 步骤2: 多样化指令生成")
        paraphrase_samples = await self.paraphrase_generator.generate_paraphrases(
            base_samples, self.config["paraphrase_variants"]
        )
        
        # 步骤3: 生成鲁棒性样本
        logger.info("🛡️ 步骤3: 鲁棒性样本生成")
        adversarial_samples = self.robustness_generator.generate_adversarial_samples(
            base_samples, self.config["adversarial_ratio"]
        )
        boundary_samples = self.robustness_generator.generate_boundary_samples(
            base_samples, self.config["boundary_samples"]
        )
        negative_samples = self.robustness_generator.generate_negative_samples(
            self.config["negative_samples"]
        )
        
        # 步骤4: 合并所有样本
        logger.info("🔗 步骤4: 合并样本")
        all_samples = (base_samples + paraphrase_samples + adversarial_samples + 
                      boundary_samples + negative_samples)
        
        # 步骤5: 质量控制
        logger.info("🔍 步骤5: 质量控制")
        valid_samples, errors = self.quality_controller.validate_samples(all_samples)
        unique_samples = self.quality_controller.deduplicate_samples(valid_samples)
        
        # 步骤6: 自适应采样调整
        logger.info("🎯 步骤6: 自适应权重调整")
        sample_distribution = self._analyze_sample_distribution(unique_samples)
        logger.info(f"样本分布: {sample_distribution}")
        
        logger.info(f"✅ 管道完成! 生成 {len(unique_samples)} 个高质量样本")
        return unique_samples
    
    def _analyze_sample_distribution(self, samples: List[IntentSample]) -> Dict[str, int]:
        """分析样本分布"""
        distribution = {}
        for sample in samples:
            sample_type = sample.sample_type
            distribution[sample_type] = distribution.get(sample_type, 0) + 1
        return distribution
    
    def export_samples(self, samples: List[IntentSample], 
                      output_path: str = "data/enhanced_samples.json"):
        """导出样本到文件"""
        export_data = []
        for sample in samples:
            export_data.append({
                "instruction": sample.instruction,
                "intent_json": sample.intent_json,
                "sample_type": sample.sample_type,
                "confidence": sample.confidence,
                "metadata": sample.metadata,
                "hash_id": sample.hash_id
            })
        
        Path(output_path).parent.mkdir(parents=True, exist_ok=True)
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"样本已导出到: {output_path}")


# 使用示例
async def main():
    """主函数示例"""
    pipeline = EnhancedSamplingPipeline()
    
    # 运行完整管道
    samples = await pipeline.run_complete_pipeline()
    
    # 导出结果
    pipeline.export_samples(samples)
    
    # 打印统计信息
    print(f"生成样本总数: {len(samples)}")
    distribution = pipeline._analyze_sample_distribution(samples)
    for sample_type, count in distribution.items():
        print(f"  {sample_type}: {count} 个")


if __name__ == "__main__":
    asyncio.run(main())