"""
基于distilabel的配置驱动数据生成Pipeline
整合现有组件，支持反向生成和质量控制
支持千问和豆包多模型互评
"""

import yaml
import json
import asyncio
import os
from typing import List, Dict, Any, Optional, Union
from pathlib import Path
from loguru import logger

from distilabel import Pipeline, Step
from distilabel.steps import GeneratorStep, TextGeneration, KeepColumns
from distilabel.llms import InferenceEndpointsLLM, TransformersLLM, OpenAILLM
from distilabel.steps.typing import StepInput, StepOutput

# 导入现有组件
from src.core.weighted_sampler import WeightedSampler
from src.core.data_quality import DataQualityController
from src.utils.config import config_manager
from src.models.intent_models import TrainingSample, Intent
from src.core.multi_model_evaluator import MultiModelEvaluator

# 加载环境变量
from dotenv import load_dotenv
load_dotenv("configs/api_keys.env")


class ConfigDrivenIntentGenerator(GeneratorStep):
    """配置驱动的意图生成步骤 - 复用现有WeightedSampler"""
    
    def __init__(self, config_path: str = "configs/pipeline_config.yaml", **kwargs):
        super().__init__(**kwargs)
        self.config_path = config_path
        self.config = self._load_config()
        self.weighted_sampler = WeightedSampler()
        
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        with open(self.config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    @property
    def inputs(self) -> List[str]:
        return []
    
    @property 
    def outputs(self) -> List[str]:
        return ["intent_json", "entity", "field_name", "data_type", "semantic_strategy"]
    
    def process(self, offset: int = 0) -> StepOutput:
        """生成结构化意图数据"""
        # 获取配置参数
        generation_config = self.config.get("enhanced_mode", {})
        samples_per_intent = generation_config.get("seed_samples_per_intent", 100)
        
        # 使用现有的WeightedSampler进行采样
        intent_types = ["ADD_COLUMN", "DELETE_COLUMN", "MODIFY_COLUMN", "ENABLE_SOFT_DELETE"]
        
        generated_data = []
        
        for intent_type in intent_types:
            # 基于权重配置采样
            entity_weights = config_manager.get_crud_weights(f"base_object_hierarchy.entity.name")
            entities = config_manager.get_orm_entities()
            
            for _ in range(samples_per_intent):
                # 使用加权采样选择参数
                entity = self.weighted_sampler.weighted_choice(entities, [1.0] * len(entities))
                field_name = self.weighted_sampler.weighted_choice(
                    ["等级", "状态", "类型", "编号", "名称"], [1.0] * 5
                )
                data_type = self.weighted_sampler.weighted_choice(
                    config_manager.get_orm_field_types(), [1.0] * len(config_manager.get_orm_field_types())
                )
                
                # 按语义策略权重分配
                semantic_strategies = {"atomic": 35, "composite": 30, "sequence": 20, "implicit": 15}
                semantic_strategy = self.weighted_sampler.weighted_choice_dict(semantic_strategies)
                
                # 构建意图JSON
                intent_json = self._build_intent_json(intent_type, entity, field_name, data_type)
                
                generated_data.append({
                    "intent_json": json.dumps(intent_json, ensure_ascii=False),
                    "entity": entity,
                    "field_name": field_name, 
                    "data_type": data_type,
                    "semantic_strategy": semantic_strategy
                })
        
        yield generated_data
    
    def _build_intent_json(self, intent_type: str, entity: str, field_name: str, data_type: str) -> Dict[str, Any]:
        """构建标准意图JSON结构"""
        base_intent = {
            "intentType": intent_type,
            "targetConceptName": entity,
            "props": {}
        }
        
        if intent_type == "ADD_COLUMN":
            base_intent["props"] = {
                "name": field_name,
                "stdSqlType": data_type,
                "nullable": True,
                "comment": f"{field_name}字段"
            }
        elif intent_type == "DELETE_COLUMN":
            base_intent["props"] = {"name": field_name}
        elif intent_type == "MODIFY_COLUMN":
            base_intent["props"] = {
                "name": field_name,
                "stdSqlType": data_type,
                "nullable": False
            }
        elif intent_type == "ENABLE_SOFT_DELETE":
            base_intent["props"] = {
                "deleteField": "is_deleted",
                "deleteFieldType": "BOOLEAN"
            }
        
        return base_intent


class IntentToInstructionConverter(TextGeneration):
    """意图转自然语言指令 - 反向生成步骤"""
    
    def __init__(self, llm: Any, **kwargs):
        super().__init__(llm=llm, **kwargs)
        self.system_prompt = """你是一个专业的数据建模专家。请将给定的JSON意图转换为自然、多样化的中文指令。

要求：
1. 生成的指令要符合用户的自然表达习惯
2. 根据语义策略调整表达方式：
   - atomic: 使用直接、简洁的表达
   - composite: 可以包含多个操作描述  
   - sequence: 体现步骤性、顺序性
   - implicit: 更口语化，可能省略部分信息
3. 保持语义准确，不添加原意图中没有的信息
4. 一次只生成一个指令"""
    
    @property
    def inputs(self) -> List[str]:
        return ["intent_json", "entity", "field_name", "semantic_strategy"]
    
    @property
    def outputs(self) -> List[str]:
        return ["instruction", "model_name"]
    
    def format_input(self, input: Dict[str, Any]) -> List[Dict[str, Any]]:
        """格式化输入为LLM提示"""
        intent_json = input["intent_json"]
        entity = input["entity"] 
        field_name = input["field_name"]
        semantic_strategy = input["semantic_strategy"]
        
        user_prompt = f"""请将以下JSON意图转换为自然语言指令：

JSON意图：{intent_json}

实体：{entity}
字段名：{field_name} 
语义策略：{semantic_strategy}

请生成一个符合{semantic_strategy}策略的自然语言指令："""
        
        return [{"role": "system", "content": self.system_prompt},
                {"role": "user", "content": user_prompt}]


class CustomGEvalStep(Step):
    """自定义G-Eval步骤 - 使用豆包LLM作为评判模型进行互评"""
    
    def __init__(self, evaluator_llm: Any, criteria: List[Dict[str, str]], **kwargs):
        super().__init__(**kwargs)
        self.evaluator_llm = evaluator_llm
        self.criteria = criteria
        self.g_eval_prompt_template = """你是一个专业的数据质量评估专家。请根据以下标准对自然语言指令和JSON意图的匹配质量进行评分。

原始JSON意图：
{intent_json}

生成的自然语言指令：
{instruction}

评估标准：
{criteria_str}

请为每个维度提供1-5分的评分（5分最高），并简要说明理由。

输出格式（严格按照JSON格式）：
{{
  "semantic_consistency": {{
    "score": 分数,
    "reason": "评分理由"
  }},
  "expression_naturalness": {{
    "score": 分数, 
    "reason": "评分理由"
  }},
  "ambiguity_level": {{
    "score": 分数,
    "reason": "评分理由"
  }},
  "overall_quality": {{
    "score": 分数,
    "reason": "综合评价"
  }}
}}"""
    
    @property
    def inputs(self) -> List[str]:
        return ["instruction", "intent_json", "entity", "field_name"]
    
    @property
    def outputs(self) -> List[str]:
        return ["instruction", "intent_json", "entity", "field_name", 
                "g_eval_scores", "g_eval_reasons", "overall_score"]
    
    async def process(self, inputs: StepInput) -> StepOutput:
        """处理G-Eval评估"""
        processed_data = []
        
        # 格式化评估标准
        criteria_str = "\n".join([
            f"• {c['name']}: {c['description']}" 
            for c in self.criteria
        ])
        
        for input_batch in inputs:
            batch_results = []
            
            for item in input_batch:
                try:
                    # 构建评估提示
                    prompt = self.g_eval_prompt_template.format(
                        instruction=item["instruction"],
                        intent_json=item["intent_json"], 
                        criteria_str=criteria_str
                    )
                    
                    # 调用豆包LLM进行评估
                    messages = [{"role": "user", "content": prompt}]
                    llm_response = await self._call_evaluator_llm(messages)
                    
                    # 解析G-Eval结果
                    g_eval_results = self._parse_g_eval_response(llm_response)
                    
                    # 计算综合分数
                    overall_score = self._calculate_overall_score(g_eval_results)
                    
                    # 构建输出数据
                    result_item = {
                        **item,
                        "g_eval_scores": {k: v["score"] for k, v in g_eval_results.items()},
                        "g_eval_reasons": {k: v["reason"] for k, v in g_eval_results.items()},
                        "overall_score": overall_score
                    }
                    
                    batch_results.append(result_item)
                    
                except Exception as e:
                    # 错误处理：提供默认分数
                    logger.warning(f"G-Eval评估失败: {e}")
                    fallback_item = {
                        **item,
                        "g_eval_scores": {c["name"]: 1 for c in self.criteria},
                        "g_eval_reasons": {c["name"]: f"评估失败: {str(e)}" for c in self.criteria},
                        "overall_score": 1.0
                    }
                    batch_results.append(fallback_item)
            
            processed_data.extend(batch_results)
        
        yield processed_data
    
    async def _call_evaluator_llm(self, messages: List[Dict[str, str]]) -> str:
        """调用评估LLM"""
        try:
            # 根据不同的LLM类型调用
            if hasattr(self.evaluator_llm, 'generate'):
                response = await self.evaluator_llm.generate([messages])
                return response[0].generations[0].text
            else:
                # 其他LLM接口
                response = await self.evaluator_llm.agenerate([messages])
                return response.generations[0][0].text
        except Exception as e:
            logger.error(f"LLM调用失败: {e}")
            raise
    
    def _parse_g_eval_response(self, response: str) -> Dict[str, Dict[str, Any]]:
        """解析G-Eval响应"""
        try:
            # 清理响应文本
            response = response.strip()
            if response.startswith("```json"):
                response = response.replace("```json", "").replace("```", "").strip()
            
            # 尝试解析JSON
            parsed_result = json.loads(response)
            
            # 验证结果结构
            for criterion in self.criteria:
                criterion_name = criterion["name"]
                if criterion_name not in parsed_result:
                    parsed_result[criterion_name] = {"score": 1, "reason": "解析错误"}
                elif not isinstance(parsed_result[criterion_name], dict):
                    parsed_result[criterion_name] = {"score": 1, "reason": "格式错误"}
                else:
                    # 确保分数在合理范围内
                    score = parsed_result[criterion_name].get("score", 1)
                    if not isinstance(score, (int, float)) or score < 1 or score > 5:
                        parsed_result[criterion_name]["score"] = 1
            
            return parsed_result
            
        except json.JSONDecodeError as e:
            logger.warning(f"JSON解析失败: {e}")
            # 返回默认分数
            return {
                c["name"]: {"score": 1, "reason": f"JSON解析失败: {str(e)}"} 
                for c in self.criteria
            }
    
    def _calculate_overall_score(self, g_eval_results: Dict[str, Dict[str, Any]]) -> float:
        """计算综合评分"""
        scores = [result["score"] for result in g_eval_results.values()]
        if not scores:
            return 1.0
        
        # 加权平均（可以根据需要调整权重）
        weights = {
            "semantic_consistency": 0.4,  # 语义一致性最重要
            "expression_naturalness": 0.3,  # 表达自然度
            "ambiguity_level": 0.2,       # 歧义程度
            "overall_quality": 0.1        # 综合质量
        }
        
        weighted_sum = 0.0
        total_weight = 0.0
        
        for criterion_name, result in g_eval_results.items():
            weight = weights.get(criterion_name, 0.1)
            weighted_sum += result["score"] * weight
            total_weight += weight
        
        return weighted_sum / total_weight if total_weight > 0 else 1.0


class MultiModelInstructionValidator(Step):
    """多模型指令验证步骤 - 结合G-Eval分数和一致性检查"""
    
    def __init__(self, validator_llm: Any, min_consistency_score: float = 0.7, 
                 min_g_eval_score: float = 3.0, **kwargs):
        super().__init__(**kwargs)
        self.validator_llm = validator_llm
        self.min_consistency_score = min_consistency_score
        self.min_g_eval_score = min_g_eval_score
        
        self.validation_prompt = """请将以下自然语言指令解析为JSON意图格式：

指令：{instruction}

请严格按照以下格式输出JSON，不要添加其他文字：
{{
  "intentType": "ADD_COLUMN/DELETE_COLUMN/MODIFY_COLUMN/ENABLE_SOFT_DELETE",
  "targetConceptName": "实体名",
  "props": {{...}}
}}"""
    
    @property
    def inputs(self) -> List[str]:
        return ["instruction", "intent_json", "g_eval_scores", "overall_score"]
    
    @property
    def outputs(self) -> List[str]:
        return ["instruction", "intent_json", "consistency_score", 
                "g_eval_scores", "overall_score", "validation_passed"]
    
    async def process(self, inputs: StepInput) -> StepOutput:
        """处理多模型验证"""
        processed_data = []
        
        for input_batch in inputs:
            for item in input_batch:
                try:
                    # 使用Qwen进行正向验证
                    validation_prompt = self.validation_prompt.format(
                        instruction=item["instruction"]
                    )
                    
                    messages = [{"role": "user", "content": validation_prompt}]
                    parsed_response = await self._call_validator_llm(messages)
                    
                    # 计算一致性分数
                    consistency_score = self._calculate_consistency(
                        parsed_response, item["intent_json"]
                    )
                    
                    # 综合评判是否通过验证
                    validation_passed = self._should_pass_validation(
                        consistency_score, item.get("overall_score", 1.0)
                    )
                    
                    result_item = {
                        **item,
                        "consistency_score": consistency_score,
                        "validation_passed": validation_passed
                    }
                    
                    processed_data.append(result_item)
                    
                except Exception as e:
                    logger.warning(f"验证失败: {e}")
                    # 默认不通过验证
                    result_item = {
                        **item,
                        "consistency_score": 0.0,
                        "validation_passed": False
                    }
                    processed_data.append(result_item)
        
        yield processed_data
    
    async def _call_validator_llm(self, messages: List[Dict[str, str]]) -> str:
        """调用验证LLM"""
        try:
            if hasattr(self.validator_llm, 'generate'):
                response = await self.validator_llm.generate([messages])
                return response[0].generations[0].text
            else:
                response = await self.validator_llm.agenerate([messages])
                return response.generations[0][0].text
        except Exception as e:
            logger.error(f"验证LLM调用失败: {e}")
            raise
    
    def _calculate_consistency(self, parsed_intent: str, original_intent: str) -> float:
        """计算一致性分数"""
        try:
            # 清理和解析JSON
            parsed_intent = parsed_intent.strip()
            if parsed_intent.startswith("```json"):
                parsed_intent = parsed_intent.replace("```json", "").replace("```", "").strip()
            
            parsed_json = json.loads(parsed_intent)
            original_json = json.loads(original_intent)
            
            score = 0.0
            
            # 检查意图类型 (40%权重)
            if parsed_json.get("intentType") == original_json.get("intentType"):
                score += 0.4
            
            # 检查目标实体 (30%权重)
            if parsed_json.get("targetConceptName") == original_json.get("targetConceptName"):
                score += 0.3
            
            # 检查属性匹配 (30%权重)
            parsed_props = parsed_json.get("props", {})
            original_props = original_json.get("props", {})
            
            if parsed_props and original_props:
                matching_keys = set(parsed_props.keys()) & set(original_props.keys())
                total_keys = set(parsed_props.keys()) | set(original_props.keys())
                
                if total_keys:
                    props_score = len(matching_keys) / len(total_keys)
                    score += 0.3 * props_score
            
            return min(1.0, score)
            
        except Exception as e:
            logger.warning(f"一致性计算失败: {e}")
            return 0.0
    
    def _should_pass_validation(self, consistency_score: float, g_eval_score: float) -> bool:
        """判断是否通过验证"""
        return (consistency_score >= self.min_consistency_score and 
                g_eval_score >= self.min_g_eval_score)
    """指令验证步骤 - 正向验证生成的指令质量"""
    
    def __init__(self, llm: Any, **kwargs):
        super().__init__(llm=llm, **kwargs)
        self.system_prompt = """你是一个专业的意图解析专家。请将给定的自然语言指令解析为标准的JSON意图格式。

请严格按照以下格式输出JSON：
{
  "intentType": "ADD_COLUMN/DELETE_COLUMN/MODIFY_COLUMN/ENABLE_SOFT_DELETE",
  "targetConceptName": "实体名",
  "props": {...}
}

只输出JSON，不要添加其他解释文字。"""
    
    @property  
    def inputs(self) -> List[str]:
        return ["instruction", "intent_json"]
    
    @property
    def outputs(self) -> List[str]:
        return ["parsed_intent", "consistency_score"]
    
    def format_input(self, input: Dict[str, Any]) -> List[Dict[str, Any]]:
        """格式化验证提示"""
        instruction = input["instruction"]
        
        user_prompt = f"""请解析以下自然语言指令为JSON意图：

指令：{instruction}

请输出对应的JSON意图："""
        
        return [{"role": "system", "content": self.system_prompt},
                {"role": "user", "content": user_prompt}]
    
    def format_output(self, output: Union[str, None], input: Dict[str, Any]) -> Dict[str, Any]:
        """格式化输出并计算一致性分数"""
        if output is None:
            return {"parsed_intent": "", "consistency_score": 0.0}
        
        try:
            # 提取JSON
            parsed_intent = output.strip()
            if parsed_intent.startswith("```json"):
                parsed_intent = parsed_intent.replace("```json", "").replace("```", "").strip()
            
            # 验证JSON格式
            parsed_json = json.loads(parsed_intent)
            original_json = json.loads(input["intent_json"])
            
            # 计算一致性分数
            consistency_score = self._calculate_consistency(parsed_json, original_json)
            
            return {
                "parsed_intent": parsed_intent,
                "consistency_score": consistency_score
            }
            
        except Exception as e:
            return {"parsed_intent": output or "", "consistency_score": 0.0}
    
    def _calculate_consistency(self, parsed: Dict[str, Any], original: Dict[str, Any]) -> float:
        """计算解析结果与原始意图的一致性分数"""
        score = 0.0
        
        # 检查意图类型
        if parsed.get("intentType") == original.get("intentType"):
            score += 0.4
        
        # 检查目标实体
        if parsed.get("targetConceptName") == original.get("targetConceptName"):
            score += 0.3
        
        # 检查属性
        parsed_props = parsed.get("props", {})
        original_props = original.get("props", {})
        
        if parsed_props and original_props:
            matching_keys = set(parsed_props.keys()) & set(original_props.keys())
            total_keys = set(parsed_props.keys()) | set(original_props.keys())
            
            if total_keys:
                props_score = len(matching_keys) / len(total_keys)
                score += 0.3 * props_score
        
        return min(1.0, score)


class CustomDataQualityStep(Step):
    """自定义数据质量控制步骤 - 复用现有质量控制组件"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.quality_controller = DataQualityController()
    
    @property
    def inputs(self) -> List[str]:
        return ["instruction", "intent_json", "consistency_score"]
    
    @property
    def outputs(self) -> List[str]:
        return ["instruction", "intent_json", "consistency_score", "quality_score", "is_valid"]
    
    async def process(self, inputs: StepInput) -> StepOutput:
        """处理数据质量控制"""
        # 初始化质量控制器
        await self.quality_controller.initialize()
        
        processed_data = []
        
        for input_batch in inputs:
            # 转换为TrainingSample格式
            samples = []
            for item in input_batch:
                try:
                    intent_data = json.loads(item["intent_json"])
                    intent = Intent(**intent_data)
                    
                    sample = TrainingSample(
                        instruction=item["instruction"],
                        output=[intent],
                        metadata={
                            "consistency_score": item["consistency_score"],
                            "generation_method": "distilabel"
                        }
                    )
                    samples.append(sample)
                except Exception as e:
                    continue
            
            # 执行质量控制
            valid_samples, validation_errors = self.quality_controller.validate_samples(samples)
            quality_metrics = self.quality_controller.assess_quality(valid_samples)
            
            # 转换回distilabel格式
            for i, sample in enumerate(samples):
                is_valid = sample in valid_samples
                quality_score = quality_metrics.expression_diversity if is_valid else 0.0
                
                processed_item = {
                    "instruction": sample.instruction,
                    "intent_json": json.dumps(sample.output[0].dict() if sample.output else {}),
                    "consistency_score": sample.metadata.get("consistency_score", 0.0),
                    "quality_score": quality_score,
                    "is_valid": is_valid
                }
                processed_data.append(processed_item)
        
        yield processed_data


def create_multi_model_pipeline(config_path: str = "configs/distilabel_config.yaml") -> Pipeline:
    """创建多模型互评的distilabel Pipeline"""
    
    # 加载配置
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    logger.info("🚀 创建多模型互评Pipeline...")
    
    # 配置主生成模型（通义千问）
    primary_model_config = config.get("distilabel", {}).get("primary_model", {})
    qwen_llm = _create_llm_instance(primary_model_config, "Qwen")
    
    # 配置评估模型（豆包）
    evaluator_config = config.get("g_eval", {}).get("evaluator_model", {})
    doubao_llm = _create_llm_instance(evaluator_config, "Doubao")
    
    # 获取G-Eval标准
    g_eval_criteria = config.get("g_eval", {}).get("criteria", [])
    
    # 获取质量阈值
    quality_thresholds = config.get("g_eval", {}).get("quality_thresholds", {})
    
    logger.info(f"✅ 模型配置完成:")
    logger.info(f"  - 生成模型: {primary_model_config.get('name', 'Unknown')}")
    logger.info(f"  - 评估模型: {evaluator_config.get('name', 'Unknown')}")
    logger.info(f"  - G-Eval标准数: {len(g_eval_criteria)}")
    
    # 创建Pipeline
    with Pipeline(name="MultiModelIntentGeneration") as pipeline:
        # Step 1: 配置驱动的意图生成
        intent_generator = ConfigDrivenIntentGenerator(
            name="intent_generator",
            config_path=config_path
        )
        
        # Step 2: 意图转指令（反向生成）- 使用通义千问
        instruction_converter = IntentToInstructionConverter(
            name="instruction_converter", 
            llm=qwen_llm
        )
        
        # Step 3: G-Eval大模型互评 - 使用豆包作为评判者
        g_eval_step = CustomGEvalStep(
            name="g_eval_step",
            evaluator_llm=doubao_llm,
            criteria=g_eval_criteria
        )
        
        # Step 4: 多模型验证 - 使用通义千问进行正向验证
        multi_validator = MultiModelInstructionValidator(
            name="multi_validator",
            validator_llm=qwen_llm,
            min_consistency_score=quality_thresholds.get("min_consistency_score", 0.8),
            min_g_eval_score=quality_thresholds.get("min_overall_score", 3.5)
        )
        
        # Step 5: 增强的数据质量控制
        enhanced_quality_controller = EnhancedDataQualityStep(
            name="enhanced_quality_controller",
            config=config.get("quality_control", {})
        )
        
        # Step 6: 高质量数据筛选
        quality_filter = KeepColumns(
            name="quality_filter",
            columns=["instruction", "intent_json", "g_eval_scores", "overall_score", 
                    "consistency_score", "validation_passed"],
            condition=lambda x: (
                x.get("validation_passed", False) and
                x.get("overall_score", 0) >= quality_thresholds.get("min_overall_score", 3.5) and
                x.get("g_eval_scores", {}).get("semantic_consistency", 0) >= quality_thresholds.get("min_semantic_consistency", 4.0)
            )
        )
        
        # 构建多模型Pipeline流程
        intent_generator >> instruction_converter >> g_eval_step >> multi_validator >> enhanced_quality_controller >> quality_filter
    
    logger.info("✅ 多模型互评Pipeline创建完成")
    return pipeline


def _create_llm_instance(model_config: Dict[str, Any], model_name: str) -> Any:
    """创建LLM实例的辅助函数"""
    model_type = model_config.get("type", "transformers")
    model_id = model_config.get("name", "")
    generation_kwargs = model_config.get("generation_kwargs", {})
    
    logger.info(f"🔧 创建{model_name}模型实例: {model_id}")
    
    try:
        if model_type == "transformers":
            return TransformersLLM(
                model=model_id,
                device="auto",
                torch_dtype=model_config.get("torch_dtype", "float16"),
                trust_remote_code=model_config.get("trust_remote_code", True),
                generation_kwargs=generation_kwargs
            )
        
        elif model_type == "inference_endpoints":
            # 豆包或其他API模型
            api_key = model_config.get("api_key", "")
            if api_key.startswith("${") and api_key.endswith("}"):
                # 从环境变量读取
                env_var = api_key[2:-1]
                api_key = os.getenv(env_var, "")
            
            return InferenceEndpointsLLM(
                model_id=model_id,
                api_key=api_key,
                base_url=model_config.get("api_endpoint"),
                generation_kwargs=generation_kwargs
            )
        
        elif model_type == "openai":
            return OpenAILLM(
                model=model_id,
                api_key=model_config.get("api_key", ""),
                generation_kwargs=generation_kwargs
            )
        
        else:
            logger.warning(f"未知模型类型: {model_type}，使用默认TransformersLLM")
            return TransformersLLM(model=model_id, generation_kwargs=generation_kwargs)
    
    except Exception as e:
        logger.error(f"创建{model_name}模型实例失败: {e}")
        raise


class EnhancedDataQualityStep(Step):
    """增强的数据质量控制步骤 - 整合G-Eval分数和传统质量控制"""
    
    def __init__(self, config: Dict[str, Any], **kwargs):
        super().__init__(**kwargs)
        self.config = config
        self.scoring_strategy = config.get("scoring_strategy", {})
        
        # 初始化现有的质量控制器
        self.quality_controller = DataQualityController()
    
    @property
    def inputs(self) -> List[str]:
        return ["instruction", "intent_json", "consistency_score", 
                "g_eval_scores", "overall_score", "validation_passed"]
    
    @property
    def outputs(self) -> List[str]:
        return ["instruction", "intent_json", "consistency_score", 
                "g_eval_scores", "overall_score", "validation_passed",
                "composite_quality_score", "quality_rank"]
    
    async def process(self, inputs: StepInput) -> StepOutput:
        """处理增强的质量控制"""
        # 初始化质量控制器
        await self.quality_controller.initialize()
        
        processed_data = []
        
        for input_batch in inputs:
            # 转换为TrainingSample格式进行质量评估
            samples = []
            batch_data = []
            
            for item in input_batch:
                try:
                    # 构建TrainingSample
                    intent_data = json.loads(item["intent_json"])
                    intent = Intent(**intent_data)
                    
                    sample = TrainingSample(
                        instruction=item["instruction"],
                        output=[intent],
                        metadata={
                            "consistency_score": item.get("consistency_score", 0.0),
                            "g_eval_scores": item.get("g_eval_scores", {}),
                            "overall_score": item.get("overall_score", 0.0),
                            "validation_passed": item.get("validation_passed", False)
                        }
                    )
                    
                    samples.append(sample)
                    batch_data.append(item)
                    
                except Exception as e:
                    logger.warning(f"数据转换失败: {e}")
                    continue
            
            # 执行传统质量控制
            if samples:
                valid_samples, validation_errors = self.quality_controller.validate_samples(samples)
                quality_metrics = self.quality_controller.assess_quality(valid_samples)
                
                logger.info(f"质量评估完成: {len(valid_samples)}/{len(samples)} 样本通过验证")
            
            # 计算复合质量分数
            for i, item in enumerate(batch_data):
                try:
                    composite_score = self._calculate_composite_quality_score(item)
                    quality_rank = self._determine_quality_rank(composite_score)
                    
                    enhanced_item = {
                        **item,
                        "composite_quality_score": composite_score,
                        "quality_rank": quality_rank
                    }
                    
                    processed_data.append(enhanced_item)
                    
                except Exception as e:
                    logger.warning(f"复合质量分数计算失败: {e}")
                    # 提供默认值
                    enhanced_item = {
                        **item,
                        "composite_quality_score": 0.0,
                        "quality_rank": "low"
                    }
                    processed_data.append(enhanced_item)
        
        yield processed_data
    
    def _calculate_composite_quality_score(self, item: Dict[str, Any]) -> float:
        """计算复合质量分数"""
        # 获取各项分数
        consistency_score = item.get("consistency_score", 0.0)
        overall_g_eval_score = item.get("overall_score", 0.0)
        validation_passed = item.get("validation_passed", False)
        
        # 获取权重配置
        consistency_weight = self.scoring_strategy.get("consistency_weight", 0.4)
        g_eval_weight = self.scoring_strategy.get("g_eval_weight", 0.5)
        structure_weight = self.scoring_strategy.get("structure_weight", 0.1)
        
        # 结构分数（基于验证通过情况）
        structure_score = 1.0 if validation_passed else 0.0
        
        # 标准化G-Eval分数（从1-5转换到0-1）
        normalized_g_eval = max(0.0, (overall_g_eval_score - 1.0) / 4.0)
        
        # 计算加权复合分数
        composite_score = (
            consistency_score * consistency_weight +
            normalized_g_eval * g_eval_weight +
            structure_score * structure_weight
        )
        
        return min(1.0, max(0.0, composite_score))
    
    def _determine_quality_rank(self, composite_score: float) -> str:
        """确定质量等级"""
        if composite_score >= 0.8:
            return "excellent"
        elif composite_score >= 0.6:
            return "good"
        elif composite_score >= 0.4:
            return "fair"
        else:
            return "poor"


async def run_multi_model_pipeline(config_path: str = "configs/distilabel_config.yaml",
                                  output_path: str = "data/processed/multi_model_output") -> Any:
    """运行多模型互评的数据生成Pipeline"""
    
    logger.info("🚀 启动多模型互评Pipeline...")
    
    try:
        # 创建Pipeline
        pipeline = create_multi_model_pipeline(config_path)
        
        # 执行Pipeline
        logger.info("🔄 开始执行多模型Pipeline...")
        start_time = datetime.now()
        
        distiset = pipeline.run(use_cache=False)
        
        # 保存结果
        output_path_with_timestamp = f"{output_path}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        distiset.save_to_disk(output_path_with_timestamp)
        
        # 计算执行时间
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # 生成执行报告
        execution_report = {
            "pipeline_type": "multi_model_evaluation",
            "execution_time_seconds": duration,
            "output_path": output_path_with_timestamp,
            "timestamp": end_time.isoformat(),
            "model_configuration": {
                "generator": "Qwen",
                "evaluator": "Doubao", 
                "validator": "Qwen"
            }
        }
        
        # 保存执行报告
        report_path = f"{output_path_with_timestamp}/execution_report.json"
        os.makedirs(os.path.dirname(report_path), exist_ok=True)
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(execution_report, f, ensure_ascii=False, indent=2)
        
        logger.info("✅ 多模型Pipeline执行完成！")
        logger.info(f"⏱️  执行时间: {duration:.2f} 秒")
        logger.info(f"📁 输出路径: {output_path_with_timestamp}")
        
        return distiset
        
    except Exception as e:
        logger.error(f"❌ 多模型Pipeline执行失败: {e}")
        raise


# 更新原有的create_pipeline函数，保持向后兼容
def create_pipeline(config_path: str = "configs/pipeline_config.yaml") -> Pipeline:
    """创建Pipeline - 自动检测配置类型"""
    
    # 读取配置检查模式
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    pipeline_mode = config.get("pipeline", {}).get("mode", "distilabel")
    
    if pipeline_mode == "distilabel_multi_model":
        logger.info("检测到多模型配置，创建多模型互评Pipeline")
        return create_multi_model_pipeline(config_path)
    else:
        logger.info("使用标准单模型Pipeline")
        return create_single_model_pipeline(config_path)


def create_single_model_pipeline(config_path: str) -> Pipeline:
    """创建单模型Pipeline（原有逻辑保持不变）"""
    # 保持原有的单模型Pipeline逻辑
    # 这里可以复用之前的代码...
    """创建配置驱动的distilabel Pipeline"""
    
    # 加载配置
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 配置LLM
    model_config = config.get("model", {})
    model_name = model_config.get("name", "Qwen/Qwen2.5-7B-Instruct")
    
    # 根据配置选择LLM类型
    if "qwen" in model_name.lower():
        llm = TransformersLLM(
            model=model_name,
            device="auto",
            generation_kwargs={
                "max_new_tokens": 512,
                "temperature": 0.7,
                "do_sample": True
            }
        )
    else:
        llm = InferenceEndpointsLLM(
            model_id=model_name,
            generation_kwargs={"max_new_tokens": 512, "temperature": 0.7}
        )
    
    # 创建Pipeline
    with Pipeline(name="ConfigDrivenIntentGeneration") as pipeline:
        # Step 1: 配置驱动的意图生成
        intent_generator = ConfigDrivenIntentGenerator(
            name="intent_generator",
            config_path=config_path
        )
        
        # Step 2: 意图转指令（反向生成）
        instruction_converter = IntentToInstructionConverter(
            name="instruction_converter", 
            llm=llm
        )
        
        # Step 3: 指令验证（正向验证）
        instruction_validator = InstructionValidator(
            name="instruction_validator",
            llm=llm
        )
        
        # Step 4: 数据质量控制
        quality_controller = CustomDataQualityStep(
            name="quality_controller"
        )
        
        # Step 5: 保留有效数据
        keep_valid = KeepColumns(
            name="keep_valid",
            columns=["instruction", "intent_json", "quality_score"],
            condition=lambda x: x.get("is_valid", False) and x.get("consistency_score", 0) > 0.7
        )
        
        # 构建Pipeline流程
        intent_generator >> instruction_converter >> instruction_validator >> quality_controller >> keep_valid
    
    return pipeline


async def run_pipeline(config_path: str = "configs/pipeline_config.yaml", 
                      output_path: str = "data/processed/distilabel_output.json"):
    """运行配置驱动的数据生成Pipeline"""
    
    # 创建Pipeline
    pipeline = create_pipeline(config_path)
    
    # 执行Pipeline
    distiset = pipeline.run(use_cache=False)
    
    # 保存结果
    distiset.save_to_disk(output_path)
    
    print(f"Pipeline执行完成，结果保存到: {output_path}")
    return distiset


if __name__ == "__main__":
    # 运行示例
    asyncio.run(run_pipeline())