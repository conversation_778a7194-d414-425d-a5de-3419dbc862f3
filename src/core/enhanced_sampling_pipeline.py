"""
增强采样管道模块
基于您现有的完整实现，遵循KISS、YAGNI和SOLID原则
专注于采样逻辑，便于集成到distilabel系统中
"""

import json
import yaml
import random
import hashlib
import time
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass, field
from pathlib import Path
from loguru import logger
from collections import defaultdict

# 导入现有组件 - 优雅的错误处理
try:
    from src.core.weighted_sampler import EnhancedSamplingAdapter, WeightedSampler
    ENHANCED_SAMPLER_AVAILABLE = True
except ImportError:
    logger.warning("增强采样器不可用，使用基础实现")
    ENHANCED_SAMPLER_AVAILABLE = False
    
    class WeightedSampler:
        @staticmethod
        def weighted_choice(choices, weights):
            return random.choice(choices) if choices else ""

try:
    from src.models.intent_models import TrainingSample, Intent
    INTENT_MODELS_AVAILABLE = True
except ImportError:
    logger.warning("Intent模型不可用，使用简化版本")
    INTENT_MODELS_AVAILABLE = False
    
    @dataclass
    class Intent:
        intentType: str = ""
        targetConceptName: str = ""
        props: Dict[str, Any] = field(default_factory=dict)
    
    @dataclass 
    class TrainingSample:
        instruction: str = ""
        output: List[Intent] = field(default_factory=list)
        metadata: Dict[str, Any] = field(default_factory=dict)


# ==================== 核心数据结构 - 单一职责 ====================

@dataclass
class IntentSample:
    """意图样本数据结构 - 保持简单且职责单一"""
    intent_json: Dict[str, Any]
    instruction: str
    sample_type: str  # base, paraphrase, adversarial, boundary, negative
    confidence: float = 1.0
    metadata: Dict[str, Any] = field(default_factory=dict)
    hash_id: str = field(default="")
    quality_score: float = 1.0
    
    def __post_init__(self):
        """生成唯一标识符"""
        if not self.hash_id:
            content = f"{json.dumps(self.intent_json, sort_keys=True)}_{self.instruction}"
            self.hash_id = hashlib.md5(content.encode()).hexdigest()
    
    def to_training_sample(self) -> TrainingSample:
        """转换为TrainingSample格式"""
        intent_obj = Intent(
            intentType=self.intent_json.get("intentType", ""),
            targetConceptName=self.intent_json.get("targetConceptName", ""),
            props=self.intent_json.get("props", {})
        ) if self.intent_json else None
        
        return TrainingSample(
            instruction=self.instruction,
            output=[intent_obj] if intent_obj else [],
            metadata={
                **self.metadata,
                "sample_type": self.sample_type,
                "confidence": self.confidence,
                "quality_score": self.quality_score,
                "hash_id": self.hash_id
            }
        )


@dataclass
class SamplingStatistics:
    """采样统计信息 - 专注统计职责"""
    total_samples: int = 0
    samples_by_type: Dict[str, int] = field(default_factory=dict)
    generation_time: float = 0.0
    success_rate: float = 1.0
    error_count: int = 0
    
    def add_sample(self, sample: IntentSample):
        """添加样本统计"""
        self.total_samples += 1
        self.samples_by_type[sample.sample_type] = self.samples_by_type.get(sample.sample_type, 0) + 1
    
    def add_error(self):
        """记录错误"""
        self.error_count += 1
        self.success_rate = max(0.0, 1.0 - (self.error_count / max(1, self.total_samples + self.error_count)))
    
    def get_summary(self) -> Dict[str, Any]:
        """获取统计摘要"""
        return {
            "total_samples": self.total_samples,
            "samples_by_type": dict(self.samples_by_type),
            "generation_time": self.generation_time,
            "success_rate": self.success_rate,
            "error_count": self.error_count
        }


# ==================== 基础生成器接口 - 开闭原则 ====================

class BaseGenerator:
    """基础生成器抽象类 - 便于扩展"""
    
    def generate(self, count: int) -> List[IntentSample]:
        """生成样本 - 子类必须实现"""
        raise NotImplementedError
    
    def validate_sample(self, sample: IntentSample) -> bool:
        """基础样本验证"""
        return (
            sample.instruction.strip() != "" and
            sample.confidence >= 0.1 and
            len(sample.instruction) >= 2
        )


# ==================== 具体生成器实现 ====================

class IntentDrivenGenerator(BaseGenerator):
    """意图驱动生成器 - 单一职责：基础意图生成"""
    
    def __init__(self, orm_config_path: str = "configs/orm_def.yaml"):
        self.weighted_sampler = WeightedSampler()
        self.orm_config = self._load_orm_config(orm_config_path)
        self.entity_field_cache = {}
        
    def _load_orm_config(self, config_path: str) -> Dict[str, Any]:
        """加载ORM配置 - KISS原则"""
        try:
            if Path(config_path).exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    return yaml.safe_load(f)
        except Exception as e:
            logger.warning(f"ORM配置加载失败: {e}")
        
        # 默认配置 - YAGNI原则，只包含必需的
        return {
            "entities": ["客户", "订单", "产品", "用户", "员工"],
            "field_types": ["VARCHAR(100)", "INT", "DATETIME", "BOOLEAN"],
            "operations": ["ADD_COLUMN", "DELETE_COLUMN", "MODIFY_COLUMN"]
        }
    
    def generate(self, count: int) -> List[IntentSample]:
        """生成基础意图样本"""
        samples = []
        entities = self.orm_config.get("entities", [])
        operations = self.orm_config.get("operations", [])
        field_types = self.orm_config.get("field_types", [])
        
        for i in range(count):
            try:
                # 循环选择确保均匀分布
                entity = entities[i % len(entities)]
                operation = operations[i % len(operations)]
                field_type = field_types[i % len(field_types)]
                
                # 生成字段名
                field_name = self._generate_field_name(entity, operation, i)
                
                # 构建意图JSON
                intent_json = self._build_intent_json(operation, entity, field_name, field_type)
                
                # 生成指令
                instruction = self._generate_instruction(operation, entity, field_name)
                
                sample = IntentSample(
                    intent_json=intent_json,
                    instruction=instruction,
                    sample_type="base",
                    confidence=1.0,
                    metadata={
                        "entity": entity,
                        "operation": operation,
                        "field_name": field_name,
                        "generation_index": i
                    }
                )
                
                if self.validate_sample(sample):
                    samples.append(sample)
                    
            except Exception as e:
                logger.warning(f"生成基础样本{i}失败: {e}")
                continue
        
        logger.info(f"生成了 {len(samples)} 个基础样本")
        return samples
    
    def _generate_field_name(self, entity: str, operation: str, index: int) -> str:
        """生成字段名 - 简单有效"""
        cache_key = entity
        if cache_key not in self.entity_field_cache:
            field_suggestions = {
                "客户": ["姓名", "电话", "地址", "等级", "状态", "类型"],
                "订单": ["状态", "金额", "数量", "时间", "备注", "优先级"],
                "产品": ["名称", "价格", "类别", "库存", "描述", "规格"],
                "用户": ["用户名", "密码", "邮箱", "角色", "状态", "权限"],
                "员工": ["姓名", "部门", "职位", "工号", "工资", "状态"]
            }
            self.entity_field_cache[cache_key] = field_suggestions.get(entity, ["名称", "状态", "类型", "编号"])
        
        fields = self.entity_field_cache[cache_key]
        return fields[index % len(fields)]
    
    def _build_intent_json(self, operation: str, entity: str, field_name: str, field_type: str) -> Dict[str, Any]:
        """构建意图JSON - 保持简洁"""
        intent = {
            "intentType": operation,
            "targetConceptName": entity
        }
        
        if operation == "ADD_COLUMN":
            intent["props"] = {
                "name": field_name,
                "stdSqlType": field_type,
                "nullable": True
            }
        elif operation == "DELETE_COLUMN":
            intent["props"] = {"name": field_name}
        elif operation == "MODIFY_COLUMN":
            intent["props"] = {
                "name": field_name,
                "stdSqlType": field_type,
                "nullable": False
            }
        
        return intent
    
    def _generate_instruction(self, operation: str, entity: str, field_name: str) -> str:
        """生成指令 - 多样化但简单"""
        templates = {
            "ADD_COLUMN": [
                f"给{entity}表添加{field_name}字段",
                f"为{entity}增加{field_name}属性",
                f"{entity}需要{field_name}字段",
                f"在{entity}中新增{field_name}"
            ],
            "DELETE_COLUMN": [
                f"删除{entity}表的{field_name}字段",
                f"移除{entity}的{field_name}",
                f"去掉{entity}中的{field_name}",
                f"把{entity}的{field_name}删了"
            ],
            "MODIFY_COLUMN": [
                f"修改{entity}表的{field_name}字段",
                f"更新{entity}的{field_name}",
                f"调整{entity}的{field_name}字段",
                f"改变{entity}中{field_name}的类型"
            ]
        }
        
        template_list = templates.get(operation, [f"对{entity}的{field_name}执行{operation}"])
        return self.weighted_sampler.weighted_choice(template_list, [1.0] * len(template_list))


class ParaphraseGenerator(BaseGenerator):
    """改写生成器 - 单一职责：生成多样化表达"""
    
    def __init__(self):
        self.weighted_sampler = WeightedSampler()
        self.strategies = ["formal", "casual", "brief", "technical"]
    
    def generate_from_base(self, base_samples: List[IntentSample], variants_per_sample: int = 3) -> List[IntentSample]:
        """从基础样本生成改写变体"""
        paraphrase_samples = []
        
        # 限制处理数量 - YAGNI原则
        limited_samples = base_samples[:min(50, len(base_samples))]
        
        for base_sample in limited_samples:
            for i in range(variants_per_sample):
                strategy = self.strategies[i % len(self.strategies)]
                
                try:
                    paraphrased = self._apply_strategy(base_sample.instruction, strategy)
                    
                    if paraphrased != base_sample.instruction:
                        sample = IntentSample(
                            intent_json=base_sample.intent_json.copy(),
                            instruction=paraphrased,
                            sample_type="paraphrase",
                            confidence=0.9,
                            metadata={
                                **base_sample.metadata,
                                "base_sample_id": base_sample.hash_id,
                                "paraphrase_strategy": strategy
                            }
                        )
                        paraphrase_samples.append(sample)
                        
                except Exception as e:
                    logger.warning(f"改写失败: {e}")
                    continue
        
        logger.info(f"生成了 {len(paraphrase_samples)} 个改写样本")
        return paraphrase_samples
    
    def _apply_strategy(self, instruction: str, strategy: str) -> str:
        """应用改写策略 - 简单有效"""
        if strategy == "formal":
            # 正式表达
            replacements = [("给", "请为"), ("加", "添加"), ("删", "删除")]
            result = instruction
            for old, new in replacements:
                result = result.replace(old, new)
            return f"请{result}" if not result.startswith("请") else result
            
        elif strategy == "casual":
            # 口语化
            replacements = [("添加", "加"), ("删除", "删"), ("字段", "项")]
            result = instruction
            for old, new in replacements:
                result = result.replace(old, new)
            return f"{result}吧"
            
        elif strategy == "brief":
            # 简洁表达
            result = instruction.replace("请", "").replace("需要", "").strip()
            return result
            
        else:  # technical
            # 技术表达
            replacements = [("字段", "属性"), ("表", "实体"), ("添加", "配置")]
            result = instruction
            for old, new in replacements:
                result = result.replace(old, new)
            return result


class RobustnessGenerator(BaseGenerator):
    """鲁棒性生成器 - 单一职责：生成对抗和边界样本"""
    
    def generate_adversarial(self, base_samples: List[IntentSample], ratio: float = 0.3) -> List[IntentSample]:
        """生成对抗样本"""
        count = max(1, int(len(base_samples) * ratio))
        adversarial_samples = []
        
        selected_samples = base_samples[:count]
        
        for sample in selected_samples:
            # 简单的噪声注入
            noisy_instruction = self._add_noise(sample.instruction)
            
            adversarial_sample = IntentSample(
                intent_json=sample.intent_json.copy(),
                instruction=noisy_instruction,
                sample_type="adversarial",
                confidence=0.7,
                metadata={
                    **sample.metadata,
                    "base_sample_id": sample.hash_id,
                    "perturbation": "noise_injection"
                }
            )
            adversarial_samples.append(adversarial_sample)
        
        logger.info(f"生成了 {len(adversarial_samples)} 个对抗样本")
        return adversarial_samples
    
    def generate_boundary(self, count: int = 30) -> List[IntentSample]:
        """生成边界样本"""
        boundary_patterns = [
            "添加字段", "删除", "修改一下", "给表加",
            "要个字段", "删掉", "改改", "新增",
            "表", "字段", "属性", "加"
        ]
        
        boundary_samples = []
        for i in range(count):
            pattern = boundary_patterns[i % len(boundary_patterns)]
            
            sample = IntentSample(
                intent_json={},
                instruction=pattern,
                sample_type="boundary",
                confidence=0.3,
                metadata={"boundary_type": "incomplete"}
            )
            boundary_samples.append(sample)
        
        logger.info(f"生成了 {len(boundary_samples)} 个边界样本")
        return boundary_samples
    
    def generate_negative(self, count: int = 50) -> List[IntentSample]:
        """生成负样本"""
        negative_patterns = [
            "今天天气怎么样", "吃什么好", "看个电影",
            "打个电话", "发送邮件", "开会议",
            "买东西", "睡觉", "起床了", "听音乐"
        ]
        
        negative_samples = []
        for i in range(count):
            pattern = negative_patterns[i % len(negative_patterns)]
            
            sample = IntentSample(
                intent_json={},
                instruction=pattern,
                sample_type="negative",
                confidence=0.0,
                metadata={"negative_type": "irrelevant"}
            )
            negative_samples.append(sample)
        
        logger.info(f"生成了 {len(negative_samples)} 个负样本")
        return negative_samples
    
    def _add_noise(self, instruction: str) -> str:
        """添加噪声 - 简单有效"""
        noise_words = ["额外的", "可能", "大概", "临时"]
        noise = random.choice(noise_words)
        
        if "字段" in instruction:
            return instruction.replace("字段", f"{noise}字段")
        else:
            return f"{noise}{instruction}"


# ==================== 质量控制器 ====================

class QualityController:
    """质量控制器 - 单一职责：样本质量管理"""
    
    def __init__(self):
        self.seen_hashes = set()
        self.min_quality = 0.3
    
    def validate_and_filter(self, samples: List[IntentSample]) -> List[IntentSample]:
        """验证并过滤样本"""
        valid_samples = []
        
        for sample in samples:
            # 基础验证
            if not self._is_valid_sample(sample):
                continue
            
            # 去重检查
            if sample.hash_id in self.seen_hashes:
                continue
            
            # 质量检查
            if sample.quality_score < self.min_quality:
                continue
            
            self.seen_hashes.add(sample.hash_id)
            valid_samples.append(sample)
        
        logger.info(f"质量控制后保留 {len(valid_samples)}/{len(samples)} 个样本")
        return valid_samples
    
    def _is_valid_sample(self, sample: IntentSample) -> bool:
        """基础样本验证"""
        return (
            sample.instruction.strip() != "" and
            2 <= len(sample.instruction) <= 200 and
            sample.confidence >= 0.0
        )


# ==================== 主要管道类 - 依赖倒置原则 ====================

class EnhancedSamplingPipeline:
    """增强采样管道 - 依赖抽象，便于测试和扩展"""
    
    def __init__(self, config_path: str = "configs/sampling_config.yaml"):
        self.config = self._load_config(config_path)
        self.statistics = SamplingStatistics()
        
        # 依赖注入 - 可以替换实现
        self.intent_generator = IntentDrivenGenerator()
        self.paraphrase_generator = ParaphraseGenerator()
        self.robustness_generator = RobustnessGenerator()
        self.quality_controller = QualityController()
        
        # 可选的增强适配器
        self.enhanced_adapter = None
        if ENHANCED_SAMPLER_AVAILABLE:
            try:
                self.enhanced_adapter = EnhancedSamplingAdapter()
            except Exception as e:
                logger.warning(f"增强适配器初始化失败: {e}")
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置 - 简单可靠"""
        try:
            if Path(config_path).exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    return yaml.safe_load(f)
        except Exception as e:
            logger.warning(f"配置加载失败: {e}")
        
        # 默认配置 - YAGNI原则
        return {
            "base_generation": {"base_samples_count": 100},
            "paraphrase_generation": {"paraphrase_variants": 3},
            "robustness_generation": {
                "adversarial_ratio": 0.2,
                "boundary_samples": 30,
                "negative_samples": 50
            }
        }
    
    def generate_complete_dataset(self) -> List[TrainingSample]:
        """生成完整数据集 - 主要接口"""
        logger.info("🚀 启动增强采样管道")
        start_time = time.time()
        
        try:
            # 步骤1: 生成基础样本
            base_config = self.config.get("base_generation", {})
            base_count = base_config.get("base_samples_count", 100)
            base_samples = self.intent_generator.generate(base_count)
            self._update_stats(base_samples)
            
            # 步骤2: 生成改写样本
            paraphrase_config = self.config.get("paraphrase_generation", {})
            variants = paraphrase_config.get("paraphrase_variants", 3)
            paraphrase_samples = self.paraphrase_generator.generate_from_base(base_samples, variants)
            self._update_stats(paraphrase_samples)
            
            # 步骤3: 生成鲁棒性样本
            robustness_config = self.config.get("robustness_generation", {})
            adversarial_samples = self.robustness_generator.generate_adversarial(
                base_samples, robustness_config.get("adversarial_ratio", 0.2)
            )
            boundary_samples = self.robustness_generator.generate_boundary(
                robustness_config.get("boundary_samples", 30)
            )
            negative_samples = self.robustness_generator.generate_negative(
                robustness_config.get("negative_samples", 50)
            )
            
            self._update_stats(adversarial_samples + boundary_samples + negative_samples)
            
            # 步骤4: 质量控制
            all_samples = base_samples + paraphrase_samples + adversarial_samples + boundary_samples + negative_samples
            filtered_samples = self.quality_controller.validate_and_filter(all_samples)
            
            # 步骤5: 转换格式
            training_samples = [sample.to_training_sample() for sample in filtered_samples]
            
            # 记录统计
            self.statistics.generation_time = time.time() - start_time
            self._log_final_stats(training_samples)
            
            logger.info(f"✅ 管道完成！生成 {len(training_samples)} 个训练样本")
            return training_samples
            
        except Exception as e:
            logger.error(f"❌ 管道执行失败: {e}")
            self.statistics.add_error()
            raise
    
    def _update_stats(self, samples: List[IntentSample]):
        """更新统计信息"""
        for sample in samples:
            self.statistics.add_sample(sample)
    
    def _log_final_stats(self, training_samples: List[TrainingSample]):
        """记录最终统计"""
        stats = self.statistics.get_summary()
        logger.info("📊 最终统计:")
        logger.info(f"  总样本数: {stats['total_samples']}")
        logger.info(f"  训练样本: {len(training_samples)}")
        logger.info(f"  生成时间: {stats['generation_time']:.2f}s")
        logger.info(f"  样本分布: {stats['samples_by_type']}")
        logger.info(f"  成功率: {stats['success_rate']:.2%}")
    
    def get_pipeline_info(self) -> Dict[str, Any]:
        """获取管道信息"""
        return {
            "config": self.config,
            "statistics": self.statistics.get_summary(),
            "components": {
                "intent_generator": type(self.intent_generator).__name__,
                "paraphrase_generator": type(self.paraphrase_generator).__name__,
                "robustness_generator": type(self.robustness_generator).__name__,
                "quality_controller": type(self.quality_controller).__name__,
                "enhanced_adapter": type(self.enhanced_adapter).__name__ if self.enhanced_adapter else None
            }
        }


# ==================== 便捷接口函数 ====================

def create_enhanced_pipeline(config_path: str = "configs/sampling_config.yaml") -> EnhancedSamplingPipeline:
    """创建增强采样管道"""
    return EnhancedSamplingPipeline(config_path)


def generate_enhanced_dataset(config_path: str = "configs/sampling_config.yaml") -> List[TrainingSample]:
    """一键生成增强数据集"""
    pipeline = create_enhanced_pipeline(config_path)
    return pipeline.generate_complete_dataset()


def run_quick_sampling(base_count: int = 50, output_path: Optional[str] = None) -> List[TrainingSample]:
    """快速采样 - 适合测试"""
    # 创建临时配置
    quick_config = {
        "base_generation": {"base_samples_count": base_count},
        "paraphrase_generation": {"paraphrase_variants": 2},
        "robustness_generation": {
            "adversarial_ratio": 0.2,
            "boundary_samples": 10,
            "negative_samples": 20
        }
    }
    
    # 临时配置文件
    temp_config_path = f"temp_config_{int(time.time())}.yaml"
    try:
        with open(temp_config_path, 'w', encoding='utf-8') as f:
            yaml.dump(quick_config, f, allow_unicode=True)
        
        pipeline = EnhancedSamplingPipeline(temp_config_path)
        samples = pipeline.generate_complete_dataset()
        
        # 导出结果
        if output_path:
            export_data = [
                {
                    "instruction": sample.instruction,
                    "output": [intent.__dict__ for intent in sample.output],
                    "metadata": sample.metadata
                }
                for sample in samples
            ]
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            logger.info(f"结果已导出到: {output_path}")
        
        return samples
        
    finally:
        # 清理临时文件
        Path(temp_config_path).unlink(missing_ok=True)


# ==================== 测试函数 ====================

def test_pipeline():
    """测试管道功能"""
    logger.info("🧪 开始测试增强采样管道...")
    
    try:
        # 测试基础功能
        pipeline = EnhancedSamplingPipeline()
        samples = pipeline.generate_complete_dataset()
        
        # 基本断言
        assert len(samples) > 0, "应该生成至少一个样本"
        assert isinstance(samples[0], TrainingSample), "样本类型应该正确"
        
        # 检查样本多样性
        sample_types = set()
        for sample in samples:
            sample_type = sample.metadata.get("sample_type", "unknown")
            sample_types.add(sample_type)
        
        logger.info(f"✅ 测试通过！生成 {len(samples)} 个样本")
        logger.info(f"样本类型: {sample_types}")
        
        # 获取管道信息
        info = pipeline.get_pipeline_info()
        logger.info(f"管道信息: {info['components']}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        return False


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "test":
            success = test_pipeline()
            sys.exit(0 if success else 1)
            
        elif command == "quick":
            count = int(sys.argv[2]) if len(sys.argv) > 2 else 50
            output = sys.argv[3] if len(sys.argv) > 3 else None
            samples = run_quick_sampling(count, output)
            print(f"✅ 快速生成完成：{len(samples)} 个样本")
            
        elif command == "generate":
            config = sys.argv[2] if len(sys.argv) > 2 else "configs/sampling_config.yaml"
            samples = generate_enhanced_dataset(config)
            print(f"✅ 数据集生成完成：{len(samples)} 个样本")
            
        else:
            print("可用命令: test, quick [count] [output], generate [config]")
    else:
        # 默认运行测试
        print("🧪 运行默认测试...")
        test_pipeline()