"""
多模型互评系统
支持千问和豆包模型的互相评估和验证
"""

import os
import json
import asyncio
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path
import requests
import time
from loguru import logger

# 加载环境变量
from dotenv import load_dotenv
load_dotenv("configs/api_keys.env")


@dataclass
class ModelConfig:
    """模型配置"""
    name: str
    api_key: str
    api_endpoint: str
    model_name: str
    headers: Dict[str, str]
    timeout: int = 30
    max_retries: int = 3


@dataclass
class EvaluationResult:
    """评估结果"""
    model_name: str
    score: float
    reasoning: str
    criteria_scores: Dict[str, float]
    confidence: float
    timestamp: float


class MultiModelEvaluator:
    """多模型互评器"""
    
    def __init__(self):
        self.qwen_config = self._create_qwen_config()
        self.doubao_config = self._create_doubao_config()
        
    def _create_qwen_config(self) -> ModelConfig:
        """创建千问模型配置"""
        return ModelConfig(
            name="qwen",
            api_key=os.getenv("QWEN_API_KEY", "sk-44d0bacaf21a43b6996ae738debbf03b"),
            api_endpoint=os.getenv("QWEN_API_ENDPOINT", "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"),
            model_name=os.getenv("QWEN_MODEL_NAME", "qwen-turbo"),
            headers={
                "Authorization": f"Bearer {os.getenv('QWEN_API_KEY', 'sk-44d0bacaf21a43b6996ae738debbf03b')}",
                "Content-Type": "application/json",
                "X-DashScope-SSE": "disable"
            }
        )
    
    def _create_doubao_config(self) -> ModelConfig:
        """创建豆包模型配置"""
        return ModelConfig(
            name="doubao",
            api_key=os.getenv("DOUBAO_API_KEY", "37af5e54-2890-4e4b-aea7-bc7e6cb8fa62"),
            api_endpoint=os.getenv("DOUBAO_API_ENDPOINT", "https://ark.cn-beijing.volces.com/api/v3/chat/completions"),
            model_name=os.getenv("DOUBAO_MODEL_NAME", "Doubao-Seed-1.6"),
            headers={
                "Authorization": f"Bearer {os.getenv('DOUBAO_API_KEY', '37af5e54-2890-4e4b-aea7-bc7e6cb8fa62')}",
                "Content-Type": "application/json"
            }
        )
    
    async def call_qwen_api(self, prompt: str, **kwargs) -> str:
        """调用千问API"""
        payload = {
            "model": self.qwen_config.model_name,
            "input": {
                "prompt": prompt
            },
            "parameters": {
                "max_tokens": kwargs.get("max_tokens", 512),
                "temperature": kwargs.get("temperature", 0.7),
                "top_p": kwargs.get("top_p", 0.9)
            }
        }
        
        try:
            response = requests.post(
                self.qwen_config.api_endpoint,
                headers=self.qwen_config.headers,
                json=payload,
                timeout=self.qwen_config.timeout
            )
            response.raise_for_status()
            
            result = response.json()
            if "output" in result and "text" in result["output"]:
                return result["output"]["text"].strip()
            else:
                logger.error(f"千问API响应格式异常: {result}")
                return ""
                
        except Exception as e:
            logger.error(f"千问API调用失败: {e}")
            return ""
    
    async def call_doubao_api(self, prompt: str, **kwargs) -> str:
        """调用豆包API"""
        payload = {
            "model": self.doubao_config.model_name,
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "max_tokens": kwargs.get("max_tokens", 512),
            "temperature": kwargs.get("temperature", 0.7),
            "top_p": kwargs.get("top_p", 0.9)
        }
        
        try:
            response = requests.post(
                self.doubao_config.api_endpoint,
                headers=self.doubao_config.headers,
                json=payload,
                timeout=self.doubao_config.timeout
            )
            response.raise_for_status()
            
            result = response.json()
            if "choices" in result and len(result["choices"]) > 0:
                return result["choices"][0]["message"]["content"].strip()
            else:
                logger.error(f"豆包API响应格式异常: {result}")
                return ""
                
        except Exception as e:
            logger.error(f"豆包API调用失败: {e}")
            return ""
    
    def create_evaluation_prompt(self, instruction: str, intent_json: str, criteria: str) -> str:
        """创建评估提示词"""
        return f"""
请评估以下自然语言指令与JSON意图的匹配质量：

自然语言指令：{instruction}

JSON意图：{intent_json}

评估标准：{criteria}

请按以下格式输出评估结果：
{{
    "score": 评分(1-5),
    "reasoning": "详细评估理由",
    "confidence": 置信度(0-1)
}}

请确保输出是有效的JSON格式。
"""
    
    async def evaluate_with_model(self, model_config: ModelConfig, instruction: str, intent_json: str, criteria: str) -> EvaluationResult:
        """使用指定模型进行评估"""
        prompt = self.create_evaluation_prompt(instruction, intent_json, criteria)
        
        if model_config.name == "qwen":
            response = await self.call_qwen_api(prompt, temperature=0.3)
        elif model_config.name == "doubao":
            response = await self.call_doubao_api(prompt, temperature=0.3)
        else:
            raise ValueError(f"不支持的模型: {model_config.name}")
        
        try:
            # 尝试解析JSON响应
            result = json.loads(response)
            return EvaluationResult(
                model_name=model_config.name,
                score=float(result.get("score", 0)),
                reasoning=result.get("reasoning", ""),
                criteria_scores={criteria: float(result.get("score", 0))},
                confidence=float(result.get("confidence", 0.5)),
                timestamp=time.time()
            )
        except json.JSONDecodeError:
            logger.warning(f"模型{model_config.name}返回非JSON格式，尝试解析文本")
            # 简单的文本解析逻辑
            score = 3.0  # 默认分数
            if "优秀" in response or "excellent" in response.lower():
                score = 5.0
            elif "良好" in response or "good" in response.lower():
                score = 4.0
            elif "差" in response or "poor" in response.lower():
                score = 2.0
            elif "很差" in response or "bad" in response.lower():
                score = 1.0
            
            return EvaluationResult(
                model_name=model_config.name,
                score=score,
                reasoning=response,
                criteria_scores={criteria: score},
                confidence=0.3,
                timestamp=time.time()
            )
    
    async def cross_evaluate(self, instruction: str, intent_json: str) -> Dict[str, EvaluationResult]:
        """交叉评估：两个模型互相评估"""
        criteria_list = [
            "语义一致性：指令与意图的语义是否完全一致",
            "表达自然度：指令是否符合自然语言习惯",
            "歧义程度：指令是否存在歧义或模糊表达"
        ]
        
        results = {}
        
        for criteria in criteria_list:
            # 千问评估
            qwen_result = await self.evaluate_with_model(
                self.qwen_config, instruction, intent_json, criteria
            )
            results[f"qwen_{criteria[:4]}"] = qwen_result
            
            # 豆包评估
            doubao_result = await self.evaluate_with_model(
                self.doubao_config, instruction, intent_json, criteria
            )
            results[f"doubao_{criteria[:4]}"] = doubao_result
            
            # 避免API限制
            await asyncio.sleep(0.5)
        
        return results
    
    def calculate_consensus_score(self, results: Dict[str, EvaluationResult]) -> float:
        """计算共识分数"""
        scores = [result.score for result in results.values()]
        if not scores:
            return 0.0
        
        # 计算平均分和一致性
        avg_score = sum(scores) / len(scores)
        variance = sum((score - avg_score) ** 2 for score in scores) / len(scores)
        consistency = max(0, 1 - variance)  # 方差越小，一致性越高
        
        return avg_score * consistency


# 测试函数
async def test_multi_model_evaluator():
    """测试多模型评估器"""
    evaluator = MultiModelEvaluator()
    
    # 测试样例
    instruction = "给客户表添加一个等级字段，类型为整型"
    intent_json = '{"intentType": "ADD_COLUMN", "targetConceptName": "客户", "props": {"name": "等级", "stdSqlType": "INT", "nullable": true}}'
    
    logger.info("开始多模型交叉评估测试...")
    
    try:
        results = await evaluator.cross_evaluate(instruction, intent_json)
        
        logger.info("评估结果:")
        for key, result in results.items():
            logger.info(f"{key}: 分数={result.score}, 置信度={result.confidence}")
            logger.info(f"  理由: {result.reasoning[:100]}...")
        
        consensus_score = evaluator.calculate_consensus_score(results)
        logger.info(f"共识分数: {consensus_score:.2f}")
        
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        return False


if __name__ == "__main__":
    asyncio.run(test_multi_model_evaluator())
