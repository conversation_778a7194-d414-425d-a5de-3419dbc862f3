"""
反向数据生成器：从JSON意图生成自然语言指令
支持配置驱动、多策略生成和语义分层
"""

import random
import json
import os
from typing import List, Dict, Any, Optional
from loguru import logger

from src.models.intent_models import TrainingSample, Intent
from src.utils.config import config_manager

class ReverseDataGenerator:
    """反向数据生成器：从结构化意图生成自然语言指令"""
    
    def __init__(self):
        self.config_manager = config_manager
        self.templates = self._load_intent_templates()
        self.entities = self._load_entities()
        self.field_types = self._load_field_types()
        self.business_terms = self._load_business_terms()
        self.expressions = self._load_expressions()
        
        # 语义策略权重（基于配置文件）
        self.semantic_strategies = {
            "atomic": {"weight": 35, "description": "原子操作"},
            "composite": {"weight": 30, "description": "复合操作"}, 
            "sequence": {"weight": 20, "description": "序列操作"},
            "implicit": {"weight": 15, "description": "隐含操作"}
        }
        
        logger.info(f"反向数据生成器初始化完成")
        logger.info(f"  - 实体数量: {len(self.entities)}")
        logger.info(f"  - 字段类型数量: {len(self.field_types)}")
        logger.info(f"  - 意图模板数量: {len(self.templates)}")
        
    def _load_intent_templates(self) -> Dict[str, Any]:
        """加载意图模板"""
        templates_path = "data/templates/intent_templates.json"
        try:
            if os.path.exists(templates_path):
                with open(templates_path, 'r', encoding='utf-8') as f:
                    templates = json.load(f)
                    logger.info(f"从文件加载意图模板: {templates_path}")
                    return templates
            else:
                logger.warning(f"意图模板文件不存在: {templates_path}，使用默认模板")
                return self._get_default_templates()
        except Exception as e:
            logger.error(f"加载意图模板失败: {e}，使用默认模板")
            return self._get_default_templates()
    
    def _get_default_templates(self) -> Dict[str, Any]:
        """获取默认的意图模板"""
        return {
            "ADD_COLUMN": {
                "template": {
                    "intentType": "ADD_COLUMN",
                    "targetConceptName": "{entity}",
                    "props": {
                        "name": "{field_name}",
                        "stdSqlType": "{data_type}",
                        "nullable": "{nullable}",
                        "comment": "{comment}"
                    }
                },
                "entities": ["客户", "订单", "产品", "用户"],
                "field_names": ["等级", "状态", "类型", "编号", "名称", "描述"],
                "data_types": ["VARCHAR(50)", "INT", "DECIMAL(10,2)", "DATETIME", "TEXT", "BOOLEAN"],
                "expressions": [
                    "给{entity}表添加一个{field_name}字段",
                    "为{entity}增加{field_name}属性", 
                    "{entity}需要一个{field_name}字段",
                    "在{entity}表中加入{field_name}",
                    "添加{field_name}到{entity}表",
                    "{entity}表要有{field_name}字段",
                    "给{entity}加个{field_name}",
                    "新增{entity}的{field_name}属性"
                ],
                "fuzzy_expressions": [
                    "给{entity}加字段",
                    "{entity}要加个字段",
                    "添加字段到{entity}",
                    "新增字段"
                ]
            },
            "DELETE_COLUMN": {
                "template": {
                    "intentType": "DELETE_COLUMN",
                    "targetConceptName": "{entity}",
                    "props": {
                        "name": "{field_name}"
                    }
                },
                "entities": ["客户", "订单", "产品", "用户"],
                "field_names": ["等级", "状态", "类型", "编号", "名称", "描述"],
                "expressions": [
                    "删除{entity}表的{field_name}字段",
                    "移除{entity}的{field_name}属性",
                    "{entity}不需要{field_name}字段了",
                    "去掉{entity}表中的{field_name}",
                    "把{entity}的{field_name}删了",
                    "取消{entity}的{field_name}字段"
                ],
                "fuzzy_expressions": [
                    "删除{entity}的字段",
                    "移除{entity}字段",
                    "{entity}删字段"
                ]
            },
            "MODIFY_COLUMN": {
                "template": {
                    "intentType": "MODIFY_COLUMN",
                    "targetConceptName": "{entity}",
                    "props": {
                        "name": "{field_name}",
                        "newName": "{new_field_name}",
                        "stdSqlType": "{data_type}",
                        "nullable": "{nullable}"
                    }
                },
                "entities": ["客户", "订单", "产品", "用户"],
                "field_names": ["等级", "状态", "类型", "编号", "名称", "描述"],
                "new_field_names": ["客户等级", "订单状态", "产品类型", "用户编号", "客户名称", "产品描述"],
                "data_types": ["VARCHAR(100)", "INT", "DECIMAL(15,2)", "TEXT"],
                "expressions": [
                    "修改{entity}表的{field_name}字段类型为{data_type}",
                    "将{entity}的{field_name}改为{new_field_name}",
                    "更新{entity}表{field_name}字段",
                    "{entity}的{field_name}字段要改成{data_type}类型"
                ]
            },
            "ENABLE_SOFT_DELETE": {
                "template": {
                    "intentType": "ENABLE_SOFT_DELETE",
                    "targetConceptName": "{entity}",
                    "props": {
                        "deleteField": "is_deleted",
                        "deleteFieldType": "BOOLEAN"
                    }
                },
                "entities": ["客户", "订单", "产品", "用户"],
                "expressions": [
                    "启用{entity}表的逻辑删除",
                    "{entity}表需要软删除功能",
                    "给{entity}添加逻辑删除",
                    "{entity}要支持软删除",
                    "开启{entity}的逻辑删除"
                ]
            }
        }
    
    def _load_entities(self) -> List[str]:
        """加载实体列表"""
        try:
            entities = self.config_manager.get_orm_entities()
            if entities:
                logger.info(f"从配置加载实体列表: {len(entities)}个")
                return entities
        except Exception as e:
            logger.warning(f"从配置加载实体失败: {e}")
        
        # 默认实体列表
        default_entities = ["客户", "订单", "产品", "用户", "供应商", "员工", "部门", "项目"]
        logger.info(f"使用默认实体列表: {len(default_entities)}个")
        return default_entities
    
    def _load_field_types(self) -> List[str]:
        """加载字段类型"""
        try:
            field_types = self.config_manager.get_orm_field_types()
            if field_types:
                logger.info(f"从配置加载字段类型: {len(field_types)}个")
                return field_types
        except Exception as e:
            logger.warning(f"从配置加载字段类型失败: {e}")
        
        # 默认字段类型
        default_types = [
            "VARCHAR(50)", "VARCHAR(100)", "VARCHAR(255)", "TEXT", 
            "INT", "BIGINT", "DECIMAL(10,2)", "FLOAT", "DOUBLE",
            "DATE", "DATETIME", "TIMESTAMP", "BOOLEAN"
        ]
        logger.info(f"使用默认字段类型: {len(default_types)}个")
        return default_types
    
    def _load_business_terms(self) -> Dict[str, Any]:
        """加载业务术语"""
        business_terms_path = "data/templates/business_terms.json"
        try:
            if os.path.exists(business_terms_path):
                with open(business_terms_path, 'r', encoding='utf-8') as f:
                    terms = json.load(f)
                    logger.info(f"加载业务术语成功: {business_terms_path}")
                    return terms
        except Exception as e:
            logger.warning(f"加载业务术语失败: {e}")
        
        # 默认业务术语
        return {
            "action_words": {
                "添加": ["添加", "增加", "新增", "加入", "创建", "建立"],
                "删除": ["删除", "移除", "去掉", "取消", "清除"],
                "修改": ["修改", "更改", "变更", "调整", "更新"],
                "关联": ["关联", "连接", "绑定", "链接", "关系"]
            },
            "common_fields": {
                "标识类": ["ID", "编号", "代码", "序号"],
                "名称类": ["名称", "姓名", "标题", "title"],
                "状态类": ["状态", "状况", "情况", "condition"],
                "时间类": ["时间", "日期", "创建时间", "更新时间"],
                "描述类": ["描述", "说明", "备注", "注释"]
            }
        }
    
    def _load_expressions(self) -> Dict[str, List[str]]:
        """加载表达方式配置"""
        return {
            "field_names": [
                "等级", "状态", "类型", "编号", "名称", "描述", "备注", 
                "创建时间", "更新时间", "价格", "数量", "总额", "标题",
                "代码", "序号", "分类", "标签", "权限", "角色"
            ],
            "modifiers": {
                "prefixes": ["请", "麻烦", "需要", "想要", "希望", "能否"],
                "suffixes": ["谢谢", "请确认", "尽快处理", "多谢", "辛苦了"]
            },
            "styles": {
                "formal": ["请为", "烦请", "恳请"],
                "casual": ["帮忙", "搞个", "弄个"],
                "technical": ["配置", "设置", "定义"]
            }
        }
    
    def generate_single_intent_samples(self, intent_type: str, count: int) -> List[TrainingSample]:
        """生成单个意图类型的样本"""
        logger.info(f"开始生成 {intent_type} 类型样本，目标数量: {count}")
        
        if intent_type not in self.templates:
            logger.error(f"未找到意图类型模板: {intent_type}")
            return []
        
        template_config = self.templates[intent_type]
        samples = []
        
        # 按语义策略分配样本数量
        strategy_distribution = self._distribute_by_semantic_strategy(count)
        
        for strategy, strategy_count in strategy_distribution.items():
            logger.info(f"  生成 {strategy} 策略样本: {strategy_count}个")
            
            for i in range(strategy_count):
                try:
                    sample = self._generate_single_sample(
                        intent_type, template_config, strategy, i
                    )
                    if sample:
                        samples.append(sample)
                        
                except Exception as e:
                    logger.warning(f"生成第{i}个{strategy}样本失败: {e}")
                    continue
        
        logger.info(f"成功生成 {len(samples)} 个 {intent_type} 样本")
        return samples
    
    def _distribute_by_semantic_strategy(self, total_count: int) -> Dict[str, int]:
        """按语义策略分配样本数量"""
        distribution = {}
        allocated = 0
        
        strategies = list(self.semantic_strategies.items())
        for i, (strategy, config) in enumerate(strategies):
            if i == len(strategies) - 1:  # 最后一个策略分配剩余所有
                distribution[strategy] = total_count - allocated
            else:
                weight = config["weight"] / 100.0  # 转换为比例
                count = int(total_count * weight)
                distribution[strategy] = count
                allocated += count
        
        return distribution
    
    def _generate_single_sample(self, intent_type: str, template_config: Dict[str, Any], 
                               strategy: str, sample_index: int) -> Optional[TrainingSample]:
        """生成单个训练样本"""
        
        # 随机选择参数
        entity = random.choice(template_config.get("entities", self.entities))
        field_name = random.choice(template_config.get("field_names", self.expressions["field_names"]))
        data_type = random.choice(template_config.get("data_types", self.field_types))
        
        # 为 MODIFY_COLUMN 选择新字段名
        if intent_type == "MODIFY_COLUMN":
            new_field_names = template_config.get("new_field_names", [f"新{field_name}"])
            new_field_name = random.choice(new_field_names)
        else:
            new_field_name = f"新{field_name}"  # 默认值
        
        # 生成指令文本
        instruction = self._generate_instruction_text(
            intent_type, template_config, entity, field_name, data_type, strategy, new_field_name
        )
        
        # 生成意图结构
        intent = self._generate_intent_structure(
            intent_type, template_config, entity, field_name, data_type, new_field_name
        )
        
        # 创建训练样本
        sample = TrainingSample(
            instruction=instruction,
            output=[intent] if intent else [],
            metadata={
                "intent_type": intent_type,
                "entity": entity,
                "field_name": field_name,
                "data_type": data_type,
                "new_field_name": new_field_name,
                "semantic_strategy": strategy,
                "generation_method": "template_based",
                "sample_index": sample_index,
                "template_version": "1.0"
            }
        )
        
        return sample
    
    def _generate_instruction_text(self, intent_type: str, template_config: Dict[str, Any],
                                 entity: str, field_name: str, data_type: str, 
                                 strategy: str, new_field_name: str = None) -> str:
        """生成指令文本"""
        
        expressions = template_config.get("expressions", [])
        if not expressions:
            return f"对{entity}执行{intent_type}操作"
        
        # 根据策略选择表达方式
        if strategy == "atomic":
            # 原子策略：使用最直接的表达
            expression = random.choice(expressions[:len(expressions)//2])
        elif strategy == "composite":
            # 复合策略：可能包含多个操作描述
            expression = random.choice(expressions)
            if random.random() < 0.3:  # 30%概率添加额外描述
                expression += f"，类型为{data_type}"
        elif strategy == "sequence":
            # 序列策略：可能包含步骤性描述
            expression = random.choice(expressions)
            if random.random() < 0.4:  # 40%概率添加序列词
                prefixes = ["首先", "然后", "接下来", "最后"]
                expression = random.choice(prefixes) + expression
        else:  # implicit
            # 隐含策略：更口语化，可能省略部分信息
            if random.random() < 0.5:
                # 使用模糊表达
                fuzzy_expressions = template_config.get("fuzzy_expressions", expressions)
                expression = random.choice(fuzzy_expressions) if fuzzy_expressions else expressions[0]
            else:
                expression = random.choice(expressions)
        
        # 应用表达变化
        instruction = self._apply_expression_variations(
            expression, entity, field_name, data_type, new_field_name
        )
        
        return instruction
    
    def _apply_expression_variations(self, expression: str, entity: str, 
                                   field_name: str, data_type: str, new_field_name: str = None) -> str:
        """应用表达变化 - 修复版本"""
        
        # 准备所有可能需要的变量
        format_vars = {
            'entity': entity,
            'field_name': field_name,
            'data_type': data_type,
            'new_field_name': new_field_name or f"新{field_name}",
            'nullable': random.choice(['true', 'false']),
            'comment': f"{field_name}字段"
        }
        
        try:
            # 安全的模板格式化
            instruction = expression.format(**format_vars)
        except KeyError as e:
            # 如果仍有缺失变量，使用降级策略
            missing_var = str(e).strip("'")
            format_vars[missing_var] = f"[{missing_var}]"
            instruction = expression.format(**format_vars)
            logger.warning(f"使用默认值替换缺失变量: {missing_var}")
        
        # 随机应用修饰词
        if random.random() < 0.3:  # 30%概率添加前缀
            prefixes = self.expressions["modifiers"]["prefixes"]
            prefix = random.choice(prefixes)
            instruction = prefix + instruction
        
        if random.random() < 0.2:  # 20%概率添加后缀
            suffixes = self.expressions["modifiers"]["suffixes"] 
            suffix = random.choice(suffixes)
            instruction = instruction + "，" + suffix
        
        # 随机应用同义词替换
        if random.random() < 0.4:  # 40%概率进行同义词替换
            instruction = self._apply_synonym_replacement(instruction)
        
        return instruction
    
    def _apply_synonym_replacement(self, text: str) -> str:
        """应用同义词替换"""
        action_words = self.business_terms.get("action_words", {})
        
        for original, synonyms in action_words.items():
            if original in text and random.random() < 0.5:
                synonym = random.choice(synonyms)
                text = text.replace(original, synonym, 1)  # 只替换第一个
                break
        
        return text
    
    def _generate_intent_structure(self, intent_type: str, template_config: Dict[str, Any],
                                 entity: str, field_name: str, data_type: str, 
                                 new_field_name: str = None) -> Optional[Intent]:
        """生成意图结构"""
        
        try:
            template = template_config.get("template", {})
            
            intent_data = {
                "intentType": intent_type,
                "targetConceptName": entity,
                "props": {}
            }
            
            # 根据意图类型填充属性
            if intent_type == "ADD_COLUMN":
                intent_data["props"] = {
                    "name": field_name,
                    "stdSqlType": data_type,
                    "nullable": random.choice([True, False]),
                    "comment": f"{field_name}字段"
                }
            elif intent_type == "DELETE_COLUMN":
                intent_data["props"] = {
                    "name": field_name
                }
            elif intent_type == "MODIFY_COLUMN":
                intent_data["props"] = {
                    "name": field_name,
                    "newName": new_field_name or f"新{field_name}",
                    "stdSqlType": data_type,
                    "nullable": random.choice([True, False])
                }
            elif intent_type == "ENABLE_SOFT_DELETE":
                intent_data["props"] = {
                    "deleteField": "is_deleted",
                    "deleteFieldType": "BOOLEAN"
                }
            
            return Intent(**intent_data)
            
        except Exception as e:
            logger.warning(f"生成意图结构失败: {e}")
            return None
    
    def generate_fuzzy_samples(self, count: int) -> List[TrainingSample]:
        """生成模糊样本（需要澄清的样本）"""
        logger.info(f"开始生成模糊样本，目标数量: {count}")
        
        fuzzy_templates = [
            "加字段",
            "删除字段",
            "修改表", 
            "添加属性",
            "创建关系",
            "设置主键",
            "建立索引",
            "配置权限",
            "设置约束",
            "优化性能"
        ]
        
        samples = []
        
        for i in range(count):
            try:
                # 随机选择模糊模板
                base_instruction = random.choice(fuzzy_templates)
                
                # 随机决定是否添加部分上下文
                if random.random() < 0.4:  # 40%概率添加实体信息
                    entity = random.choice(self.entities)
                    instruction = base_instruction.replace("表", f"{entity}表")
                else:
                    instruction = base_instruction
                
                # 随机添加修饰词
                if random.random() < 0.3:
                    prefixes = ["请", "帮忙", "需要"]
                    instruction = random.choice(prefixes) + instruction
                
                # 创建模糊样本
                sample = TrainingSample(
                    instruction=instruction,
                    output=[],  # 空的意图列表，表示需要澄清
                    metadata={
                        "intent_type": "CLARIFICATION_NEEDED",
                        "generation_method": "fuzzy",
                        "sample_index": i,
                        "is_fuzzy": True,
                        "base_template": base_instruction,
                        "potential_intent": self._guess_potential_intent(instruction)
                    }
                )
                
                samples.append(sample)
                
            except Exception as e:
                logger.warning(f"生成第{i}个模糊样本失败: {e}")
                continue
        
        logger.info(f"成功生成 {len(samples)} 个模糊样本")
        return samples
    
    def _guess_potential_intent(self, instruction: str) -> str:
        """猜测模糊指令的潜在意图"""
        instruction_lower = instruction.lower()
        
        if any(word in instruction_lower for word in ["加", "添加", "增加", "新增"]):
            return "ADD_COLUMN"
        elif any(word in instruction_lower for word in ["删", "删除", "移除", "去掉"]):
            return "DELETE_COLUMN"
        elif any(word in instruction_lower for word in ["改", "修改", "更新", "调整"]):
            return "MODIFY_COLUMN"
        elif any(word in instruction_lower for word in ["关系", "关联", "连接"]):
            return "ADD_RELATIONSHIP"
        else:
            return "UNKNOWN"
    
    async def generate_training_dataset(self, samples_per_intent: int) -> List[TrainingSample]:
        """生成完整的训练数据集"""
        logger.info(f"开始生成完整训练数据集，每种意图 {samples_per_intent} 个样本")
        
        all_samples = []
        
        # 为每种意图类型生成样本
        for intent_type in self.templates.keys():
            logger.info(f"正在生成 {intent_type} 类型样本...")
            samples = self.generate_single_intent_samples(intent_type, samples_per_intent)
            all_samples.extend(samples)
        
        logger.info(f"训练数据集生成完成，总样本数: {len(all_samples)}")
        
        # 生成统计信息
        intent_stats = {}
        for sample in all_samples:
            intent_type = sample.metadata.get("intent_type", "UNKNOWN")
            intent_stats[intent_type] = intent_stats.get(intent_type, 0) + 1
        
        logger.info("样本分布统计:")
        for intent_type, count in intent_stats.items():
            logger.info(f"  {intent_type}: {count} 个样本")
        
        return all_samples
    
    def get_generation_statistics(self) -> Dict[str, Any]:
        """获取生成器统计信息"""
        return {
            "templates_count": len(self.templates),
            "entities_count": len(self.entities),
            "field_types_count": len(self.field_types),
            "supported_intent_types": list(self.templates.keys()),
            "semantic_strategies": self.semantic_strategies,
            "business_terms_loaded": len(self.business_terms) > 0
        }